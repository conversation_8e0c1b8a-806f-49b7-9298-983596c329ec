19:36:02.329 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:36:03.404 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0
19:36:03.498 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 53 ms to scan 1 urls, producing 3 keys and 6 values 
19:36:03.536 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:36:03.554 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
19:36:03.742 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 184 ms to scan 248 urls, producing 0 keys and 0 values 
19:36:03.753 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
19:36:03.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
19:36:03.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
19:36:03.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 179 ms to scan 248 urls, producing 0 keys and 0 values 
19:36:03.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:36:03.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/925152318
19:36:03.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/2011912080
19:36:03.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:36:03.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:36:03.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:36:05.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750332965490_127.0.0.1_52893
19:36:05.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Notify connected event to listeners.
19:36:05.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:36:05.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba50136b-29d5-4c7d-b7ab-4ecc441e3a3e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1674550752
19:36:05.894 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:36:09.612 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
19:36:09.613 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:36:09.613 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:36:09.898 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:36:13.181 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
19:37:26.914 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:37:27.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 51f62637-1937-4975-a76c-183dd118eb69_config-0
19:37:27.982 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
19:37:28.015 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
19:37:28.027 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
19:37:28.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 181 ms to scan 248 urls, producing 0 keys and 0 values 
19:37:28.222 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
19:37:28.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
19:37:28.247 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
19:37:28.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 200 ms to scan 248 urls, producing 0 keys and 0 values 
19:37:28.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:37:28.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/925152318
19:37:28.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/2011912080
19:37:28.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:37:28.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:37:28.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:37:30.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750333049770_127.0.0.1_53209
19:37:30.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Notify connected event to listeners.
19:37:30.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:37:30.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f62637-1937-4975-a76c-183dd118eb69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1498259207
19:37:30.170 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:37:33.231 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
19:37:33.232 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:37:33.232 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:37:33.531 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:37:35.718 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
19:38:48.667 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:38:49.620 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0
19:38:49.703 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
19:38:49.735 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:38:49.745 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
19:38:49.925 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 175 ms to scan 248 urls, producing 0 keys and 0 values 
19:38:49.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
19:38:49.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
19:38:49.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
19:38:50.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 180 ms to scan 248 urls, producing 0 keys and 0 values 
19:38:50.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:38:50.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1917149817
19:38:50.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/1403649277
19:38:50.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:38:50.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:38:50.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:38:51.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750333131438_127.0.0.1_53676
19:38:51.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Notify connected event to listeners.
19:38:51.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:38:51.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0b295b6-5c8a-410c-a8f0-9be066584bec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/430050226
19:38:51.871 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:38:55.114 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
19:38:55.114 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:38:55.115 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:38:55.418 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:38:59.308 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:39:00.012 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 94e78c66-f784-4217-93bd-dbeb32244908
19:39:00.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] RpcClient init label, labels = {module=naming, source=sdk}
19:39:00.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:39:00.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:39:00.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:39:00.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:39:00.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750333140024_127.0.0.1_53719
19:39:00.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Notify connected event to listeners.
19:39:00.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:39:00.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/430050226
19:39:03.203 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9704"]
19:39:03.253 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-biz-shop 192.168.0.68:9704 register finished
19:39:03.754 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Receive server push request, request = NotifySubscriberRequest, requestId = 248
19:39:03.761 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Ack server push request, request = NotifySubscriberRequest, requestId = 248
19:39:04.140 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStarted,61] - Started RuoyiBizShopApplication in 16.241 seconds (JVM running for 17.667)
19:39:04.171 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop-dev.yaml, group=DEFAULT_GROUP
19:39:04.172 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop.yaml, group=DEFAULT_GROUP
19:39:04.172 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop, group=DEFAULT_GROUP
19:39:04.558 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:47:18.713 [nacos-grpc-client-executor-111] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Receive server push request, request = NotifySubscriberRequest, requestId = 250
19:47:18.713 [nacos-grpc-client-executor-111] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e78c66-f784-4217-93bd-dbeb32244908] Ack server push request, request = NotifySubscriberRequest, requestId = 250
19:48:31.044 [http-nio-9704-exec-5] INFO  c.r.b.s.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1828894
19:48:31.045 [http-nio-9704-exec-5] INFO  c.r.b.s.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：784652，5分钟内有效，如非本人操作，请忽略本短信！
19:50:34.632 [http-nio-9704-exec-7] INFO  c.r.b.s.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1829007
19:50:34.632 [http-nio-9704-exec-7] INFO  c.r.b.s.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：337992，5分钟内有效，如非本人操作，请忽略本短信！
19:51:30.603 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:51:30.606 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:51:30.935 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:51:30.935 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1b905e84[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:51:30.936 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750333140024_127.0.0.1_53719
19:51:30.937 [nacos-grpc-client-executor-164] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750333140024_127.0.0.1_53719]Ignore complete event,isRunning:false,isAbandon=false
19:51:30.948 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f535c03[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 165]
19:51:38.444 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:51:39.500 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ce01d6ce-f185-496d-91f1-734e39138ea1_config-0
19:51:39.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
19:51:39.623 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
19:51:39.637 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
19:51:39.884 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 245 ms to scan 248 urls, producing 0 keys and 0 values 
19:51:39.893 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
19:51:39.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
19:51:39.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
19:51:40.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 209 ms to scan 248 urls, producing 0 keys and 0 values 
19:51:40.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:51:40.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1118998513
19:51:40.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/906370291
19:51:40.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:51:40.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:51:40.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:51:42.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750333901741_127.0.0.1_56947
19:51:42.022 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Notify connected event to listeners.
19:51:42.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:51:42.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce01d6ce-f185-496d-91f1-734e39138ea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1402606475
19:51:42.154 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:51:45.316 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
19:51:45.316 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:51:45.316 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:51:45.623 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:51:49.392 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:51:50.068 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1fe80ebe-b775-433c-b119-29bf1eddd32f
19:51:50.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] RpcClient init label, labels = {module=naming, source=sdk}
19:51:50.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:51:50.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:51:50.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:51:50.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:51:50.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750333910079_127.0.0.1_56969
19:51:50.193 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Notify connected event to listeners.
19:51:50.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:51:50.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1402606475
19:51:52.911 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9704"]
19:51:52.967 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-biz-shop 192.168.0.68:9704 register finished
19:51:53.435 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Receive server push request, request = NotifySubscriberRequest, requestId = 252
19:51:53.441 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fe80ebe-b775-433c-b119-29bf1eddd32f] Ack server push request, request = NotifySubscriberRequest, requestId = 252
19:51:53.807 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStarted,61] - Started RuoyiBizShopApplication in 16.19 seconds (JVM running for 17.963)
19:51:53.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop-dev.yaml, group=DEFAULT_GROUP
19:51:53.837 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop.yaml, group=DEFAULT_GROUP
19:51:53.837 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop, group=DEFAULT_GROUP
19:51:54.289 [RMI TCP Connection(3)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
