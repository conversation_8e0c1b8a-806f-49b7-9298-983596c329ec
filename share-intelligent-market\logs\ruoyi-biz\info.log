09:11:01.889 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:04.189 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of add2d054-c558-40dd-98ce-d6eddfd28f63_config-0
09:11:04.294 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 56 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:04.353 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:04.372 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:04.624 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 247 ms to scan 248 urls, producing 0 keys and 0 values 
09:11:04.634 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:04.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:04.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:04.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 224 ms to scan 248 urls, producing 0 keys and 0 values 
09:11:04.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:04.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/364389956
09:11:04.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/542598487
09:11:04.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:04.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:04.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:07.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381867404_127.0.0.1_49476
09:11:07.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Notify connected event to listeners.
09:11:07.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:07.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1788036341
09:11:08.253 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
