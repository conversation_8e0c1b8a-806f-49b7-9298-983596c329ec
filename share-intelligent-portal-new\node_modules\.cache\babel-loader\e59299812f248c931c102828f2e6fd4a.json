{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue", "mtime": 1750385853718}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>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"}, {"version": 3, "names": ["_compositeExhibitionHall", "require", "_manufacturingSharing", "name", "data", "loading", "pageNum", "pageSize", "total", "bgUrl", "deviceMenuList", "url", "currentItem", "imageUrl", "materialName", "description", "videoIcon", "isPlay", "materialList", "productName", "productImageUrl", "productId", "created", "$route", "query", "getList", "methods", "_this", "params", "getCompositeExhibitionHallList", "then", "response", "code", "rows", "length", "sort", "a", "b", "sortOrder", "for<PERSON>ach", "item", "index", "res", "findIndex", "id", "handleClick", "_this2", "console", "log", "listSysProduct", "exhibitionHallId", "handlePlayAudio", "toFactory", "$router", "push", "path", "page"], "sources": ["src/views/compositeExhibitionHall/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"left\">\r\n      <div class=\"hall-bg\">\r\n        <img class=\"hall-img\" :src=\"currentItem.url\" alt=\"\" />\r\n        <div class=\"card\">\r\n          <div class=\"card-top\">\r\n            <div class=\"card-left\">\r\n              <img class=\"card-img\" :src=\"currentItem.imageUrl\" alt=\"\" />\r\n              <el-button type=\"primary\" :icon=\"videoIcon\" @click=\"handlePlayAudio\">播放音频</el-button>\r\n            </div>\r\n            <div class=\"card-right\">\r\n              <div class=\"card-title\">{{ currentItem.materialName }}</div>\r\n              <div class=\"card-content\">{{ currentItem.description }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-bottom\" v-if=\"materialList.length > 0\">\r\n            <div class=\"card-item\" v-for=\"(item, index) in materialList\" @click=\"toFactory(item)\" :key=\"index\">\r\n              <div class=\"card-item-title\">{{ item.productName }}</div>\r\n              <img :src=\"item.productImageUrl\" alt=\"\">\r\n            </div>\r\n          </div>\r\n          <div class=\"card-empty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"hall-list\">\r\n        <div class=\"hall-item\" v-for=\"(item, index) in deviceMenuList\" @click=\"handleClick(item)\" :key=\"index\">\r\n          <img :src=\"item.url\" alt=\"\" />\r\n          <div class=\"hall-title\">{{ item.materialName }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getCompositeExhibitionHallList } from '@/api/compositeExhibitionHall'\r\nimport { listSysProduct } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceSharing\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      bgUrl: [\r\n        require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n      ],\r\n      deviceMenuList: [\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n      ],\r\n      currentItem: {\r\n        url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        imageUrl: require(\"@/assets/compositeExhibitionHall/hall_img.png\"),\r\n        materialName: \"化工环保展厅\",\r\n        description: '暂无介绍',\r\n      },\r\n      videoIcon: 'el-icon-video-play',\r\n      isPlay: false,\r\n      materialList: [\r\n        {\r\n          productName: '玻璃钢槽式电缆桥架',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material1.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢格栅',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material2.png\"),\r\n        },\r\n        {\r\n          productName: '钢筋钢板',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material3.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢管道',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material4.png\"),\r\n        }\r\n      ],\r\n      productId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.productId = this.$route.query.productId ? this.$route.query.productId : '';\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      getCompositeExhibitionHallList(params).then((response) => {\r\n        if (response.code === 200 && response.rows.length > 0) {\r\n          this.deviceMenuList = response.rows;\r\n          this.deviceMenuList.sort((a, b) => a.sortOrder - b.sortOrder);\r\n          // 按顺序循环bgUrl\r\n          this.deviceMenuList.forEach((item, index) => {\r\n            item.url = this.bgUrl[index % this.bgUrl.length];\r\n          })\r\n          if (this.productId) {\r\n            getCompositeExhibitionHallList({\r\n              pageNum: 1,\r\n              pageSize: 1000,\r\n              productId: this.productId,\r\n            }).then((res) => {\r\n              if (res.code === 200 && res.rows.length > 0) {\r\n                let index = this.deviceMenuList.findIndex(item => item.id === res.rows[0].id);\r\n                this.handleClick(this.deviceMenuList[index]);\r\n              }\r\n            })\r\n          } else {\r\n            this.handleClick(this.deviceMenuList[0]);\r\n          }\r\n          // this.handleClick(this.deviceMenuList[0]);\r\n        }\r\n      });\r\n    },\r\n    handleClick(item) {\r\n      console.log(item, 'item')\r\n      this.currentItem.url = item.url;\r\n      this.currentItem.materialName = item.materialName;\r\n      this.currentItem.imageUrl = item.imageUrl ? item.imageUrl : require(\"@/assets/compositeExhibitionHall/hall_img.png\");\r\n      this.currentItem.description = item.description ? item.description : '暂无介绍';\r\n      // if (item.imageList) {\r\n      //   this.materialList = item.imageList.split(',').map(item => {\r\n      //     let name = item.split('/')[item.split('/').length - 1].split('_');\r\n      //     name.pop();\r\n      //     return {\r\n      //       name: name.join('_'),\r\n      //       img: item\r\n      //     }\r\n      //   })\r\n      // } else {\r\n      //   this.materialList = []\r\n      // }\r\n      listSysProduct({\r\n        exhibitionHallId: item.id,\r\n        pageNum: 1,\r\n        pageSize: 1000,\r\n      }).then((res) => {\r\n        if (res.code === 200) {\r\n          this.materialList = res.rows || [];\r\n        }\r\n      });\r\n    },\r\n    handlePlayAudio() {\r\n      if (this.isPlay) {\r\n        this.videoIcon = 'el-icon-video-play'\r\n        this.isPlay = false\r\n      } else {\r\n        this.videoIcon = 'el-icon-video-pause'\r\n        this.isPlay = true\r\n      }\r\n    },\r\n    toFactory(item) {\r\n      this.$router.push({\r\n        path: '/manufacturingSharing',\r\n        query: {\r\n          index: 2,\r\n          page: 2,\r\n          productId: item.productId,\r\n        }\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  height: auto;\r\n  padding: 40px;\r\n  box-sizing: border-box;\r\n  background-color: #F7FBFF;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .left {\r\n    width: 80%;\r\n    position: relative;\r\n\r\n    .hall-img {\r\n      width: 100%;\r\n    }\r\n\r\n    .card {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      background-color: #F7FBFF;\r\n      border-radius: 10px;\r\n      padding: 40px;\r\n      box-sizing: border-box;\r\n      width: 84%;\r\n      height: 74%;\r\n\r\n      .card-top {\r\n        height: 52%;\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        .card-left {\r\n          width: 29%;\r\n\r\n          .card-img {\r\n            width: 100%;\r\n            margin-bottom: 1vw;\r\n          }\r\n        }\r\n\r\n        .card-right {\r\n          width: 65%;\r\n\r\n          .card-title {\r\n            font-weight: 500;\r\n            font-size: 1.1vw;\r\n            color: #222222;\r\n            margin-bottom: 0.8vw;\r\n          }\r\n\r\n          .card-content {\r\n            font-weight: 400;\r\n            font-size: 0.7vw;\r\n            line-height: 1vw;\r\n            color: #787878;\r\n            overflow-y: auto;\r\n            box-sizing: border-box;\r\n            height: 90%;\r\n            overflow: auto;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-bottom {\r\n        height: 48%;\r\n        margin-top: 2vw;\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        overflow-x: auto;\r\n\r\n        .card-item {\r\n          min-width: 22%;\r\n          margin: 0.5vw;\r\n          margin-right: 1.3vw;\r\n\r\n          .card-item-title {\r\n            width: 100%;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            font-weight: 400;\r\n            font-size: 0.9vw;\r\n            color: #222222;\r\n            margin-bottom: 14px;\r\n          }\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 75%;\r\n          }\r\n        }\r\n        \r\n      }\r\n\r\n\r\n\r\n      .card-empty {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 18%;\r\n    height: 46vw;\r\n    overflow-y: auto;\r\n    background-color: transparent;\r\n\r\n    .hall-list {\r\n      width: 100%;\r\n\r\n      .hall-item {\r\n        width: 100%;\r\n        position: relative;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 100%;\r\n          margin-bottom: 1.5vw;\r\n        }\r\n\r\n        .hall-title {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          font-weight: 500;\r\n          font-size: 1.1vw;\r\n          color: #000000;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAuCA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA,GACAR,OAAA,gDACAA,OAAA,gDACAA,OAAA,gDACAA,OAAA,+CACA;MACAS,cAAA,GACA;QACAC,GAAA,EAAAV,OAAA;MACA,GACA;QACAU,GAAA,EAAAV,OAAA;MACA,GACA;QACAU,GAAA,EAAAV,OAAA;MACA,GACA;QACAU,GAAA,EAAAV,OAAA;MACA,GACA;QACAU,GAAA,EAAAV,OAAA;MACA,GACA;QACAU,GAAA,EAAAV,OAAA;MACA;QACAU,GAAA,EAAAV,OAAA;MACA;QACAU,GAAA,EAAAV,OAAA;MACA,EACA;MACAW,WAAA;QACAD,GAAA,EAAAV,OAAA;QACAY,QAAA,EAAAZ,OAAA;QACAa,YAAA;QACAC,WAAA;MACA;MACAC,SAAA;MACAC,MAAA;MACAC,YAAA,GACA;QACAC,WAAA;QACAC,eAAA,EAAAnB,OAAA;MACA,GACA;QACAkB,WAAA;QACAC,eAAA,EAAAnB,OAAA;MACA,GACA;QACAkB,WAAA;QACAC,eAAA,EAAAnB,OAAA;MACA,GACA;QACAkB,WAAA;QACAC,eAAA,EAAAnB,OAAA;MACA,EACA;MACAoB,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,SAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,SAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,SAAA;IACA,KAAAI,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtB,OAAA;MACA,IAAAuB,MAAA;QACAtB,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA;MACA,IAAAsB,uDAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA,YAAAD,QAAA,CAAAE,IAAA,CAAAC,MAAA;UACAP,KAAA,CAAAjB,cAAA,GAAAqB,QAAA,CAAAE,IAAA;UACAN,KAAA,CAAAjB,cAAA,CAAAyB,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAD,CAAA,CAAAE,SAAA,GAAAD,CAAA,CAAAC,SAAA;UAAA;UACA;UACAX,KAAA,CAAAjB,cAAA,CAAA6B,OAAA,WAAAC,IAAA,EAAAC,KAAA;YACAD,IAAA,CAAA7B,GAAA,GAAAgB,KAAA,CAAAlB,KAAA,CAAAgC,KAAA,GAAAd,KAAA,CAAAlB,KAAA,CAAAyB,MAAA;UACA;UACA,IAAAP,KAAA,CAAAN,SAAA;YACA,IAAAQ,uDAAA;cACAvB,OAAA;cACAC,QAAA;cACAc,SAAA,EAAAM,KAAA,CAAAN;YACA,GAAAS,IAAA,WAAAY,GAAA;cACA,IAAAA,GAAA,CAAAV,IAAA,YAAAU,GAAA,CAAAT,IAAA,CAAAC,MAAA;gBACA,IAAAO,KAAA,GAAAd,KAAA,CAAAjB,cAAA,CAAAiC,SAAA,WAAAH,IAAA;kBAAA,OAAAA,IAAA,CAAAI,EAAA,KAAAF,GAAA,CAAAT,IAAA,IAAAW,EAAA;gBAAA;gBACAjB,KAAA,CAAAkB,WAAA,CAAAlB,KAAA,CAAAjB,cAAA,CAAA+B,KAAA;cACA;YACA;UACA;YACAd,KAAA,CAAAkB,WAAA,CAAAlB,KAAA,CAAAjB,cAAA;UACA;UACA;QACA;MACA;IACA;IACAmC,WAAA,WAAAA,YAAAL,IAAA;MAAA,IAAAM,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAAR,IAAA;MACA,KAAA5B,WAAA,CAAAD,GAAA,GAAA6B,IAAA,CAAA7B,GAAA;MACA,KAAAC,WAAA,CAAAE,YAAA,GAAA0B,IAAA,CAAA1B,YAAA;MACA,KAAAF,WAAA,CAAAC,QAAA,GAAA2B,IAAA,CAAA3B,QAAA,GAAA2B,IAAA,CAAA3B,QAAA,GAAAZ,OAAA;MACA,KAAAW,WAAA,CAAAG,WAAA,GAAAyB,IAAA,CAAAzB,WAAA,GAAAyB,IAAA,CAAAzB,WAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAkC,oCAAA;QACAC,gBAAA,EAAAV,IAAA,CAAAI,EAAA;QACAtC,OAAA;QACAC,QAAA;MACA,GAAAuB,IAAA,WAAAY,GAAA;QACA,IAAAA,GAAA,CAAAV,IAAA;UACAc,MAAA,CAAA5B,YAAA,GAAAwB,GAAA,CAAAT,IAAA;QACA;MACA;IACA;IACAkB,eAAA,WAAAA,gBAAA;MACA,SAAAlC,MAAA;QACA,KAAAD,SAAA;QACA,KAAAC,MAAA;MACA;QACA,KAAAD,SAAA;QACA,KAAAC,MAAA;MACA;IACA;IACAmC,SAAA,WAAAA,UAAAZ,IAAA;MACA,KAAAa,OAAA,CAAAC,IAAA;QACAC,IAAA;QACA/B,KAAA;UACAiB,KAAA;UACAe,IAAA;UACAnC,SAAA,EAAAmB,IAAA,CAAAnB;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}