<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucDemandJointMapper">
    
    <resultMap type="UucDemandJoint" id="UucDemandJointResult">
        <result property="id"    column="id"    />
        <result property="demandId"    column="demand_id"    />
        <result property="demandTitle"    column="demand_title"    />
        <result property="fieldName"    column="field_name"    />
        <result property="content"    column="content"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkTel"    column="link_tel"    />
        <result property="company"    column="company"    />
        <result property="pictures"    column="pictures"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucDemandJointVo">
        select id, demand_id, demand_title, field_name, content, link_man, link_tel, company, pictures, attachments, remark, create_by, create_time, update_by, update_time from uuc_demand_joint
    </sql>

    <select id="selectUucDemandJointList" parameterType="UucDemandJoint" resultMap="UucDemandJointResult">
        <include refid="selectUucDemandJointVo"/>
        <where>  
            <if test="demandId != null  and demandId != ''"> and demand_id like concat('%', #{demandId}, '%')</if>
            <if test="demandTitle != null  and demandTitle != ''"> and demand_title like concat('%', #{demandTitle}, '%')</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="linkMan != null  and linkMan != ''"> and link_man like concat('%', #{linkMan}, '%')</if>
            <if test="linkTel != null  and linkTel != ''"> and link_tel like concat('%', #{linkTel}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucDemandJointById" parameterType="Long" resultMap="UucDemandJointResult">
        <include refid="selectUucDemandJointVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucDemandJoint" parameterType="UucDemandJoint">
        insert into uuc_demand_joint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="demandId != null and demandId != ''">demand_id,</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title,</if>
            <if test="fieldName != null and fieldName != ''">field_name,</if>
            <if test="content != null">content,</if>
            <if test="linkMan != null and linkMan != ''">link_man,</if>
            <if test="linkTel != null and linkTel != ''">link_tel,</if>
            <if test="company != null">company,</if>
            <if test="pictures != null">pictures,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="demandId != null and demandId != ''">#{demandId},</if>
            <if test="demandTitle != null and demandTitle != ''">#{demandTitle},</if>
            <if test="fieldName != null and fieldName != ''">#{fieldName},</if>
            <if test="content != null">#{content},</if>
            <if test="linkMan != null and linkMan != ''">#{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">#{linkTel},</if>
            <if test="company != null">#{company},</if>
            <if test="pictures != null">#{pictures},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucDemandJoint" parameterType="UucDemandJoint">
        update uuc_demand_joint
        <trim prefix="SET" suffixOverrides=",">
            <if test="demandId != null and demandId != ''">demand_id = #{demandId},</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title = #{demandTitle},</if>
            <if test="fieldName != null and fieldName != ''">field_name = #{fieldName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="linkMan != null and linkMan != ''">link_man = #{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">link_tel = #{linkTel},</if>
            <if test="company != null">company = #{company},</if>
            <if test="pictures != null">pictures = #{pictures},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucDemandJointById" parameterType="Long">
        delete from uuc_demand_joint where id = #{id}
    </delete>

    <delete id="deleteUucDemandJointByIds" parameterType="String">
        delete from uuc_demand_joint where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>