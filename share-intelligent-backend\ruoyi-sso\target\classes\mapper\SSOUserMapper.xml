<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sso.mapper.SSOUserMapper">

    <resultMap type="com.ruoyi.sso.domain.SSOUser" id="SSOUserResult">
        <id property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="realName" column="real_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="userType" column="user_type"/>
        <result property="backendEnabled" column="backend_enabled"/>
        <result property="marketEnabled" column="market_enabled"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="loginCount" column="login_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSSOUserVo">
        select user_id, username, password, real_name, phone, email, avatar, status, user_type,
               backend_enabled, market_enabled, last_login_time, last_login_ip, login_count,
               create_by, create_time, update_by, update_time, remark
        from sso_users
    </sql>

    <select id="selectSSOUserByUsername" parameterType="String" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where username = #{username} and status = '0'
    </select>

    <select id="selectSSOUserByPhone" parameterType="String" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where phone = #{phone} and status = '0'
    </select>

    <select id="selectSSOUserById" parameterType="Long" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectSSOUserList" parameterType="com.ruoyi.sso.domain.SSOUser" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        <where>
            <if test="username != null and username != ''">
                AND username like concat('%', #{username}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone like concat('%', #{phone}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="userType != null and userType != ''">
                AND user_type = #{userType}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="checkUsernameUnique" parameterType="String" resultType="int">
        select count(1) from sso_users where username = #{username} limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultType="int">
        select count(1) from sso_users where phone = #{phone} limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultType="int">
        select count(1) from sso_users where email = #{email} limit 1
    </select>

    <select id="selectUsersBySystemAccess" parameterType="String" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where status = '0'
        <if test="clientId == 'backend'">
            and backend_enabled = '1'
        </if>
        <if test="clientId == 'market'">
            and market_enabled = '1'
        </if>
    </select>

    <insert id="insertSSOUser" parameterType="com.ruoyi.sso.domain.SSOUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sso_users
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">username,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="backendEnabled != null and backendEnabled != ''">backend_enabled,</if>
            <if test="marketEnabled != null and marketEnabled != ''">market_enabled,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">#{username},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="backendEnabled != null and backendEnabled != ''">#{backendEnabled},</if>
            <if test="marketEnabled != null and marketEnabled != ''">#{marketEnabled},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateSSOUser" parameterType="com.ruoyi.sso.domain.SSOUser">
        update sso_users
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="backendEnabled != null and backendEnabled != ''">backend_enabled = #{backendEnabled},</if>
            <if test="marketEnabled != null and marketEnabled != ''">market_enabled = #{marketEnabled},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where user_id = #{userId}
    </update>

    <update id="updateUserLoginInfo">
        update sso_users 
        set last_login_time = sysdate(), 
            last_login_ip = #{loginIp}, 
            login_count = IFNULL(login_count, 0) + 1
        where user_id = #{userId}
    </update>

    <update id="resetUserPassword">
        update sso_users set password = #{password} where user_id = #{userId}
    </update>

    <delete id="deleteSSOUserById" parameterType="Long">
        delete from sso_users where user_id = #{userId}
    </delete>

    <delete id="deleteSSOUserByIds" parameterType="String">
        delete from sso_users where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
