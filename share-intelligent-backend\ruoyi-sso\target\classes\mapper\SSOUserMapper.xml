<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sso.mapper.SSOUserMapper">

    <resultMap type="com.ruoyi.sso.domain.SSOUser" id="SSOUserResult">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="nickname" column="nickname"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="loginCount" column="login_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSSOUserVo">
        select id, username, password, nickname, phone, email, avatar, status,
               last_login_time, login_count, create_time, update_time
        from sso_users
    </sql>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="String" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where username = #{username} and status = 1
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" parameterType="String" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where phone = #{phone} and status = 1
    </select>

    <!-- 根据ID查询用户 -->
    <select id="selectById" parameterType="Long" resultMap="SSOUserResult">
        <include refid="selectSSOUserVo"/>
        where id = #{id}
    </select>



    <!-- 更新最后登录时间 -->
    <update id="updateLastLoginTime" parameterType="Long">
        update sso_users
        set last_login_time = now(), login_count = login_count + 1
        where id = #{userId}
    </update>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.ruoyi.sso.domain.SSOUser" useGeneratedKeys="true" keyProperty="id">
        insert into sso_users
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">username,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="status != null">status,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">#{username},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="status != null">#{status},</if>
            now()
        </trim>
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="com.ruoyi.sso.domain.SSOUser">
        update sso_users
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="status != null">status = #{status},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

</mapper>
