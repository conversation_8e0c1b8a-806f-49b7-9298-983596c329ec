<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MemberMapper">

    <resultMap type="com.ruoyi.system.api.domain.Member" id="MemberResult">
        <id property="memberId" column="member_id"/>
        <result property="memberPhone" column="member_phone"/>
        <result property="memberPassword" column="member_password"/>
        <result property="memberRealName" column="member_real_name"/>
        <result property="memberStatus" column="member_status"/>
        <result property="avatar" column="avatar"/>
        <result property="isAdmin" column="is_admin"/>
        <result property="remark" column="remark"/>
        <result property="rongYunToken" column="rong_yun_token"/>
        <result property="nickname" column="nickname"/>
    </resultMap>

    <sql id="selectMemberVo">
        select member_id, member_phone, member_password, member_real_name, is_admin, rong_yun_token, member_status, nickname, avatar, remark
        from member
    </sql>
    <update id="updateMemberRongYunTokenByMemberPhone" parameterType="com.ruoyi.system.api.domain.Member">
        UPDATE member set rong_yun_token = #{rongYunToken} WHERE member_phone = #{memberPhone}
    </update>

    <select id="selectMemberIdList" resultType="java.lang.Long">
        SELECT member_id from member where member_status = '0'
    </select>

    <select id="selectMemberByMemberPhone" resultType="com.ruoyi.system.api.domain.Member" resultMap="MemberResult">
        <include refid="selectMemberVo"></include>
        WHERE member_phone = #{memberPhone}
    </select>

    <select id="selectMemberListWithoutRongYunToken"  resultMap="MemberResult">
        <include refid="selectMemberVo"></include>
        WHERE rong_yun_token = '' or rong_yun_token is null
    </select>

    <select id="selectByMemberPhoneOrNickname" resultMap="MemberResult">
        <include refid="selectMemberVo"></include>
        WHERE member_status = '1' and (member_phone like concat('%', #{searchKey}, '%')
        or nickname like concat('%', #{searchKey}, '%'))
    </select>

</mapper>