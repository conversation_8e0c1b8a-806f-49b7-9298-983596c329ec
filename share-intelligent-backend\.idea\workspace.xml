<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3792335a-6a1f-4eb3-8259-02650975830a" name="Changes" comment="登录 退出问题 与单点登录问题解决">
      <change afterPath="$PROJECT_DIR$/ruoyi-sso/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-api/ruoyi-api-portalweb/src/main/java/com/ruoyi/portalweb/api/RemoteMemberService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-api/ruoyi-api-portalweb/src/main/java/com/ruoyi/portalweb/api/RemoteMemberService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-api/ruoyi-api-system/src/main/java/com/ruoyi/system/api/domain/Member.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-api/ruoyi-api-system/src/main/java/com/ruoyi/system/api/domain/Member.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/PortalController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/PortalController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/service/PortalLoginService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/service/PortalLoginService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-auth/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-cas-auth/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-cas-auth/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-gateway/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-gateway/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-file/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-file/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-im/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-im/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-job/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-job/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalconsole/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalconsole/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/java/com/ruoyi/portalweb/controller/MemberController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/java/com/ruoyi/portalweb/controller/MemberController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/java/com/ruoyi/portalweb/service/impl/MemberServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/java/com/ruoyi/portalweb/service/impl/MemberServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-portalweb/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/IMemberService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/IMemberService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/MemberServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/MemberServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/mapper/system/MemberMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/mapper/system/MemberMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-sso/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-sso/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/RuoyiSSOApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/RuoyiSSOApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/controller/SSOAuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/controller/SSOAuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/service/impl/SSOAuthServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/service/impl/SSOAuthServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-sso/src/main/resources/static/login.html" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-sso/src/main/resources/static/login.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-visual/ruoyi-monitor/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-visual/ruoyi-monitor/src/main/resources/bootstrap.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yAgnXrxaKk3cvuXgwCDIBnxZjD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiCasAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiFileApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiGenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiJobApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiMonitorApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiPortalconsoleApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiPortalwebApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoyiImApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoyiSSOApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/pom.xml&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\company\nmd\nmdnew\share-intelligent-backend\ruoyi-modules\ruoyi-system\src\main\resources\mapper\system" />
      <recent name="E:\company\nmd\nmdnew\share-intelligent-backend\ruoyi-modules\ruoyi-system\src\main\java\com\ruoyi\system\controller" />
      <recent name="E:\company\nmd\nmdnew\share-intelligent-backend\ruoyi-modules\ruoyi-system\src\main\java\com\ruoyi\system\service\impl" />
      <recent name="E:\company\nmd\nmdnew\share-intelligent-backend\ruoyi-modules\ruoyi-system\src\main\java\com\ruoyi\system\service" />
      <recent name="E:\company\nmd\nmdnew\share-intelligent-backend\ruoyi-modules\ruoyi-system\src\main\java\com\ruoyi\system\mapper" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\src\main\java\com\ruoyi\sso" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoyiSSOApplication">
    <configuration name="RuoYiAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.auth.RuoYiAuthApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.auth.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiCasAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-cas-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.cas.auth.RuoYiCasAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.file.RuoYiFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.gateway.RuoYiGatewayApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.gateway.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.gen.RuoYiGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiJobApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-job" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.job.RuoYiJobApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-visual-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.modules.monitor.RuoYiMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiPortalconsoleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-modules-portalconsole" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.portalconsole.RuoYiPortalconsoleApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.portalconsole.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiPortalwebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-modules-portalweb" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.portalweb.RuoYiPortalwebApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.portalweb.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-modules-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.system.RuoYiSystemApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.system.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiImApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-im" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.im.RuoyiImApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiSSOApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-sso" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.sso.RuoyiSSOApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.RuoYiCasAuthApplication" />
      <item itemvalue="Spring Boot.RuoYiFileApplication" />
      <item itemvalue="Spring Boot.RuoYiGenApplication" />
      <item itemvalue="Spring Boot.RuoYiJobApplication" />
      <item itemvalue="Spring Boot.RuoYiMonitorApplication" />
      <item itemvalue="Spring Boot.RuoyiImApplication" />
      <item itemvalue="Spring Boot.RuoyiSSOApplication" />
      <item itemvalue="Spring Boot.RuoYiAuthApplication" />
      <item itemvalue="Spring Boot.RuoYiGatewayApplication" />
      <item itemvalue="Spring Boot.RuoYiPortalconsoleApplication" />
      <item itemvalue="Spring Boot.RuoYiPortalwebApplication" />
      <item itemvalue="Spring Boot.RuoYiSystemApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.RuoYiPortalwebApplication" />
        <item itemvalue="Spring Boot.RuoYiAuthApplication" />
        <item itemvalue="Spring Boot.RuoYiSystemApplication" />
        <item itemvalue="Spring Boot.RuoYiGatewayApplication" />
        <item itemvalue="Spring Boot.RuoYiPortalconsoleApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3792335a-6a1f-4eb3-8259-02650975830a" name="Changes" comment="" />
      <created>1749282958916</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749282958916</updated>
      <workItem from="1749282959979" duration="2438000" />
      <workItem from="1749431642125" duration="513000" />
      <workItem from="1749432218611" duration="16932000" />
      <workItem from="1749521522176" duration="3582000" />
      <workItem from="1749539113527" duration="4977000" />
      <workItem from="1749603685320" duration="4262000" />
      <workItem from="1749805671205" duration="335000" />
      <workItem from="1750039901730" duration="1977000" />
      <workItem from="1750122039004" duration="5541000" />
      <workItem from="1750208423378" duration="595000" />
      <workItem from="1750214766707" duration="11052000" />
      <workItem from="1750295328488" duration="353000" />
      <workItem from="1750295816675" duration="28826000" />
      <workItem from="1750381218205" duration="13537000" />
      <workItem from="1750400623270" duration="4009000" />
    </task>
    <task id="LOCAL-00001" summary="代码提交">
      <option name="closed" value="true" />
      <created>1750152213335</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750152213335</updated>
    </task>
    <task id="LOCAL-00002" summary="登录 退出问题 与单点登录问题解决">
      <option name="closed" value="true" />
      <created>1750329886909</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750329886909</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="cbd15cc7-3597-4512-af85-a04ddad8ad40" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="cbd15cc7-3597-4512-af85-a04ddad8ad40">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="代码提交" />
    <MESSAGE value="登录 退出问题 与单点登录问题解决" />
    <option name="LAST_COMMIT_MESSAGE" value="登录 退出问题 与单点登录问题解决" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/service/impl/SSOUserManagementServiceImpl.java</url>
          <line>166</line>
          <option name="timeStamp" value="77" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/service/impl/SSOUserManagementServiceImpl.java</url>
          <line>167</line>
          <option name="timeStamp" value="78" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ruoyi-sso/src/main/java/com/ruoyi/sso/service/impl/SSOUserManagementServiceImpl.java</url>
          <line>169</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
      </breakpoints>
      <breakpoints-dialog>
        <breakpoints-dialog>
          <selected-grouping-rules>
            <grouping-rule id="XBreakpointGroupingByPackageRule" />
          </selected-grouping-rules>
        </breakpoints-dialog>
      </breakpoints-dialog>
    </breakpoint-manager>
  </component>
</project>