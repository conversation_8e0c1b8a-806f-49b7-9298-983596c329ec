package com.ruoyi.system.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.service.IMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/member")
public class MemberController {

    @Autowired
    private IMemberService memberService;

    @GetMapping("/list")
    public List<Long> list() {
        return memberService.selectMemberIdList();
    }
    
    /**
     * 通过手机号查询会员信息（用于SSO验证）
     */
    @GetMapping("/info/{memberPhone}")
    public R<Member> getMemberInfo(@PathVariable("memberPhone") String memberPhone, 
                                  @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("没有权限访问用户数据");
        }
        
        Member member = memberService.selectMemberByPhone(memberPhone);
        if (StringUtils.isNull(member)) {
            return R.fail("用户不存在");
        }
        
        return R.ok(member);
    }
    
    /**
     * 验证会员密码（用于SSO验证）
     */
    @GetMapping("/validate/{memberPhone}/{password}")
    public R<Member> validateMemberPassword(@PathVariable("memberPhone") String memberPhone,
                                          @PathVariable("password") String password,
                                          @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("没有权限访问用户数据");
        }
        
        Member member = memberService.selectMemberByPhone(memberPhone);
        if (StringUtils.isNull(member)) {
            return R.fail("用户不存在");
        }
        
        // 验证密码
        if (!SecurityUtils.matchesPassword(password, member.getMemberPassword())) {
            return R.fail("密码错误");
        }
        
        return R.ok(member);
    }
}
