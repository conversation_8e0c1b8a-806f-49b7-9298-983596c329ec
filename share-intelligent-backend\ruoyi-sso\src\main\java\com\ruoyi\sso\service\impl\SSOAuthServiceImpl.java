package com.ruoyi.sso.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.sso.domain.dto.LoginRequest;
import com.ruoyi.sso.domain.dto.TokenValidateRequest;
import com.ruoyi.sso.domain.vo.LoginResponse;
import com.ruoyi.sso.domain.vo.TokenValidateResponse;
import com.ruoyi.sso.service.SSOAuthService;
import com.ruoyi.sso.service.UserMappingService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.RemoteMemberService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO认证服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOAuthServiceImpl implements SSOAuthService {

    private static final Logger log = LoggerFactory.getLogger(SSOAuthServiceImpl.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserMappingService userMappingService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteMemberService remoteMemberService;

    // 密码编码器
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    // 从配置文件读取客户端配置
    @Value("${sso.clients.backend.secret:}")
    private String backendSecret;

    @Value("${sso.clients.market.secret:}")
    private String marketSecret;

    // Token配置
    @Value("${sso.token.expire-minutes:30}")
    private int tokenExpireMinutes;

    @Value("${sso.token.access-token-expire-minutes:480}")
    private int accessTokenExpireMinutes;

    @Value("${sso.token.secret:sso_default_secret}")
    private String tokenSecret;

    @Value("${sso.token.prefix:sso_token:}")
    private String tokenPrefix;

    @Value("${sso.token.access-prefix:access_token:}")
    private String accessTokenPrefix;

    // 授权码前缀
    private static final String AUTH_CODE_PREFIX = "auth_code:";
    private static final String USER_SESSION_PREFIX = "user_session:";

    // ThreadLocal存储当前请求的客户端ID
    private static final ThreadLocal<String> CLIENT_ID_CONTEXT = new ThreadLocal<>();

    @Override
    public boolean isValidClient(String clientId) {
        if (StringUtils.isEmpty(clientId)) {
            return false;
        }
        
        // 检查支持的客户端
        return "backend".equals(clientId) || "market".equals(clientId);
    }

    @Override
    public String getCurrentUserToken() {
        // 这里应该从当前会话中获取用户Token
        // 暂时返回null，表示未登录
        // TODO: 实现从HTTP Session或其他方式获取当前用户Token
        return null;
    }

    @Override
    public String generateAuthCode(String userToken, String clientId, String redirectUri) {
        if (StringUtils.isEmpty(userToken) || StringUtils.isEmpty(clientId)) {
            return null;
        }

        // 生成授权码
        String authCode = IdUtils.fastSimpleUUID();
        
        // 构造授权码数据
        Map<String, Object> authData = new HashMap<>();
        authData.put("userToken", userToken);
        authData.put("clientId", clientId);
        authData.put("redirectUri", redirectUri);
        authData.put("createTime", System.currentTimeMillis());
        
        // 存储授权码，设置较短的过期时间（5分钟）
        String redisKey = AUTH_CODE_PREFIX + authCode;
        redisService.setCacheObject(redisKey, authData, 5L, TimeUnit.MINUTES);
        
        log.info("生成授权码: {} for client: {}", authCode, clientId);
        
        return authCode;
    }

    @Override
    public LoginResponse authenticate(LoginRequest loginRequest) {
        LoginResponse response = new LoginResponse();

        try {
            // 验证客户端
            if (!isValidClient(loginRequest.getClientId())) {
                response.setSuccess(false);
                response.setMessage("无效的客户端ID");
                return response;
            }

            // 设置客户端ID到ThreadLocal
            CLIENT_ID_CONTEXT.set(loginRequest.getClientId());
            
            // 根据登录类型进行不同的验证
            if ("sms".equals(loginRequest.getLoginType())) {
                // 短信验证码登录
                if (StringUtils.isEmpty(loginRequest.getPhone()) ||
                    StringUtils.isEmpty(loginRequest.getSmsCode())) {
                    response.setSuccess(false);
                    response.setMessage("手机号或短信验证码不能为空");
                    return response;
                }
            } else {
                // 密码登录
                if (StringUtils.isEmpty(loginRequest.getUsername()) ||
                    StringUtils.isEmpty(loginRequest.getPassword())) {
                    response.setSuccess(false);
                    response.setMessage("用户名或密码不能为空");
                    return response;
                }
            }

            // 验证验证码（如果提供）
            if (StringUtils.isNotEmpty(loginRequest.getCaptcha()) &&
                StringUtils.isNotEmpty(loginRequest.getUuid())) {
                String verifyKey = "sso:captcha:" + loginRequest.getUuid();
                String storedCaptcha = redisService.getCacheObject(verifyKey);

                if (StringUtils.isEmpty(storedCaptcha)) {
                    response.setSuccess(false);
                    response.setMessage("验证码已过期");
                    return response;
                }

                if (!loginRequest.getCaptcha().equalsIgnoreCase(storedCaptcha)) {
                    response.setSuccess(false);
                    response.setMessage("验证码错误");
                    return response;
                }

                // 验证成功后删除验证码
                redisService.deleteObject(verifyKey);
            }

            // 验证短信验证码（如果是短信登录）
            if ("sms".equals(loginRequest.getLoginType())) {
                String smsCodeKey = "sso:sms:code:" + loginRequest.getPhone();
                String storedSmsCode = redisService.getCacheObject(smsCodeKey);

                if (StringUtils.isEmpty(storedSmsCode)) {
                    response.setSuccess(false);
                    response.setMessage("短信验证码已过期或不存在");
                    return response;
                }

                if (!loginRequest.getSmsCode().equals(storedSmsCode)) {
                    response.setSuccess(false);
                    response.setMessage("短信验证码错误");
                    return response;
                }

                // 验证成功后删除短信验证码
                redisService.deleteObject(smsCodeKey);
            }
            
            // 根据登录类型调用不同的用户认证方法
            SysUser user = null;
            if ("sms".equals(loginRequest.getLoginType())) {
                // 短信验证码登录，通过手机号查找用户
                user = authenticateUserByPhone(loginRequest.getPhone());
                if (user == null) {
                    response.setSuccess(false);
                    response.setMessage("手机号未绑定用户或用户不存在");
                    return response;
                }
            } else {
                // 密码登录
                user = authenticateUser(loginRequest.getUsername(), loginRequest.getPassword());
                if (user == null) {
                    response.setSuccess(false);
                    response.setMessage("用户名或密码错误");
                    return response;
                }
            }

            // 检查用户状态
            if (!"0".equals(user.getStatus())) {
                response.setSuccess(false);
                response.setMessage("用户已被禁用");
                return response;
            }
            
            // 生成用户Token
            String userToken = IdUtils.fastSimpleUUID();
            
            // 存储用户会话信息
            Map<String, Object> userSession = new HashMap<>();
            userSession.put("userId", user.getUserId());
            userSession.put("username", user.getUserName());
            userSession.put("nickName", user.getNickName());
            userSession.put("email", user.getEmail());
            userSession.put("phonenumber", user.getPhonenumber());
            userSession.put("clientId", loginRequest.getClientId());
            userSession.put("loginTime", System.currentTimeMillis());
            
            String sessionKey = USER_SESSION_PREFIX + userToken;
            redisService.setCacheObject(sessionKey, userSession, (long) accessTokenExpireMinutes, TimeUnit.MINUTES);
            
            // 生成授权码
            String authCode = generateAuthCode(userToken, loginRequest.getClientId(), loginRequest.getRedirectUri());

            // 构建完整的回调URL（包含授权码和状态参数）
            String callbackUrl = buildCallbackUrl(loginRequest.getRedirectUri(), authCode, loginRequest.getState());

            response.setSuccess(true);
            response.setMessage("登录成功");
            response.setAuthCode(authCode);
            response.setRedirectUrl(callbackUrl);
            
            log.info("用户 {} 登录成功，客户端: {}", loginRequest.getUsername(), loginRequest.getClientId());

        } catch (Exception e) {
            log.error("用户认证异常", e);
            response.setSuccess(false);
            response.setMessage("认证服务异常");
        } finally {
            // 清理ThreadLocal
            CLIENT_ID_CONTEXT.remove();
        }

        return response;
    }

    @Override
    public Map<String, Object> exchangeToken(String authCode, String clientId, String clientSecret) {
        if (StringUtils.isEmpty(authCode) || StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return null;
        }
        
        // 验证客户端密钥
        if (!validateClientSecret(clientId, clientSecret)) {
            log.warn("客户端密钥验证失败: clientId={}", clientId);
            return null;
        }
        
        // 获取授权码数据
        String redisKey = AUTH_CODE_PREFIX + authCode;
        Map<String, Object> authData = redisService.getCacheObject(redisKey);
        
        if (authData == null) {
            log.warn("授权码不存在或已过期: {}", authCode);
            return null;
        }
        
        // 验证客户端ID
        String storedClientId = (String) authData.get("clientId");
        if (!clientId.equals(storedClientId)) {
            log.warn("客户端ID不匹配: {} != {}", clientId, storedClientId);
            return null;
        }
        
        // 删除授权码（一次性使用）
        redisService.deleteObject(redisKey);
        
        // 生成访问令牌
        String accessToken = IdUtils.fastSimpleUUID();
        String userToken = (String) authData.get("userToken");
        
        // 获取用户会话信息
        String sessionKey = USER_SESSION_PREFIX + userToken;
        Map<String, Object> userSession = redisService.getCacheObject(sessionKey);
        
        if (userSession == null) {
            log.warn("用户会话不存在: {}", userToken);
            return null;
        }
        
        // 存储访问令牌
        Map<String, Object> tokenData = new HashMap<>();
        tokenData.put("userToken", userToken);
        tokenData.put("clientId", clientId);
        tokenData.put("username", userSession.get("username"));
        tokenData.put("createTime", System.currentTimeMillis());
        
        String accessTokenKey = accessTokenPrefix + accessToken;
        redisService.setCacheObject(accessTokenKey, tokenData, (long) accessTokenExpireMinutes, TimeUnit.MINUTES);
        
        // 构造返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("access_token", accessToken);
        result.put("token_type", "Bearer");
        result.put("expires_in", accessTokenExpireMinutes * 60);
        result.put("scope", "read write");
        
        log.info("授权码换取令牌成功: clientId={}", clientId);
        
        return result;
    }

    @Override
    public TokenValidateResponse validateToken(TokenValidateRequest validateRequest) {
        TokenValidateResponse response = new TokenValidateResponse();
        
        try {
            String accessToken = validateRequest.getAccessToken();
            String clientId = validateRequest.getClientId();
            
            if (StringUtils.isEmpty(accessToken)) {
                response.setValid(false);
                response.setMessage("访问令牌不能为空");
                return response;
            }
            
            // 获取令牌数据
            String tokenKey = accessTokenPrefix + accessToken;
            Map<String, Object> tokenData = redisService.getCacheObject(tokenKey);
            
            if (tokenData == null) {
                response.setValid(false);
                response.setMessage("访问令牌无效或已过期");
                return response;
            }
            
            // 验证客户端ID（如果提供）
            if (StringUtils.isNotEmpty(clientId)) {
                String storedClientId = (String) tokenData.get("clientId");
                if (!clientId.equals(storedClientId)) {
                    response.setValid(false);
                    response.setMessage("客户端ID不匹配");
                    return response;
                }
            }
            
            response.setValid(true);
            response.setMessage("令牌验证成功");
            // 注意：这里暂时使用userToken作为userId，实际应该是真实的用户ID
            // response.setUserId(Long.valueOf(tokenData.get("userId").toString()));
            response.setUsername((String) tokenData.get("username"));
            response.setClientId((String) tokenData.get("clientId"));
            
        } catch (Exception e) {
            log.error("令牌验证异常", e);
            response.setValid(false);
            response.setMessage("令牌验证服务异常");
        }
        
        return response;
    }

    @Override
    public Map<String, Object> getUserInfo(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        
        // 获取令牌数据
        String tokenKey = accessTokenPrefix + accessToken;
        Map<String, Object> tokenData = redisService.getCacheObject(tokenKey);
        
        if (tokenData == null) {
            log.warn("访问令牌无效: {}", accessToken);
            return null;
        }
        
        String userToken = (String) tokenData.get("userToken");
        String sessionKey = USER_SESSION_PREFIX + userToken;
        Map<String, Object> userSession = redisService.getCacheObject(sessionKey);
        
        if (userSession == null) {
            log.warn("用户会话不存在: {}", userToken);
            return null;
        }
        
        // 构造用户信息
        Map<String, Object> userInfo = new HashMap<>();
        Long userId = (Long) userSession.get("userId");
        String username = (String) userSession.get("username");
        String clientId = (String) tokenData.get("clientId");

        userInfo.put("userId", userId != null ? userId.toString() : userToken);
        userInfo.put("username", username);
        userInfo.put("nickName", userSession.get("nickName"));
        userInfo.put("email", userSession.get("email"));
        userInfo.put("phonenumber", userSession.get("phonenumber"));
        userInfo.put("clientId", clientId);
        userInfo.put("loginTime", userSession.get("loginTime"));

        // 获取用户在该系统中的权限信息
        Map<String, Object> permissions = userMappingService.getUserPermissions(username, clientId);
        if (!permissions.isEmpty()) {
            userInfo.put("roles", userMappingService.getUserRoles(username, clientId));
            userInfo.put("permissions", userMappingService.getUserPermissionCodes(username, clientId));
            userInfo.put("hasPermission", true);
        } else {
            userInfo.put("roles", new String[0]);
            userInfo.put("permissions", new String[0]);
            userInfo.put("hasPermission", false);
        }

        return userInfo;
    }

    @Override
    public boolean logout(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return false;
        }
        
        try {
            // 获取令牌数据
            String tokenKey = accessTokenPrefix + accessToken;
            Map<String, Object> tokenData = redisService.getCacheObject(tokenKey);
            
            if (tokenData != null) {
                String userToken = (String) tokenData.get("userToken");
                
                // 删除访问令牌
                redisService.deleteObject(tokenKey);
                
                // 删除用户会话
                String sessionKey = USER_SESSION_PREFIX + userToken;
                redisService.deleteObject(sessionKey);
                
                log.info("用户登出成功: {}", tokenData.get("username"));
                return true;
            }
            
        } catch (Exception e) {
            log.error("登出异常", e);
        }
        
        return false;
    }

    @Override
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "SSO认证服务");
        status.put("version", "1.0.0");
        status.put("status", "running");
        status.put("timestamp", System.currentTimeMillis());
        
        // 添加支持的客户端信息
        Map<String, Object> clients = new HashMap<>();
        clients.put("backend", "复合材料共享智造平台");
        clients.put("market", "智能市场系统");
        status.put("supportedClients", clients);
        
        return status;
    }

    /**
     * 验证客户端密钥
     */
    private boolean validateClientSecret(String clientId, String clientSecret) {
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return false;
        }

        switch (clientId) {
            case "backend":
                return backendSecret.equals(clientSecret);
            case "market":
                return marketSecret.equals(clientSecret);
            default:
                return false;
        }
    }

    /**
     * 构造回调URL
     *
     * @param redirectUri 重定向URI
     * @param authCode 授权码
     * @param state 状态参数
     * @return 回调URL
     */
    private String buildCallbackUrl(String redirectUri, String authCode, String state) {
        StringBuilder url = new StringBuilder(redirectUri);

        if (redirectUri.contains("?")) {
            url.append("&");
        } else {
            url.append("?");
        }

        url.append("code=").append(authCode);

        if (StringUtils.isNotEmpty(state)) {
            url.append("&state=").append(state);
        }

        return url.toString();
    }

    /**
     * 验证用户名和密码
     *
     * @param username 用户名
     * @param password 密码
     * @return 用户信息，验证失败返回null
     */
    private SysUser authenticateUser(String username, String password) {
        try {
            // 获取当前客户端ID（从ThreadLocal或其他方式获取）
            String clientId = getCurrentClientId();

            if ("market".equals(clientId)) {
                // 市场系统：验证Member表
                return authenticateMarketUser(username, password);
            } else {
                // 主系统：验证SysUser表
                return authenticateBackendUser(username, password);
            }

        } catch (Exception e) {
            log.error("用户验证异常: {}", username, e);
            return null;
        }
    }

    /**
     * 验证主系统用户（Member表 - industry数据库）
     */
    private SysUser authenticateBackendUser(String username, String password) {
        try {
            // 主系统和市场系统都使用Member表，但连接不同的数据库
            // 这里暂时使用同一个remoteMemberService，实际应该根据数据库配置区分
            // TODO: 需要根据不同的数据库配置调用不同的Member服务

            R<Member> memberResult = remoteMemberService.validateMemberPassword(username, password, "inner");

            if (memberResult == null || memberResult.getData() == null) {
                log.warn("主系统用户不存在或密码错误: {}", username);
                return null;
            }

            Member member = memberResult.getData();

            // 检查用户状态
            if (!"0".equals(member.getMemberStatus())) {
                log.warn("主系统用户 {} 已被禁用", username);
                return null;
            }

            // 将Member转换为SysUser（为了统一SSO处理）
            SysUser sysUser = convertMemberToSysUser(member);
            // 标记为主系统用户
            sysUser.setRemark("BACKEND_USER");

            log.info("主系统用户 {} 验证成功", username);
            return sysUser;

        } catch (Exception e) {
            log.error("主系统用户验证异常: {}", username, e);
            return null;
        }
    }

    /**
     * 验证市场系统用户（Member表）
     */
    private SysUser authenticateMarketUser(String username, String password) {
        try {
            // 调用系统服务验证Member用户
            R<Member> memberResult = remoteMemberService.validateMemberPassword(username, password, "inner");

            if (memberResult == null || memberResult.getData() == null) {
                log.warn("市场系统用户不存在或密码错误: {}", username);
                return null;
            }

            Member member = memberResult.getData();

            // 检查用户状态
            if (!"0".equals(member.getMemberStatus())) {
                log.warn("市场系统用户 {} 已被禁用", username);
                return null;
            }

            // 将Member转换为SysUser（为了统一SSO处理）
            SysUser sysUser = convertMemberToSysUser(member);

            log.info("市场系统用户 {} 验证成功", username);
            return sysUser;

        } catch (Exception e) {
            log.error("市场系统用户验证异常: {}", username, e);
            return null;
        }
    }

    /**
     * 将Member对象转换为SysUser对象（用于统一SSO处理）
     */
    private SysUser convertMemberToSysUser(Member member) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(member.getMemberId());
        sysUser.setUserName(member.getMemberPhone()); // 使用手机号作为用户名
        sysUser.setNickName(member.getMemberRealName());
        sysUser.setPhonenumber(member.getMemberPhone());
        sysUser.setStatus(member.getMemberStatus());
        sysUser.setAvatar(member.getAvatar());
        // 设置用户类型标识，用于区分是Member用户
        sysUser.setRemark("MEMBER_USER");
        return sysUser;
    }

    /**
     * 获取当前请求的客户端ID
     */
    private String getCurrentClientId() {
        String clientId = CLIENT_ID_CONTEXT.get();
        return clientId != null ? clientId : "backend"; // 默认返回backend
    }

    /**
     * 通过手机号认证用户
     *
     * @param phone 手机号
     * @return 用户信息，验证失败返回null
     */
    private SysUser authenticateUserByPhone(String phone) {
        try {
            // 调用远程用户服务通过手机号获取用户信息
            R<LoginUser> userResult = remoteUserService.getUserInfoByPhone(phone, "inner");

            if (userResult == null || userResult.getData() == null) {
                log.warn("手机号对应的用户不存在: {}", phone);
                return null;
            }

            LoginUser loginUser = userResult.getData();
            SysUser user = loginUser.getSysUser();

            if (user == null) {
                log.warn("手机号对应的用户信息为空: {}", phone);
                return null;
            }

            // 检查用户状态
            if (!"0".equals(user.getStatus())) {
                log.warn("手机号对应的用户已被禁用: {}", phone);
                return null;
            }

            log.info("手机号 {} 对应的用户 {} 验证成功", phone, user.getUserName());
            return user;

        } catch (Exception e) {
            log.error("通过手机号验证用户异常: {}", phone, e);
            return null;
        }
    }
}
