{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\scienceFunding\\index.vue?vue&type=template&id=e710fdda&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\scienceFunding\\index.vue", "mtime": 1750385853720}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}