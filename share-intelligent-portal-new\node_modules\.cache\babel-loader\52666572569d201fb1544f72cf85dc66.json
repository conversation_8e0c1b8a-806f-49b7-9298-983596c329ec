{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_release", "require", "data", "form", "testingContent", "testingRequirements", "basicRequirements", "imageUrl", "companyName", "<PERSON><PERSON><PERSON>", "contactPhone", "rules", "required", "message", "trigger", "created", "userinfo", "JSON", "parse", "window", "sessionStorage", "getItem", "memberCompanyName", "contact", "memberRealName", "phone", "memberPhone", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "releaseDetection", "then", "res", "code", "$message", "success", "onCancel", "error", "$router", "go"], "sources": ["src/views/release/components/testingRequire.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"检测内容\" prop=\"testingContent\">\r\n        <el-input\r\n          v-model=\"form.testingContent\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"检测要求\" prop=\"testingRequirements\">\r\n        <el-input\r\n          v-model=\"form.testingRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"其他要求\" prop=\"basicRequirements\">\r\n        <el-input\r\n          v-model=\"form.basicRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"场景图片\" prop=\"imageUrl\">\r\n        <ImageUpload v-model=\"form.imageUrl\" :limit=\"1\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPerson\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPhone\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n          >取消</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseDetection } from \"@/api/release\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        testingContent: \"\",\r\n        testingRequirements: \"\",\r\n        basicRequirements: \"\",\r\n        imageUrl: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        testingContent: [\r\n          { required: true, message: \"检测内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        testingRequirements: [\r\n          { required: true, message: \"检测要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        basicRequirements: [\r\n          { required: true, message: \"其他要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        // 添加场景图片必填校验规则\r\n        imageUrl: [\r\n          { required: true, message: \"场景图片不能为空\", trigger: \"change\" },\r\n        ],\r\n        // contactPerson: [\r\n        //   { required: true, message: \"请先维护联系人\", trigger: \"blur\" },\r\n        // ],\r\n        // contactPhone: [\r\n        //   { required: true, message: \"请先维护联系方式\", trigger: \"blur\" },\r\n        // ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo && userinfo != \"null\") {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contact = userinfo.memberRealName;\r\n      this.form.phone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          releaseDetection(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel();\r\n            } else {\r\n              this.$message.error(\"发布失败\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAsEA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,YAAA;MACA;MACA;MACAC,KAAA;QACAP,cAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,mBAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,iBAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,IAAAL,QAAA,IAAAA,QAAA;MACA,KAAAb,IAAA,CAAAK,WAAA,GAAAQ,QAAA,CAAAM,iBAAA;MACA,KAAAnB,IAAA,CAAAoB,OAAA,GAAAP,QAAA,CAAAQ,cAAA;MACA,KAAArB,IAAA,CAAAsB,KAAA,GAAAT,QAAA,CAAAU,WAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,yBAAA,EAAAJ,KAAA,CAAA1B,IAAA,EAAA+B,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACAT,KAAA,CAAAU,QAAA;YACA;cACAV,KAAA,CAAAQ,QAAA,CAAAG,KAAA;YACA;UACA;QACA;MACA;IACA;IACAD,QAAA,WAAAA,SAAA;MACA,KAAAE,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}