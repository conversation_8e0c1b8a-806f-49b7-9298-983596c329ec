package com.ruoyi.sso.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * SSO统一用户对象
 * 
 * <AUTHOR>
 */
@ApiModel("SSO统一用户")
public class SSOUser extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty("用户ID")
    private Long userId;

    /** 用户名（通常是手机号） */
    @ApiModelProperty("用户名")
    private String username;

    /** 密码（BCrypt加密） */
    @ApiModelProperty("密码")
    private String password;

    /** 真实姓名 */
    @ApiModelProperty("真实姓名")
    private String realName;

    /** 手机号 */
    @ApiModelProperty("手机号")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty("邮箱")
    private String email;

    /** 头像地址 */
    @ApiModelProperty("头像地址")
    private String avatar;

    /** 状态（0正常 1停用） */
    @ApiModelProperty("状态")
    private String status;

    /** 用户类型（NORMAL普通用户 ADMIN管理员） */
    @ApiModelProperty("用户类型")
    private String userType;

    /** 主系统权限（0禁用 1启用） */
    @ApiModelProperty("主系统权限")
    private String backendEnabled;

    /** 市场系统权限（0禁用 1启用） */
    @ApiModelProperty("市场系统权限")
    private String marketEnabled;

    /** 最后登录时间 */
    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

    /** 最后登录IP */
    @ApiModelProperty("最后登录IP")
    private String lastLoginIp;

    /** 登录次数 */
    @ApiModelProperty("登录次数")
    private Integer loginCount;

    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getBackendEnabled() {
        return backendEnabled;
    }

    public void setBackendEnabled(String backendEnabled) {
        this.backendEnabled = backendEnabled;
    }

    public String getMarketEnabled() {
        return marketEnabled;
    }

    public void setMarketEnabled(String marketEnabled) {
        this.marketEnabled = marketEnabled;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public Integer getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }

    /**
     * 检查用户是否有访问指定系统的权限
     */
    public boolean hasSystemAccess(String clientId) {
        if ("backend".equals(clientId)) {
            return "1".equals(this.backendEnabled);
        } else if ("market".equals(clientId)) {
            return "1".equals(this.marketEnabled);
        }
        return false;
    }

    /**
     * 检查用户状态是否正常
     */
    public boolean isActive() {
        return "0".equals(this.status);
    }

    @Override
    public String toString() {
        return "SSOUser{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status='" + status + '\'' +
                ", userType='" + userType + '\'' +
                ", backendEnabled='" + backendEnabled + '\'' +
                ", marketEnabled='" + marketEnabled + '\'' +
                ", lastLoginTime=" + lastLoginTime +
                ", loginCount=" + loginCount +
                '}';
    }
}
