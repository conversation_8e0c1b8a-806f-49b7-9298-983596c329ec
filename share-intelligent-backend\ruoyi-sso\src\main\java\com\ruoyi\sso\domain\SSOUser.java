package com.ruoyi.sso.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * SSO统一用户对象
 *
 * <AUTHOR>
 */
@ApiModel("SSO统一用户")
public class SSOUser {

    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty("用户ID")
    private Long id;

    /** 用户名（通常是手机号） */
    @ApiModelProperty("用户名")
    private String username;

    /** 密码（BCrypt加密） */
    @ApiModelProperty("密码")
    private String password;

    /** 昵称 */
    @ApiModelProperty("昵称")
    private String nickname;

    /** 手机号 */
    @ApiModelProperty("手机号")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty("邮箱")
    private String email;

    /** 头像地址 */
    @ApiModelProperty("头像地址")
    private String avatar;

    /** 状态（1正常 0停用） */
    @ApiModelProperty("状态")
    private Integer status;

    /** 最后登录时间 */
    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

    /** 登录次数 */
    @ApiModelProperty("登录次数")
    private Integer loginCount;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public Integer getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 检查用户状态是否正常
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(this.status);
    }

    @Override
    public String toString() {
        return "SSOUser{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", nickname='" + nickname + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status=" + status +
                ", lastLoginTime=" + lastLoginTime +
                ", loginCount=" + loginCount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
