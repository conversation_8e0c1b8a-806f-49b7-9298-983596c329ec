{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue", "mtime": 1750385853718}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/compositeExhibitionHall", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"left\">\r\n      <div class=\"hall-bg\">\r\n        <img class=\"hall-img\" :src=\"currentItem.url\" alt=\"\" />\r\n        <div class=\"card\">\r\n          <div class=\"card-top\">\r\n            <div class=\"card-left\">\r\n              <img class=\"card-img\" :src=\"currentItem.imageUrl\" alt=\"\" />\r\n              <el-button type=\"primary\" :icon=\"videoIcon\" @click=\"handlePlayAudio\">播放音频</el-button>\r\n            </div>\r\n            <div class=\"card-right\">\r\n              <div class=\"card-title\">{{ currentItem.materialName }}</div>\r\n              <div class=\"card-content\">{{ currentItem.description }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-bottom\" v-if=\"materialList.length > 0\">\r\n            <div class=\"card-item\" v-for=\"(item, index) in materialList\" @click=\"toFactory(item)\" :key=\"index\">\r\n              <div class=\"card-item-title\">{{ item.productName }}</div>\r\n              <img :src=\"item.productImageUrl\" alt=\"\">\r\n            </div>\r\n          </div>\r\n          <div class=\"card-empty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"hall-list\">\r\n        <div class=\"hall-item\" v-for=\"(item, index) in deviceMenuList\" @click=\"handleClick(item)\" :key=\"index\">\r\n          <img :src=\"item.url\" alt=\"\" />\r\n          <div class=\"hall-title\">{{ item.materialName }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getCompositeExhibitionHallList } from '@/api/compositeExhibitionHall'\r\nimport { listSysProduct } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceSharing\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      bgUrl: [\r\n        require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n      ],\r\n      deviceMenuList: [\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n      ],\r\n      currentItem: {\r\n        url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        imageUrl: require(\"@/assets/compositeExhibitionHall/hall_img.png\"),\r\n        materialName: \"化工环保展厅\",\r\n        description: '暂无介绍',\r\n      },\r\n      videoIcon: 'el-icon-video-play',\r\n      isPlay: false,\r\n      materialList: [\r\n        {\r\n          productName: '玻璃钢槽式电缆桥架',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material1.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢格栅',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material2.png\"),\r\n        },\r\n        {\r\n          productName: '钢筋钢板',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material3.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢管道',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material4.png\"),\r\n        }\r\n      ],\r\n      productId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.productId = this.$route.query.productId ? this.$route.query.productId : '';\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      getCompositeExhibitionHallList(params).then((response) => {\r\n        if (response.code === 200 && response.rows.length > 0) {\r\n          this.deviceMenuList = response.rows;\r\n          this.deviceMenuList.sort((a, b) => a.sortOrder - b.sortOrder);\r\n          // 按顺序循环bgUrl\r\n          this.deviceMenuList.forEach((item, index) => {\r\n            item.url = this.bgUrl[index % this.bgUrl.length];\r\n          })\r\n          if (this.productId) {\r\n            getCompositeExhibitionHallList({\r\n              pageNum: 1,\r\n              pageSize: 1000,\r\n              productId: this.productId,\r\n            }).then((res) => {\r\n              if (res.code === 200 && res.rows.length > 0) {\r\n                let index = this.deviceMenuList.findIndex(item => item.id === res.rows[0].id);\r\n                this.handleClick(this.deviceMenuList[index]);\r\n              }\r\n            })\r\n          } else {\r\n            this.handleClick(this.deviceMenuList[0]);\r\n          }\r\n          // this.handleClick(this.deviceMenuList[0]);\r\n        }\r\n      });\r\n    },\r\n    handleClick(item) {\r\n      console.log(item, 'item')\r\n      this.currentItem.url = item.url;\r\n      this.currentItem.materialName = item.materialName;\r\n      this.currentItem.imageUrl = item.imageUrl ? item.imageUrl : require(\"@/assets/compositeExhibitionHall/hall_img.png\");\r\n      this.currentItem.description = item.description ? item.description : '暂无介绍';\r\n      // if (item.imageList) {\r\n      //   this.materialList = item.imageList.split(',').map(item => {\r\n      //     let name = item.split('/')[item.split('/').length - 1].split('_');\r\n      //     name.pop();\r\n      //     return {\r\n      //       name: name.join('_'),\r\n      //       img: item\r\n      //     }\r\n      //   })\r\n      // } else {\r\n      //   this.materialList = []\r\n      // }\r\n      listSysProduct({\r\n        exhibitionHallId: item.id,\r\n        pageNum: 1,\r\n        pageSize: 1000,\r\n      }).then((res) => {\r\n        if (res.code === 200) {\r\n          this.materialList = res.rows || [];\r\n        }\r\n      });\r\n    },\r\n    handlePlayAudio() {\r\n      if (this.isPlay) {\r\n        this.videoIcon = 'el-icon-video-play'\r\n        this.isPlay = false\r\n      } else {\r\n        this.videoIcon = 'el-icon-video-pause'\r\n        this.isPlay = true\r\n      }\r\n    },\r\n    toFactory(item) {\r\n      this.$router.push({\r\n        path: '/manufacturingSharing',\r\n        query: {\r\n          index: 2,\r\n          page: 2,\r\n          productId: item.productId,\r\n        }\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  height: auto;\r\n  padding: 40px;\r\n  box-sizing: border-box;\r\n  background-color: #F7FBFF;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .left {\r\n    width: 80%;\r\n    position: relative;\r\n\r\n    .hall-img {\r\n      width: 100%;\r\n    }\r\n\r\n    .card {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      background-color: #F7FBFF;\r\n      border-radius: 10px;\r\n      padding: 40px;\r\n      box-sizing: border-box;\r\n      width: 84%;\r\n      height: 74%;\r\n\r\n      .card-top {\r\n        height: 52%;\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        .card-left {\r\n          width: 29%;\r\n\r\n          .card-img {\r\n            width: 100%;\r\n            margin-bottom: 1vw;\r\n          }\r\n        }\r\n\r\n        .card-right {\r\n          width: 65%;\r\n\r\n          .card-title {\r\n            font-weight: 500;\r\n            font-size: 1.1vw;\r\n            color: #222222;\r\n            margin-bottom: 0.8vw;\r\n          }\r\n\r\n          .card-content {\r\n            font-weight: 400;\r\n            font-size: 0.7vw;\r\n            line-height: 1vw;\r\n            color: #787878;\r\n            overflow-y: auto;\r\n            box-sizing: border-box;\r\n            height: 90%;\r\n            overflow: auto;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-bottom {\r\n        height: 48%;\r\n        margin-top: 2vw;\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        overflow-x: auto;\r\n\r\n        .card-item {\r\n          min-width: 22%;\r\n          margin: 0.5vw;\r\n          margin-right: 1.3vw;\r\n\r\n          .card-item-title {\r\n            width: 100%;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            font-weight: 400;\r\n            font-size: 0.9vw;\r\n            color: #222222;\r\n            margin-bottom: 14px;\r\n          }\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 75%;\r\n          }\r\n        }\r\n        \r\n      }\r\n\r\n\r\n\r\n      .card-empty {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 18%;\r\n    height: 46vw;\r\n    overflow-y: auto;\r\n    background-color: transparent;\r\n\r\n    .hall-list {\r\n      width: 100%;\r\n\r\n      .hall-item {\r\n        width: 100%;\r\n        position: relative;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 100%;\r\n          margin-bottom: 1.5vw;\r\n        }\r\n\r\n        .hall-title {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          font-weight: 500;\r\n          font-size: 1.1vw;\r\n          color: #000000;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}