19:30:27.799 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:30:28.976 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0
19:30:29.085 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 55 ms to scan 1 urls, producing 3 keys and 6 values 
19:30:29.131 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
19:30:29.143 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:30:29.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 234 ms to scan 293 urls, producing 0 keys and 0 values 
19:30:29.391 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
19:30:29.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
19:30:29.421 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
19:30:29.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 208 ms to scan 293 urls, producing 0 keys and 0 values 
19:30:29.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:30:29.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/465869765
19:30:29.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/2070830098
19:30:29.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:30:29.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:30:29.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:30:31.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750332631162_127.0.0.1_51182
19:30:31.447 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Notify connected event to listeners.
19:30:31.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:31.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a3569d9-3f59-472d-aa6e-c1ec6a193845_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/388489274
19:30:31.591 [main] INFO  c.r.s.RuoYiShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:30:35.021 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
19:30:35.036 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
19:30:35.174 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
19:30:35.203 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
19:30:35.204 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
19:30:35.387 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
19:30:35.701 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
19:30:35.701 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
19:30:35.731 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
19:30:35.732 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
19:30:35.748 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
19:30:35.750 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
19:30:35.750 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
19:30:37.607 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9707"]
19:30:37.608 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:30:37.608 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:30:37.914 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:30:38.964 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
19:30:39.646 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
19:30:49.998 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:30:50.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8586d9c2-6455-49b9-bb75-eae1ae70a707
19:30:50.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] RpcClient init label, labels = {module=naming, source=sdk}
19:30:50.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:30:50.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:30:50.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:30:50.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:30:50.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750332650780_127.0.0.1_51310
19:30:50.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Notify connected event to listeners.
19:30:50.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:50.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/388489274
19:30:55.199 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9707"]
19:30:55.265 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-shop 192.168.0.68:9707 register finished
19:30:55.594 [main] INFO  c.r.s.RuoYiShopApplication - [logStarted,61] - Started RuoYiShopApplication in 28.601 seconds (JVM running for 30.194)
19:30:55.734 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop-dev.yaml, group=DEFAULT_GROUP
19:30:55.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop, group=DEFAULT_GROUP
19:30:55.736 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop.yaml, group=DEFAULT_GROUP
19:30:55.825 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Receive server push request, request = NotifySubscriberRequest, requestId = 245
19:30:55.863 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8586d9c2-6455-49b9-bb75-eae1ae70a707] Ack server push request, request = NotifySubscriberRequest, requestId = 245
19:30:56.498 [RMI TCP Connection(6)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
