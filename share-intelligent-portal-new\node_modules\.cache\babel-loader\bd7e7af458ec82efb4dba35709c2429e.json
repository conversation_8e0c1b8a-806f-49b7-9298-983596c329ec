{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue", "mtime": 1750329471822}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9sb2dpbiA9IHJlcXVpcmUoIkAvYXBpL2xvZ2luIik7CnZhciBfdmVyaWZpY2F0aW9uQ29kZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL3ZlcmlmaWNhdGlvbkNvZGUvIikpOwp2YXIgX2FncmVlbWVudERpYWxvZyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9hZ3JlZW1lbnREaWFsb2ciKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBjb21wb25lbnRzOiB7CiAgICBWZXJpZmljYXRpb25Db2RlOiBfdmVyaWZpY2F0aW9uQ29kZS5kZWZhdWx0LAogICAgQWdyZWVtZW50RGlhbG9nOiBfYWdyZWVtZW50RGlhbG9nLmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtOiB7fSwKICAgICAgY29kZVVybDogIiIsCiAgICAgIC8v5Zu+5b2i6aqM6K+B56CB5Zu+54mHCiAgICAgIHR5cGU6ICJjb2RlIiwKICAgICAgLy/otKblj7fnsbvlnosgKGNvZGXvvJrpqozor4HnoIHnmbvlvZUgIGFjY291bnQ66LSm5Y+35a+G56CB55m75b2VICBzZXQ65a+G56CB6K6+572uKQogICAgICBhZ3JlZW1lbnQ6IDAsCiAgICAgIC8v5Y2P6K6uCiAgICAgIHByb3RvY29sc1Zpc2libGU6IGZhbHNlCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdEZvcm0oKTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBydWxlczogZnVuY3Rpb24gcnVsZXMoKSB7CiAgICAgIHZhciBydWxlcyA9IHsKICAgICAgICB1c2VybmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeaJi+acuuWPtyIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9LCB7CiAgICAgICAgICBtaW46IDExLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpTEx5L2N5omL5py65Y+3IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNtc0NvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXpqozor4HnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIKICAgICAgICB9LCB7CiAgICAgICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWUubGVuZ3RoICE9PSA2KSB7CiAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpqozor4HnoIHmoLzlvI/kuI3mraPnoa4iKSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpemqjOivgeeggSIKICAgICAgICB9XSwKICAgICAgICBwYXNzd29yZDE6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXnoa7orqTlr4bnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfSwgewogICAgICAgICAgbWluOiA2LAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpTYtMTHkvY3noa7orqTlr4bnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfSwgewogICAgICAgICAgbWF4OiAxMSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaU2LTEx5L2N56Gu6K6k5a+G56CBIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH0sIHsKICAgICAgICAgIHZhbGlkYXRvcjogdGhpcy52YWxpZGF0b3JQYXNzd29yZAogICAgICAgIH1dCiAgICAgIH07CiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJhY2NvdW50IikgewogICAgICAgIHJ1bGVzLnBhc3N3b3JkID0gW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeWvhueggSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9LCB7CiAgICAgICAgICBtaW46IDYsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWlNi0xMeS9jeWvhueggSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9LCB7CiAgICAgICAgICBtYXg6IDExLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpTYtMTHkvY3lr4bnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV07CiAgICAgIH0gZWxzZSBpZiAodGhpcy50eXBlID09PSAic2V0IikgewogICAgICAgIHJ1bGVzLnBhc3N3b3JkID0gW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeWvhueggSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9LCB7CiAgICAgICAgICBtaW46IDYsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWlNi0xMeS9jeWvhueggSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9LCB7CiAgICAgICAgICBtYXg6IDExLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpTYtMTHkvY3lr4bnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfSwgewogICAgICAgICAgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRvclBhc3N3b3JkCiAgICAgICAgfV07CiAgICAgIH0KICAgICAgcmV0dXJuIHJ1bGVzOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICJmb3JtLnBhc3N3b3JkIjogZnVuY3Rpb24gZm9ybVBhc3N3b3JkKCkgewogICAgICBpZiAodGhpcy5mb3JtLnBhc3N3b3JkMSAmJiB0aGlzLmZvcm0ucGFzc3dvcmQgPT09IHRoaXMuZm9ybS5wYXNzd29yZDEpIHsKICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGVGaWVsZCgicGFzc3dvcmQxIik7CiAgICAgIH0KICAgIH0sCiAgICAiZm9ybS5wYXNzd29yZDEiOiBmdW5jdGlvbiBmb3JtUGFzc3dvcmQxKCkgewogICAgICBpZiAodGhpcy5mb3JtLnBhc3N3b3JkMSAmJiB0aGlzLmZvcm0ucGFzc3dvcmQgPT09IHRoaXMuZm9ybS5wYXNzd29yZDEpIHsKICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGVGaWVsZCgicGFzc3dvcmQiKTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaW5pdEZvcm06IGZ1bmN0aW9uIGluaXRGb3JtKCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIC8v6LSm5Y+3CiAgICAgICAgc21zQ29kZTogIiIsCiAgICAgICAgLy/nn63kv6Hpqozor4HnoIEKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgLy/lr4bnoIEKICAgICAgICBwYXNzd29yZDE6ICIiLAogICAgICAgIC8v56Gu6K6k5a+G56CBCiAgICAgICAgY29kZTogIiIsCiAgICAgICAgLy/lm77lvaLpqozor4HnoIEKICAgICAgICB1dWlkOiAiIgogICAgICB9OwogICAgfSwKICAgIC8vIOWIh+aNoueZu+W9leaWueW8jwogICAgc3dpdGNoTG9naW46IGZ1bmN0aW9uIHN3aXRjaExvZ2luKHZhbCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAvLyDnp7vpmaTlr4bnoIHnmbvlvZXml7bojrflj5blm77lvaLpqozor4HnoIHnmoTpgLvovpHvvIzlm6DkuLrmlrDnmoTmjqXlj6PkuI3pnIDopoHpqozor4HnoIEKICAgICAgLy8gaWYgKHZhbCA9PT0gImFjY291bnQiKSB7CiAgICAgIC8vICAgdGhpcy5nZXRDb2RlSW1nKCk7CiAgICAgIC8vIH0KICAgICAgdGhpcy5pbml0Rm9ybSgpOwogICAgICB0aGlzLnR5cGUgPSB2YWw7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy4kcmVmcy5mb3JtLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W5Zu+5b2i6aqM6K+B56CBCiAgICBnZXRDb2RlSW1nOiBmdW5jdGlvbiBnZXRDb2RlSW1nKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgKDAsIF9sb2dpbi5nZXRDb2RlSW1nKSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi5jb2RlVXJsID0gImRhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCwiICsgcmVzLmltZzsKICAgICAgICBfdGhpczIuZm9ybS51dWlkID0gcmVzLnV1aWQ7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWvhueggeagoemqjAogICAgdmFsaWRhdG9yUGFzc3dvcmQ6IGZ1bmN0aW9uIHZhbGlkYXRvclBhc3N3b3JkKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICB2YXIgcGFzc3dvcmQgPSB0aGlzLmZvcm0ucGFzc3dvcmQ7CiAgICAgIHZhciBwYXNzd29yZDEgPSB0aGlzLmZvcm0ucGFzc3dvcmQxOwogICAgICBpZiAocGFzc3dvcmQgJiYgcGFzc3dvcmQxICYmIHBhc3N3b3JkICE9PSBwYXNzd29yZDEpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWvhueggei+k+WFpeS4jeS4gOiHtO+8jOivt+mHjeaWsOi+k+WFpSIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9LAogICAgYmVmb3JlU2VuZENvZGU6IGZ1bmN0aW9uIGJlZm9yZVNlbmRDb2RlKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgICBfdGhpczMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKCJ1c2VybmFtZSIsIGZ1bmN0aW9uIChlcnJvck1lc3NhZ2UpIHsKICAgICAgICAgIGVycm9yTWVzc2FnZSA/IHJlamVjdCgpIDogcmVzb2x2ZSgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiZPlvIDmnI3liqHljY/orq7lvLnnqpcKICAgIHZpZXdQcm90b2NvbHM6IGZ1bmN0aW9uIHZpZXdQcm90b2NvbHMoKSB7CiAgICAgIHRoaXMucHJvdG9jb2xzVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy8g55m75b2VCiAgICBoYW5kbGVMb2dpbjogZnVuY3Rpb24gaGFuZGxlTG9naW4oKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBpZiAodGhpcy5hZ3JlZW1lbnQgIT09IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICLor7fpmIXor7vlubblkIzmhI/jgIrmnI3liqHljY/orq7jgIsiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHZhciBvYmogPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpczQuZm9ybSksIHt9LCB7CiAgICAgICAgICAgIHR5cGU6IF90aGlzNC50eXBlCiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIF90aGlzNC4kc3RvcmUuZGlzcGF0Y2goIkxvZ2luIiwgb2JqKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXM0LiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgcGF0aDogX3RoaXM0LnJlZGlyZWN0IHx8ICIvIgogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIC8vIOenu+mZpOWvhueggeeZu+W9leWksei0peaXtumHjeaWsOiOt+WPluWbvuW9oumqjOivgeeggeeahOmAu+i+kQogICAgICAgICAgICAvLyBpZiAodGhpcy50eXBlID09PSAiYWNjb3VudCIpIHsKICAgICAgICAgICAgLy8gICB0aGlzLmZvcm0uY29kZSA9ICIiOwogICAgICAgICAgICAvLyAgIHRoaXMuZ2V0Q29kZUltZygpOwogICAgICAgICAgICAvLyAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgLy8gICAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCJjb2RlIik7CiAgICAgICAgICAgIC8vICAgfSk7CiAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgX3RoaXM0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgYXV0aGVudGljYXRpb246IGZ1bmN0aW9uIGF1dGhlbnRpY2F0aW9uKCkgewogICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICJodHRwczovL3F5emhmdy5jaGVuZ3lhbmcuZ292LmNuL3Nzby9sb2dpbj9yZWRpcmVjdFVybD1odHRwczovL3F5ZncuY2hlbmd5YW5nLmdvdi5jbi9pbmRleCI7CiAgICAgIC8vIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0KICAgICAgLy8gICAiaHR0cHM6Ly9xeXpoZncuY2hlbmd5YW5nLmdvdi5jbi9zc28vbG9naW4/cmVkaXJlY3RVcmw9aHR0cDovL2xvY2FsaG9zdC9pbmRleCI7CiAgICB9LAogICAgdG9JbmRleDogZnVuY3Rpb24gdG9JbmRleCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvIgogICAgICB9KTsKICAgIH0sCiAgICAvLyBTU0/nmbvlvZUKICAgIGhhbmRsZVNTT0xvZ2luOiBmdW5jdGlvbiBoYW5kbGVTU09Mb2dpbigpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgICgwLCBfbG9naW4uZ2V0U1NPTG9naW5VcmwpKHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gKyB0aGlzLiRyb3V0ZS5mdWxsUGF0aCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5sb2dpblVybCkgewogICAgICAgICAgLy8g6Lez6L2s5YiwU1NP55m75b2V6aG16Z2iCiAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHJlc3BvbnNlLmRhdGEubG9naW5Vcmw7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigi6I635Y+WU1NP55m75b2V5Zyw5Z2A5aSx6LSlIik7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCJTU0/nmbvlvZXlpLHotKU6IiwgZXJyb3IpOwogICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigiU1NP55m75b2V5pyN5Yqh5byC5bi4Iik7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_login", "require", "_verificationCode", "_interopRequireDefault", "_agreementDialog", "components", "VerificationCode", "AgreementDialog", "data", "form", "codeUrl", "type", "agreement", "protocolsVisible", "created", "initForm", "computed", "rules", "username", "required", "message", "trigger", "min", "smsCode", "validator", "rule", "value", "callback", "length", "Error", "code", "password1", "max", "validatorPassword", "password", "watch", "formPassword", "$refs", "validateField", "formPassword1", "methods", "uuid", "switchLogin", "val", "_this", "$nextTick", "clearValidate", "getCodeImg", "_this2", "then", "res", "img", "beforeSendCode", "_this3", "Promise", "resolve", "reject", "errorMessage", "viewProtocols", "handleLogin", "_this4", "$message", "validate", "valid", "obj", "_objectSpread2", "default", "loading", "$store", "dispatch", "$router", "push", "path", "redirect", "catch", "authentication", "window", "location", "href", "toIndex", "handleSSOLogin", "_this5", "getSSOLoginUrl", "origin", "$route", "fullPath", "response", "loginUrl", "error", "console"], "sources": ["src/views/login.vue"], "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-28 09:15:15\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-15 14:46:27\r\n-->\r\n<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"title\" @click=\"toIndex\">\r\n      <div class=\"titLeft\">\r\n        <img src=\"@/assets/images/home/<USER>\" alt=\"\" />\r\n      </div>\r\n      <div class=\"titRight\">易复材共享智造工业互联网平台</div>\r\n    </div>\r\n    <div class=\"login_content\">\r\n      <div class=\"left_img\"></div>\r\n      <div\r\n        :class=\"{\r\n          'login-content-code': type === 'code',\r\n          'login-content-account': type === 'account',\r\n          'login-content-set': type === 'set',\r\n        }\"\r\n      >\r\n        <div class=\"login-info\">\r\n          <!-- <div class=\"login-project-name\">星碳生态平台</div> -->\r\n          <div class=\"login-box\">\r\n            <div class=\"login-tab\">\r\n              <div\r\n                class=\"tabStyle\"\r\n                v-show=\"type == 'account' || type == 'code'\"\r\n              >\r\n                <div\r\n                  class=\"tab_left\"\r\n                  :style=\"\r\n                    type == 'code'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('code')\"\r\n                >\r\n                  验证码登录\r\n                </div>\r\n                <div\r\n                  class=\"tab_right\"\r\n                  :style=\"\r\n                    type == 'account'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('account')\"\r\n                >\r\n                  密码登录\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-show=\"type == 'set'\"\r\n                style=\"width: 100%; text-align: center\"\r\n              >\r\n                设置密码\r\n                <!-- {{\r\n                  type === \"account\"\r\n                    ? \"账号密码登录\"\r\n                    : type === \"code\"\r\n                    ? \"验证码登录\"\r\n                    : \"设置密码\"\r\n                }} -->\r\n              </div>\r\n            </div>\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\">\r\n              <el-form-item prop=\"username\">\r\n                <el-input\r\n                  v-model=\"form.username\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入手机号\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/mobile.png\"\r\n                    alt=\"\"\r\n                    style=\"width: 12px; height: 16px; margin: 12px\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 验证码登录 -->\r\n              <el-form-item\r\n                v-if=\"type !== 'account'\"\r\n                prop=\"smsCode\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <verification-code\r\n                  v-model=\"form.smsCode\"\r\n                  :mobile=\"{ phone: form.username }\"\r\n                  :before-send-code=\"beforeSendCode\"\r\n                ></verification-code>\r\n              </el-form-item>\r\n              <!-- 账号密码登录、密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'account' || type === 'set'\"\r\n                prop=\"password\"\r\n                class=\"form-item-password-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 账号密码登录 -->\r\n              <!-- <el-form-item\r\n                v-if=\"type === 'account'\"\r\n                prop=\"code\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.code\"\r\n                  placeholder=\"请输入验证码\"\r\n                  auto-complete=\"off\"\r\n                  style=\"width: 63%\"\r\n                  class=\"form-input-img-style\"\r\n                  :maxlength=\"200\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/imgCode.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n                <div class=\"login-code\">\r\n                  <img\r\n                    class=\"login-code-img\"\r\n                    :src=\"codeUrl\"\r\n                    @click=\"getCodeImg\"\r\n                  />\r\n                </div>\r\n              </el-form-item> -->\r\n              <!-- 密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'set'\"\r\n                prop=\"password1\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password1\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请确认密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-checkbox\r\n                  v-model=\"agreement\"\r\n                  :true-label=\"1\"\r\n                  :false-label=\"0\"\r\n                >\r\n                  <span class=\"login-agreement-text\">已阅读并同意</span>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    class=\"login-agreement-btn\"\r\n                    @click=\"viewProtocols\"\r\n                  >\r\n                    《服务协议》\r\n                  </el-button>\r\n                </el-checkbox>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"medium\"\r\n              class=\"button-area\"\r\n              @click=\"handleLogin\"\r\n              >登录</el-button\r\n            >\r\n            <el-button\r\n              type=\"info\"\r\n              size=\"medium\"\r\n              class=\"button-area sso-button\"\r\n              @click=\"handleSSOLogin\"\r\n              >SSO统一登录</el-button\r\n            >\r\n            <div class=\"button-switch-box\">\r\n              <el-button\r\n                v-if=\"type === 'account'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('set')\"\r\n                >设置密码</el-button\r\n              >\r\n              <el-button\r\n                v-if=\"type == 'set'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('account')\"\r\n                >密码登录</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务协议弹窗 -->\r\n    <agreement-dialog :visible.sync=\"protocolsVisible\"></agreement-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, getSSOLoginUrl } from \"@/api/login\";\r\nimport VerificationCode from \"@/components/verificationCode/\";\r\nimport AgreementDialog from \"./agreementDialog\";\r\n\r\nexport default {\r\n  components: {\r\n    VerificationCode,\r\n    AgreementDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      codeUrl: \"\", //图形验证码图片\r\n      type: \"code\", //账号类型 (code：验证码登录  account:账号密码登录  set:密码设置)\r\n      agreement: 0, //协议\r\n      protocolsVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.initForm();\r\n  },\r\n  computed: {\r\n    rules() {\r\n      let rules = {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n          { min: 11, message: \"请输入11位手机号\", trigger: \"blur\" },\r\n        ],\r\n        smsCode: [\r\n          { required: true, message: \"请输入验证码\", trigger: \"change\" },\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (!value) {\r\n                callback();\r\n              } else if (value.length !== 6) {\r\n                callback(new Error(\"验证码格式不正确\"));\r\n              } else {\r\n                callback();\r\n              }\r\n            },\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n        password1: [\r\n          { required: true, message: \"请输入确认密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ],\r\n      };\r\n      if (this.type === \"account\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n        ];\r\n      } else if (this.type === \"set\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ];\r\n      }\r\n      return rules;\r\n    },\r\n  },\r\n  watch: {\r\n    \"form.password\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password1\");\r\n      }\r\n    },\r\n    \"form.password1\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password\");\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        username: \"\", //账号\r\n        smsCode: \"\", //短信验证码\r\n        password: \"\", //密码\r\n        password1: \"\", //确认密码\r\n        code: \"\", //图形验证码\r\n        uuid: \"\",\r\n      };\r\n    },\r\n    // 切换登录方式\r\n    switchLogin(val) {\r\n      // 移除密码登录时获取图形验证码的逻辑，因为新的接口不需要验证码\r\n      // if (val === \"account\") {\r\n      //   this.getCodeImg();\r\n      // }\r\n      this.initForm();\r\n      this.type = val;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.clearValidate();\r\n      });\r\n    },\r\n    // 获取图形验证码\r\n    getCodeImg() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.form.uuid = res.uuid;\r\n      });\r\n    },\r\n    // 密码校验\r\n    validatorPassword(rule, value, callback) {\r\n      let password = this.form.password;\r\n      let password1 = this.form.password1;\r\n      if (password && password1 && password !== password1) {\r\n        callback(new Error(\"密码输入不一致，请重新输入\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    beforeSendCode() {\r\n      return new Promise((resolve, reject) => {\r\n        this.$refs.form.validateField(\"username\", (errorMessage) => {\r\n          errorMessage ? reject() : resolve();\r\n        });\r\n      });\r\n    },\r\n    // 打开服务协议弹窗\r\n    viewProtocols() {\r\n      this.protocolsVisible = true;\r\n    },\r\n    // 登录\r\n    handleLogin() {\r\n      if (this.agreement !== 1) {\r\n        this.$message({\r\n          message: \"请阅读并同意《服务协议》\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          let obj = { ...this.form, type: this.type };\r\n          this.loading = true;\r\n          this.$store\r\n            .dispatch(\"Login\", obj)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              // 移除密码登录失败时重新获取图形验证码的逻辑\r\n              // if (this.type === \"account\") {\r\n              //   this.form.code = \"\";\r\n              //   this.getCodeImg();\r\n              //   this.$nextTick(() => {\r\n              //     this.$refs.form.clearValidate(\"code\");\r\n              //   });\r\n              // }\r\n              this.loading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    authentication() {\r\n      window.location.href =\r\n        \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n      // window.location.href =\r\n      //   \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=http://localhost/index\";\r\n    },\r\n    toIndex(){\r\n      this.$router.push({ path: \"/\" })\r\n    },\r\n    // SSO登录\r\n    handleSSOLogin() {\r\n      getSSOLoginUrl(window.location.origin + this.$route.fullPath)\r\n        .then(response => {\r\n          if (response.code === 200 && response.data && response.data.loginUrl) {\r\n            // 跳转到SSO登录页面\r\n            window.location.href = response.data.loginUrl;\r\n          } else {\r\n            this.$message.error(\"获取SSO登录地址失败\");\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"SSO登录失败:\", error);\r\n          this.$message.error(\"SSO登录服务异常\");\r\n        });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url(\"../assets/login/background.png\") no-repeat;\r\n  background-size: 100% 100%;\r\n  padding-top: calc((100vh - 580px) / 2);\r\n  position: relative;\r\n  .title {\r\n    display: flex;\r\n    position: absolute;\r\n    left: 2%;\r\n    top: 72px;\r\n    width: 15%;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .titLeft {\r\n      width: 60px;\r\n      height: 50px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    .titRight {\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      color: #000000;\r\n      margin-left: 1%;\r\n      width: 210px;\r\n    }\r\n  }\r\n  .left_img {\r\n    width: 50%;\r\n    height: 367px;\r\n    position: absolute;\r\n    top: 95px;\r\n    left: 3.3%;\r\n    background: url(\"../assets/login/image.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login_content {\r\n    position: relative;\r\n    width: 62.5%;\r\n    height: 580px;\r\n    margin-left: 18.75%;\r\n    // margin-top: 100px;\r\n    // left: 18.75%;\r\n    // top: calc((100vh - 580px) / 2);\r\n    background: url(\"../assets/login/background1.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login-logo {\r\n    width: 139px;\r\n    height: 48px;\r\n    position: absolute;\r\n    top: 4.07%;\r\n    left: 2.6%;\r\n  }\r\n  .login-background1 {\r\n    position: absolute;\r\n    top: 24.07%;\r\n    left: 9.01%;\r\n    width: 1638px;\r\n    height: 613px;\r\n  }\r\n  .login-content-code {\r\n    position: absolute;\r\n    top: calc((100% - 400px) / 2);\r\n    // top: 28.33%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 470px;\r\n  }\r\n  .login-content-account {\r\n    position: absolute;\r\n    top: calc((100% - 465px) / 2);\r\n    // top: 25.37%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 535px;\r\n  }\r\n  .login-content-set {\r\n    position: absolute;\r\n    top: calc((100% - 530px) / 2);\r\n    // top: 22.4%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 600px;\r\n  }\r\n  .login-background2-code {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 22px;\r\n  }\r\n  .login-background2-account {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 49px;\r\n  }\r\n  .login-background2-set {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 77px;\r\n  }\r\n  .login-info {\r\n    width: 393px;\r\n    // width: 464px;\r\n    .login-project-name {\r\n      height: 70px;\r\n      font-size: 22px;\r\n      font-family: AlibabaPuHuiTi_2_85_Bold;\r\n      color: #fff;\r\n      line-height: 70px;\r\n      text-align: center;\r\n      // margin-bottom: 36px;\r\n      // background: rgb(41, 92, 233);\r\n      background: linear-gradient(\r\n        to right,\r\n        rgb(83, 140, 241),\r\n        rgb(41, 92, 233)\r\n      );\r\n      border-top-left-radius: 10px;\r\n      border-top-right-radius: 10px;\r\n    }\r\n    .login-box {\r\n      width: 100%;\r\n      background: #fff;\r\n      box-shadow: 0px 10px 30px 0px #********;\r\n      padding: 0 32px;\r\n      .login-tab {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 24px;\r\n        text-align: center;\r\n        padding: 40px 0 32px;\r\n        .tabStyle {\r\n          display: flex;\r\n          justify-content: center;\r\n          width: 100%;\r\n          .tab_left {\r\n            width: 91px;\r\n            height: 35px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n          .tab_right {\r\n            margin-left: 60px;\r\n            width: 73px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .form-item-password-style {\r\n        margin-top: 24px;\r\n      }\r\n      .form-item-style {\r\n        margin: 24px 0 15px;\r\n        .login-code {\r\n          width: 33%;\r\n          height: 40px;\r\n          float: right;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            cursor: pointer;\r\n            vertical-align: middle;\r\n          }\r\n        }\r\n      }\r\n      .input-icon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin: 12px;\r\n      }\r\n      .login-agreement-text {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 14px;\r\n      }\r\n      .login-agreement-btn {\r\n        color: #21c9b8;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n      }\r\n      .button-area {\r\n        margin: 7px 0 26px;\r\n        width: 330px;\r\n        height: 40px;\r\n        background: #21c9b8;\r\n        border-color: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n      }\r\n      .sso-button {\r\n        background: #409eff !important;\r\n        border-color: #409eff !important;\r\n        margin-top: 12px !important;\r\n        margin-bottom: 12px !important;\r\n      }\r\n      .button-switch-box {\r\n        display: flex;\r\n        justify-content: right;\r\n        align-items: center;\r\n        padding: 0 0 18px 69px;\r\n        .button-switch-style {\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.login-container {\r\n  .login-box {\r\n    .form-input-style {\r\n      .el-input__inner {\r\n        // width: 400px;\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-form-item__error {\r\n      background: url(\"../assets/login/warningIcon.png\") no-repeat;\r\n      background-size: 16px 16px;\r\n      padding-left: 18px;\r\n      padding-top: 3px;\r\n      margin-top: 2px;\r\n      height: 16px;\r\n      color: #f05642;\r\n    }\r\n    .form-input-img-style {\r\n      .el-input__inner {\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-checkbox__inner {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-top: -1px;\r\n      border-color: #d9d9d9;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner,\r\n    .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n      background-color: #21c9b8;\r\n      border-color: #21c9b8;\r\n    }\r\n    .el-checkbox__inner:hover {\r\n      border-color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAsOA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,UAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,eAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MAAA;MACAC,IAAA;MAAA;MACAC,SAAA;MAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,IAAAA,KAAA;QACAC,QAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,OAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAG,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA;YACA,WAAAD,KAAA,CAAAE,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACAF,QAAA;YACA;UACA;UACAN,OAAA;QACA,EACA;QACAS,IAAA;UAAAX,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA;QACAW,SAAA,GACA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAW,GAAA;UAAAZ,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,SAAA,OAAAS;QAAA;MAEA;MACA,SAAAtB,IAAA;QACAM,KAAA,CAAAiB,QAAA,IACA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAW,GAAA;UAAAZ,OAAA;UAAAC,OAAA;QAAA,EACA;MACA,gBAAAV,IAAA;QACAM,KAAA,CAAAiB,QAAA,IACA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAW,GAAA;UAAAZ,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,SAAA,OAAAS;QAAA,EACA;MACA;MACA,OAAAhB,KAAA;IACA;EACA;EACAkB,KAAA;IACA,0BAAAC,aAAA;MACA,SAAA3B,IAAA,CAAAsB,SAAA,SAAAtB,IAAA,CAAAyB,QAAA,UAAAzB,IAAA,CAAAsB,SAAA;QACA,KAAAM,KAAA,CAAA5B,IAAA,CAAA6B,aAAA;MACA;IACA;IACA,2BAAAC,cAAA;MACA,SAAA9B,IAAA,CAAAsB,SAAA,SAAAtB,IAAA,CAAAyB,QAAA,UAAAzB,IAAA,CAAAsB,SAAA;QACA,KAAAM,KAAA,CAAA5B,IAAA,CAAA6B,aAAA;MACA;IACA;EACA;EACAE,OAAA;IACAzB,QAAA,WAAAA,SAAA;MACA,KAAAN,IAAA;QACAS,QAAA;QAAA;QACAK,OAAA;QAAA;QACAW,QAAA;QAAA;QACAH,SAAA;QAAA;QACAD,IAAA;QAAA;QACAW,IAAA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,KAAA;MACA;MACA;MACA;MACA;MACA,KAAA7B,QAAA;MACA,KAAAJ,IAAA,GAAAgC,GAAA;MACA,KAAAE,SAAA;QACAD,KAAA,CAAAP,KAAA,CAAA5B,IAAA,CAAAqC,aAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,iBAAA,IAAAE,IAAA,WAAAC,GAAA;QACAF,MAAA,CAAAtC,OAAA,8BAAAwC,GAAA,CAAAC,GAAA;QACAH,MAAA,CAAAvC,IAAA,CAAAgC,IAAA,GAAAS,GAAA,CAAAT,IAAA;MACA;IACA;IACA;IACAR,iBAAA,WAAAA,kBAAAR,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAO,QAAA,QAAAzB,IAAA,CAAAyB,QAAA;MACA,IAAAH,SAAA,QAAAtB,IAAA,CAAAsB,SAAA;MACA,IAAAG,QAAA,IAAAH,SAAA,IAAAG,QAAA,KAAAH,SAAA;QACAJ,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACAyB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAAhB,KAAA,CAAA5B,IAAA,CAAA6B,aAAA,uBAAAmB,YAAA;UACAA,YAAA,GAAAD,MAAA,KAAAD,OAAA;QACA;MACA;IACA;IACA;IACAG,aAAA,WAAAA,cAAA;MACA,KAAA7C,gBAAA;IACA;IACA;IACA8C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAhD,SAAA;QACA,KAAAiD,QAAA;UACAzC,OAAA;UACAT,IAAA;QACA;QACA;MACA;MACA,KAAA0B,KAAA,CAAA5B,IAAA,CAAAqD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,GAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAN,MAAA,CAAAnD,IAAA;YAAAE,IAAA,EAAAiD,MAAA,CAAAjD;UAAA;UACAiD,MAAA,CAAAO,OAAA;UACAP,MAAA,CAAAQ,MAAA,CACAC,QAAA,UAAAL,GAAA,EACAf,IAAA;YACAW,MAAA,CAAAU,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAZ,MAAA,CAAAa,QAAA;YAAA,GAAAC,KAAA;UACA,GACAA,KAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACAd,MAAA,CAAAO,OAAA;UACA;QACA;MACA;IACA;IACAQ,cAAA,WAAAA,eAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,GACA;MACA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAAT,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAQ,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,qBAAA,EAAAN,MAAA,CAAAC,QAAA,CAAAM,MAAA,QAAAC,MAAA,CAAAC,QAAA,EACApC,IAAA,WAAAqC,QAAA;QACA,IAAAA,QAAA,CAAAxD,IAAA,YAAAwD,QAAA,CAAA9E,IAAA,IAAA8E,QAAA,CAAA9E,IAAA,CAAA+E,QAAA;UACA;UACAX,MAAA,CAAAC,QAAA,CAAAC,IAAA,GAAAQ,QAAA,CAAA9E,IAAA,CAAA+E,QAAA;QACA;UACAN,MAAA,CAAApB,QAAA,CAAA2B,KAAA;QACA;MACA,GACAd,KAAA,WAAAc,KAAA;QACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;QACAP,MAAA,CAAApB,QAAA,CAAA2B,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}