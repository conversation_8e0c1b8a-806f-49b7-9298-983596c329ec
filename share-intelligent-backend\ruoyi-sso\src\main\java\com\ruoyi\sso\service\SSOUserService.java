package com.ruoyi.sso.service;

import com.ruoyi.sso.domain.SSOUser;

/**
 * SSO用户服务接口
 * 
 * <AUTHOR>
 */
public interface SSOUserService {

    /**
     * 根据用户名查询SSO用户
     * 
     * @param username 用户名
     * @return SSO用户信息
     */
    SSOUser selectSSOUserByUsername(String username);

    /**
     * 根据手机号查询SSO用户
     * 
     * @param phone 手机号
     * @return SSO用户信息
     */
    SSOUser selectSSOUserByPhone(String phone);

    /**
     * 根据ID查询SSO用户
     * 
     * @param id 用户ID
     * @return SSO用户信息
     */
    SSOUser selectSSOUserById(Long id);

    /**
     * 新增SSO用户
     * 
     * @param ssoUser SSO用户信息
     * @return 结果
     */
    int insertSSOUser(SSOUser ssoUser);

    /**
     * 修改SSO用户
     * 
     * @param ssoUser SSO用户信息
     * @return 结果
     */
    int updateSSOUser(SSOUser ssoUser);

    /**
     * 更新最后登录时间
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int updateLastLoginTime(Long userId);

    /**
     * 验证用户密码
     * 
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    boolean validatePassword(String username, String password);
}
