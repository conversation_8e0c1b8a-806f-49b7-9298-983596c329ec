package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.factory.RemoteSSOFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * SSO远程服务接口
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteSSOService", value = "ruoyi-sso", fallbackFactory = RemoteSSOFallbackFactory.class)
public interface RemoteSSOService {

    /**
     * 创建SSO用户
     * 主系统注册时调用
     *
     * @param memberPhone 手机号
     * @param memberRealName 真实姓名
     * @param password 密码（已加密）
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/sso/user/create")
    R<Boolean> createSSOUser(@RequestParam("memberPhone") String memberPhone,
                            @RequestParam("memberRealName") String memberRealName,
                            @RequestParam("password") String password,
                            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 检查SSO用户是否存在
     *
     * @param phone 手机号
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/sso/user/exists/{phone}")
    R<Boolean> checkUserExists(@PathVariable("phone") String phone,
                              @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取SSO用户基础信息
     *
     * @param phone 手机号
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/sso/user/info/{phone}")
    R<Object> getUserInfo(@PathVariable("phone") String phone,
                         @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 更新SSO用户密码
     * 主系统密码重置时调用
     *
     * @param phone 手机号
     * @param password 新密码（已加密）
     * @param source 请求来源
     * @return 结果
     */
    @PutMapping("/sso/user/password")
    R<Boolean> updateSSOUserPassword(@RequestParam("phone") String phone,
                                    @RequestParam("password") String password,
                                    @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
