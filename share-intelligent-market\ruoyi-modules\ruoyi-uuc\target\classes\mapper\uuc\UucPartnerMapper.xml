<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucPartnerMapper">
    
    <resultMap type="UucPartner" id="UucPartnerResult">
        <result property="id"    column="id"    />
        <result property="fieldCode"    column="field_code"    />
        <result property="fieldName"    column="field_name"    />
        <result property="thoughts"    column="thoughts"    />
        <result property="btypeCode"    column="btype_code"    />
        <result property="btypeName"    column="btype_name"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucPartnerVo">
        select id, field_code, field_name, thoughts, btype_code, btype_name, contact, phone, remark, create_by, create_time, update_by, update_time from uuc_partner
    </sql>

    <select id="selectUucPartnerList" parameterType="UucPartner" resultMap="UucPartnerResult">
        <include refid="selectUucPartnerVo"/>
        <where>  
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="contact != null  and contact != ''"> and contact like concat('%', #{contact}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucPartnerById" parameterType="Long" resultMap="UucPartnerResult">
        <include refid="selectUucPartnerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucPartner" parameterType="UucPartner">
        insert into uuc_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fieldCode != null">field_code,</if>
            <if test="fieldName != null and fieldName != ''">field_name,</if>
            <if test="thoughts != null and thoughts != ''">thoughts,</if>
            <if test="btypeCode != null">btype_code,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fieldCode != null">#{fieldCode},</if>
            <if test="fieldName != null and fieldName != ''">#{fieldName},</if>
            <if test="thoughts != null and thoughts != ''">#{thoughts},</if>
            <if test="btypeCode != null">#{btypeCode},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucPartner" parameterType="UucPartner">
        update uuc_partner
        <trim prefix="SET" suffixOverrides=",">
            <if test="fieldCode != null">field_code = #{fieldCode},</if>
            <if test="fieldName != null and fieldName != ''">field_name = #{fieldName},</if>
            <if test="thoughts != null and thoughts != ''">thoughts = #{thoughts},</if>
            <if test="btypeCode != null">btype_code = #{btypeCode},</if>
            <if test="btypeName != null">btype_name = #{btypeName},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucPartnerById" parameterType="Long">
        delete from uuc_partner where id = #{id}
    </delete>

    <delete id="deleteUucPartnerByIds" parameterType="String">
        delete from uuc_partner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>