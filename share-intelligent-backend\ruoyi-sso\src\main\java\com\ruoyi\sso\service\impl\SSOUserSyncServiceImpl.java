package com.ruoyi.sso.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.sso.service.SSOUserSyncService;
import com.ruoyi.system.api.RemoteUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SSO用户同步服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOUserSyncServiceImpl implements SSOUserSyncService {

    private static final Logger log = LoggerFactory.getLogger(SSOUserSyncServiceImpl.class);

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public boolean syncToMainSystem(SSOUser ssoUser) {
        try {
            log.info("开始同步SSO用户到主系统: {}", ssoUser.getUsername());

            // 1. 检查主系统中是否已存在该用户
            R<SysUser> userResult = remoteUserService.getUserInfo(ssoUser.getUsername(), "inner");
            
            if (userResult != null && userResult.getData() != null) {
                // 用户已存在，更新用户信息
                SysUser existingUser = userResult.getData();
                existingUser.setNickName(ssoUser.getNickname());
                existingUser.setPhonenumber(ssoUser.getPhone());
                existingUser.setEmail(ssoUser.getEmail());
                existingUser.setAvatar(ssoUser.getAvatar());
                
                R<Void> updateResult = remoteUserService.updateUserProfile(existingUser, "inner");
                if (R.isSuccess(updateResult)) {
                    log.info("主系统用户信息更新成功: {}", ssoUser.getUsername());
                    return true;
                } else {
                    log.warn("主系统用户信息更新失败: {}", ssoUser.getUsername());
                    return false;
                }
            } else {
                // 用户不存在，创建新用户
                SysUser newUser = createSysUserFromSSO(ssoUser);
                
                R<Void> createResult = remoteUserService.registerUserInfo(newUser, "inner");
                if (R.isSuccess(createResult)) {
                    log.info("主系统用户创建成功: {}", ssoUser.getUsername());
                    
                    // 为新用户分配默认角色
                    assignDefaultRoleToUser(newUser.getUserName());
                    return true;
                } else {
                    log.warn("主系统用户创建失败: {}", ssoUser.getUsername());
                    return false;
                }
            }
            
        } catch (Exception e) {
            log.error("同步用户到主系统异常: {}", ssoUser.getUsername(), e);
            return false;
        }
    }

    @Override
    public boolean syncToMarketSystem(SSOUser ssoUser) {
        try {
            log.info("开始同步SSO用户到市场系统: {}", ssoUser.getUsername());
            
            // TODO: 实现市场系统用户同步逻辑
            // 这里需要调用市场系统的用户服务API
            // 类似主系统的实现方式
            
            log.info("市场系统用户同步完成: {}", ssoUser.getUsername());
            return true;
            
        } catch (Exception e) {
            log.error("同步用户到市场系统异常: {}", ssoUser.getUsername(), e);
            return false;
        }
    }

    @Override
    public boolean syncToAllSystems(SSOUser ssoUser) {
        boolean mainSystemResult = syncToMainSystem(ssoUser);
        boolean marketSystemResult = syncToMarketSystem(ssoUser);
        
        return mainSystemResult && marketSystemResult;
    }

    /**
     * 将SSO用户转换为主系统用户对象
     */
    private SysUser createSysUserFromSSO(SSOUser ssoUser) {
        SysUser sysUser = new SysUser();
        sysUser.setUserName(ssoUser.getUsername());
        sysUser.setNickName(ssoUser.getNickname());
        sysUser.setPhonenumber(ssoUser.getPhone());
        sysUser.setEmail(ssoUser.getEmail());
        sysUser.setAvatar(ssoUser.getAvatar());
        sysUser.setStatus("0"); // 正常状态
        sysUser.setUserType("00"); // 普通用户
        sysUser.setRemark("SSO自动创建用户");
        return sysUser;
    }

    /**
     * 为新用户分配默认角色
     */
    private void assignDefaultRoleToUser(String username) {
        try {
            // TODO: 调用角色分配服务，为用户分配默认角色
            // 例如：普通用户角色
            log.info("为用户 {} 分配默认角色", username);
        } catch (Exception e) {
            log.error("为用户分配默认角色失败: {}", username, e);
        }
    }
}
