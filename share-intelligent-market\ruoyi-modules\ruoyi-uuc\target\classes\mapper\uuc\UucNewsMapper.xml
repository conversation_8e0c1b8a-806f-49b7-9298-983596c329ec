<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucNewsMapper">

    <resultMap type="UucNews" id="UucNewsResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="introduction"    column="introduction"    />
        <result property="content"    column="content"    />
        <result property="readCount"    column="read_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="shareCount"    column="share_count"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="publisher"    column="publisher"    />
        <result property="recommend"    column="recommend"    />
        <result property="origin"    column="origin"    />
        <result property="top"    column="top"    />
        <result property="coverImagePath"    column="cover_image_path"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucNewsVo">
        select id, title, category, introduction, content, read_count, like_count, comment_count, share_count, publish_time, publisher, recommend, origin, top, cover_image_path, status, remark, create_by, create_time, update_by, update_time from uuc_news
    </sql>

    <select id="selectUucNewsList" parameterType="UucNews" resultMap="UucNewsResult">
        <include refid="selectUucNewsVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
        order by publish_time desc
    </select>

    <select id="selectUucAppNewsList" parameterType="UucNews" resultMap="UucNewsResult">
        <include refid="selectUucNewsVo"/>
        <where>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
              and status = 1
        </where>
        order by publish_time desc
    </select>

    <select id="selectUucNewsById" parameterType="Long" resultMap="UucNewsResult">
        <include refid="selectUucNewsVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppNewsById" parameterType="Long" resultMap="UucNewsResult">
        <include refid="selectUucNewsVo"/>
        where id = #{id} and status = 1
    </select>

    <select id="selectIndexQty" parameterType="UucNews" resultMap="UucNewsResult">
        select (select count(*) from tb_enterprise) read_count ,(select count(1) from tb_order) like_count,ROUND((select sum(t.total_price) from tb_order t),2) content
    </select>

    <insert id="insertUucNews" parameterType="UucNews" useGeneratedKeys="true" keyProperty="id">
        insert into uuc_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="category != null">category,</if>
            <if test="introduction != null">introduction,</if>
            <if test="content != null">content,</if>
            <if test="readCount != null">read_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="shareCount != null">share_count,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="publisher != null and publisher != ''">publisher,</if>
            <if test="recommend != null">recommend,</if>
            <if test="origin != null">origin,</if>
            <if test="top != null">top,</if>
            <if test="coverImagePath != null">cover_image_path,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="content != null">#{content},</if>
            <if test="readCount != null">#{readCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="shareCount != null">#{shareCount},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="publisher != null and publisher != ''">#{publisher},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="origin != null">#{origin},</if>
            <if test="top != null">#{top},</if>
            <if test="coverImagePath != null">#{coverImagePath},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucNews" parameterType="UucNews">
        update uuc_news
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="content != null">content = #{content},</if>
            <if test="readCount != null">read_count = #{readCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="shareCount != null">share_count = #{shareCount},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="publisher != null and publisher != ''">publisher = #{publisher},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="origin != null">origin = #{origin},</if>
            <if test="top != null">top = #{top},</if>
            <if test="coverImagePath != null">cover_image_path = #{coverImagePath},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucNewsById" parameterType="Long">
        delete from uuc_news where id = #{id}
    </delete>

    <delete id="deleteUucNewsByIds" parameterType="String">
        delete from uuc_news where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
