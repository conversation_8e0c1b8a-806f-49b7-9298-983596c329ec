<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucProductFollowMapper">
    
    <resultMap type="UucProductFollow" id="UucProductFollowResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="principal"    column="principal"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucProductFollowVo">
        select id, product_id, product_name, principal, remark, create_by, create_time, update_by, update_time from uuc_product_follow
    </sql>

    <select id="selectUucProductFollowList" parameterType="UucProductFollow" resultMap="UucProductFollowResult">
        <include refid="selectUucProductFollowVo"/>
        <where>  
            <if test="productId != null "> and product_id like concat('%', #{productId}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="principal != null  and principal != ''"> and principal like concat('%', #{principal}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucProductFollowById" parameterType="Long" resultMap="UucProductFollowResult">
        <include refid="selectUucProductFollowVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucProductFollow" parameterType="UucProductFollow">
        insert into uuc_product_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="principal != null and principal != ''">principal,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="principal != null and principal != ''">#{principal},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucProductFollow" parameterType="UucProductFollow">
        update uuc_product_follow
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="principal != null and principal != ''">principal = #{principal},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucProductFollowById" parameterType="Long">
        delete from uuc_product_follow where id = #{id}
    </delete>

    <delete id="deleteUucProductFollowByIds" parameterType="String">
        delete from uuc_product_follow where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>