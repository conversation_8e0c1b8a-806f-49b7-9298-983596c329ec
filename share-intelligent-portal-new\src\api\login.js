/*
 * @Author: jhy
 * @Date: 2023-01-28 08:57:34
 * @LastEditors: jhy
 * @LastEditTime: 2023-01-29 15:07:26
 */
import request from "@/utils/request";

// 短信验证码登录、密码设置
export function loginCode(username, smsCode, password) {
  return request({
    url: "/auth/loginBySmsCode",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, smsCode, password, userType: "01" },
  });
}

// 短信验证码登录、密码设置
export function ssologinCode(username, smsCode, password) {
  return request({
    url: "/auth/chiWeb/loginBySmsCode",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, smsCode, password, userType: "01" },
  });
}

// 账号密码登录
export function login(username, password, code, uuid) {
  return request({
    url: "/auth/portallogin",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, password, code, uuid },
  });
}

// 账号密码登录
// export function login(username, password, code, uuid) {
//   return request({
//     url: "/register",
//     headers: {
//       isToken: false,
//     },
//     method: "post",
//     data: { username, password, code, uuid, userType: "01" },
//   });
// }

// sso账号密码登录
export function ssologin(username, password, code, uuid) {
  return request({
    url: "/auth/chiWeb/loginByPwd",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, password, code, uuid, userType: "01" },
  });
}

// 注册方法
export function register(data) {
  return request({
    url: "/auth/register",
    headers: {
      isToken: false,
    },
    method: "post",
    data: data,
  });
}

// 刷新方法
export function refreshToken() {
  return request({
    url: "/auth/refresh",
    method: "post",
  });
}

// // 获取用户详细信息
// export function getInfo() {
//   return request({
//     url: "/system/user/getInfo",
//     method: "get",
//   });
// }
// 获取用户详细信息
export function getInfo() {
  return request({
    url: "/portalweb/Member/getMemberInfo",
    method: "get",
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "delete",
  });
}

// 获取图形验证码
export function getCodeImg() {
  return request({
    url: "/code",
    headers: {
      isToken: false,
    },
    method: "get",
    timeout: 20000,
  });
}

// 获取手机号验证码
export function getCommonCode(params) {
  return request({
    // url: "/auth/single/util/get_common_code",
    url: "/auth/single/util/get_qwt_code",
    method: "get",
    params,
  });
}

// sso单点登录 通过票据 调用接口获取token
export function accessToken(params) {
  return request({
    url: "/auth/chiWeb/getToken",
    method: "get",
    params,
  });
}

// SSO登录 - 获取SSO登录地址
export function getSSOLoginUrl(redirect) {
  return request({
    url: "/sso/loginUrl",
    headers: {
      isToken: false,
    },
    method: "get",
    params: { redirect },
  });
}

// SSO登录 - 处理SSO回调
export function handleSSOCallback(code, state) {
  return request({
    url: "/sso/callback",
    headers: {
      isToken: false,
    },
    method: "get",
    params: { code, state },
  });
}
