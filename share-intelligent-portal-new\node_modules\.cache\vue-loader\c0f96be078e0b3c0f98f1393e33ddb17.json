{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["testingRequire.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "testingRequire.vue", "sourceRoot": "src/views/release/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"检测内容\" prop=\"testingContent\">\r\n        <el-input\r\n          v-model=\"form.testingContent\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"检测要求\" prop=\"testingRequirements\">\r\n        <el-input\r\n          v-model=\"form.testingRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"其他要求\" prop=\"basicRequirements\">\r\n        <el-input\r\n          v-model=\"form.basicRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"场景图片\" prop=\"imageUrl\">\r\n        <ImageUpload v-model=\"form.imageUrl\" :limit=\"1\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPerson\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPhone\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n          >取消</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseDetection } from \"@/api/release\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        testingContent: \"\",\r\n        testingRequirements: \"\",\r\n        basicRequirements: \"\",\r\n        imageUrl: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        testingContent: [\r\n          { required: true, message: \"检测内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        testingRequirements: [\r\n          { required: true, message: \"检测要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        basicRequirements: [\r\n          { required: true, message: \"其他要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        // 添加场景图片必填校验规则\r\n        imageUrl: [\r\n          { required: true, message: \"场景图片不能为空\", trigger: \"change\" },\r\n        ],\r\n        // contactPerson: [\r\n        //   { required: true, message: \"请先维护联系人\", trigger: \"blur\" },\r\n        // ],\r\n        // contactPhone: [\r\n        //   { required: true, message: \"请先维护联系方式\", trigger: \"blur\" },\r\n        // ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo && userinfo != \"null\") {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contact = userinfo.memberRealName;\r\n      this.form.phone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          releaseDetection(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel();\r\n            } else {\r\n              this.$message.error(\"发布失败\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}