-- SSO用户映射表
-- 用于实现真正的单点登录，将不同系统的用户关联起来

CREATE TABLE IF NOT EXISTS sso_user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sso_username VARCHAR(100) NOT NULL COMMENT 'SSO统一用户名（通常是手机号）',
    sso_password VARCHAR(255) NOT NULL COMMENT 'SSO统一密码（BCrypt加密）',
    sso_real_name VARCHAR(100) COMMENT 'SSO用户真实姓名',
    sso_phone VARCHAR(20) COMMENT 'SSO用户手机号',
    sso_email VARCHAR(100) COMMENT 'SSO用户邮箱',
    
    -- 主系统关联信息
    backend_member_id BIGINT COMMENT '主系统member_id',
    backend_member_phone VARCHAR(20) COMMENT '主系统用户手机号',
    
    -- 市场系统关联信息  
    market_member_id BIGINT COMMENT '市场系统member_id',
    market_member_phone VARCHAR(20) COMMENT '市场系统用户手机号',
    
    -- 状态和时间
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    
    -- 索引
    UNIQUE KEY uk_sso_username (sso_username),
    UNIQUE KEY uk_sso_phone (sso_phone),
    INDEX idx_backend_member (backend_member_id),
    INDEX idx_market_member (market_member_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO用户映射表';

-- 插入测试数据示例
INSERT INTO sso_user_mapping (
    sso_username, sso_password, sso_real_name, sso_phone, sso_email,
    backend_member_id, backend_member_phone,
    market_member_id, market_member_phone,
    status, remark
) VALUES 
(
    '13800138000', 
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '张三',
    '13800138000',
    '<EMAIL>',
    1, '13800138000',  -- 假设主系统中的member_id=1
    1, '13800138000',  -- 假设市场系统中的member_id=1  
    '0',
    'SSO测试用户'
),
(
    '13900139000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '李四', 
    '13900139000',
    '<EMAIL>',
    2, '13900139000',  -- 假设主系统中的member_id=2
    2, '13900139000',  -- 假设市场系统中的member_id=2
    '0',
    'SSO测试用户'
);
