{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\supply.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\supply.vue", "mtime": 1750385853721}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_release", "_default", "exports", "default", "data", "imageUrlList", "form", "title", "type", "description", "technologyCategory", "productCategory", "cooperationType", "process", "organization", "contact", "phone", "supplyTypeList", "techTypeList", "cooperationModeOptions", "productStageList", "rules", "required", "message", "trigger", "appShow", "appinput", "demandappList", "demandapptagList", "productTypeList", "created", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "memberRealName", "memberPhone", "getSupplyTypeDict", "getTechTypeDict", "getCooperationDict", "getProductStageDict", "getProductTypeDict", "methods", "changeType", "_this", "params", "dictType", "listData", "then", "response", "rows", "_this2", "_this3", "console", "log", "_this4", "_this5", "onSubmit", "_this6", "$refs", "validate", "valid", "join", "imageUrl", "length", "url", "supplyAdd", "res", "code", "$router", "go", "$message", "success", "onCancel", "$emit", "showDialog", "getApplicationData", "_this7", "applicationFieldName", "applicationData", "arrList", "for<PERSON>ach", "item", "push", "indexOf", "addAppli", "_this8", "applicationAdd", "cancelBtn"], "sources": ["src/views/release/components/supply.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"供给标题\" prop=\"title\">\r\n        <el-input v-model=\"form.title\" maxlength=\"50\" show-word-limit placeholder=\"请输入供给标题\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"type\" label=\"供给类型\">\r\n        <el-radio-group v-model=\"form.type\" placeholder=\"请选择\" clearable @change=\"changeType\">\r\n          <el-radio v-for=\"dict in supplyTypeList\" :key=\"dict.dictValue\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"description\" label=\"供给描述（可按需求产品+应用行业+应用领域进行描述）\">\r\n        <el-input v-model=\"form.description\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\" show-word-limit\r\n          placeholder=\"该供给产品、成果、服务的原理、特点优势，与旧技术的对比数据成本情况等。填写越详细，匹配越准确。\" />\r\n      </el-form-item>\r\n      <el-form-item prop=\"technologyCategory\" label=\"服务类别\" v-if=\"form.type != 2\">\r\n        <el-checkbox-group v-model=\"form.technologyCategory\" placeholder=\"请选择\" clearable>\r\n          <el-checkbox v-for=\"dict in techTypeList\" :key=\"dict.dictValue\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"productCategory\" label=\"产品类别\" v-if=\"form.type == 2\">\r\n        <el-checkbox-group v-model=\"form.productCategory\" placeholder=\"请选择\" clearable>\r\n          <el-checkbox v-for=\"dict in productTypeList\" :key=\"dict.dictCode\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <!-- <el-form-item prop=\"applications\" label=\"应用领域\">\r\n        <div class=\"apptag\">\r\n          <i\r\n            class=\"el-icon-circle-plus-outline\"\r\n            style=\"font-size: 24px; color: rgba(187, 187, 187, 1)\"\r\n            @click=\"showDialog()\"\r\n          ></i>\r\n          <el-tag\r\n            v-for=\"(item, index) in demandapptagList\"\r\n            :key=\"index\"\r\n            closable\r\n            @close=\"closeTag(item, index)\"\r\n          >\r\n            {{ item.applicationFieldName }}\r\n          </el-tag>\r\n        </div>\r\n      </el-form-item> -->\r\n      <el-form-item label=\"场景图片\" prop=\"imageUrl\">\r\n        <ImageUpload :limit=\"1\" v-model=\"imageUrlList\" />\r\n        <!-- <ImageUpload :limit=\"1\" v-model=\"form.imageUrl\" /> -->\r\n      </el-form-item>\r\n      <el-form-item label=\"合作方式\" prop=\"cooperationType\">\r\n        <el-select v-model=\"form.cooperationType\" placeholder=\"请选择\" clearable style=\"width: 100%\">\r\n          <el-option v-for=\"dict in cooperationModeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品阶段\" prop=\"process\">\r\n        <el-select v-model=\"form.process\" placeholder=\"请选择\" clearable style=\"width: 100%\">\r\n          <el-option v-for=\"dict in productStageList\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.attachment\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\">\r\n        <el-input disabled v-model=\"form.organization\" placeholder=\"请先绑定公司\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\">\r\n        <el-input disabled v-model=\"form.contact\" placeholder=\"请先维护联系人\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\">\r\n        <el-input disabled v-model=\"form.phone\" placeholder=\"请先维护联系方式\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!-- 应用领域弹窗 -->\r\n    <div>\r\n      <el-dialog title=\"\" :visible.sync=\"appShow\" width=\"30%\">\r\n        <div>\r\n          <div class=\"searchBoxApp\">\r\n            <el-input v-model=\"appinput\" placeholder=\"请输入内容\" style=\"width: 90%\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchApp()\"></el-button>\r\n            </el-input>\r\n            <span class=\"cancelBtn\" @click=\"cancelBtn()\">取消</span>\r\n          </div>\r\n\r\n          <div style=\"min-height: 250px; overflow-y: scroll\">\r\n            <div v-for=\"(item, index) in demandappList\" :key=\"index\" class=\"selectBottom\" @click=\"selectApp(item)\">\r\n              <i class=\"el-icon-search\"></i>&nbsp;&nbsp;&nbsp;{{\r\n                item.applicationFieldName\r\n              }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// import { product } from \"ramda\";\r\n// import { get } from \"sortablejs\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { applicationData, applicationAdd, supplyAdd } from \"@/api/release\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      imageUrlList: [],\r\n      form: {\r\n        title: \"\",\r\n        type: \"\",\r\n        description: \"\",\r\n        technologyCategory: [],\r\n        productCategory: [],\r\n        // applicationArea: [],\r\n        // imageUrl: [],\r\n        cooperationType: \"\",\r\n        process: \"\",\r\n        // attachment: [],\r\n        organization: \"\",\r\n        contact: \"\",\r\n        phone: \"\",\r\n      },\r\n      supplyTypeList: [],\r\n      techTypeList: [],\r\n      cooperationModeOptions: [],\r\n      productStageList: [],\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: \"请输入供给标题\", trigger: \"blur\" }],\r\n        type: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"change\" },\r\n        ],\r\n        description: [\r\n          { required: true, message: \"请输入供给描述\", trigger: \"blur\" },\r\n        ],\r\n        technologyCategory: [\r\n          { required: true, message: \"请选择服务类别\", trigger: \"change\" },\r\n        ],\r\n        productCategory: [\r\n          { required: true, message: \"请选择产品类别\", trigger: \"change\" },\r\n        ],\r\n        cooperationType: [\r\n          { required: true, message: \"请选择合作方式\", trigger: \"change\" },\r\n        ],\r\n        process: [\r\n          { required: true, message: \"请选择产品阶段\", trigger: \"change\" },\r\n        ],\r\n        // applicationArea: [\r\n        //   { required: true, message: \"请选择应用领域\", trigger: \"change\" },\r\n        // ],\r\n        // displayRestrictions: [\r\n        //   { required: true, message: \"请选择展示限制\", trigger: \"change\" },\r\n        // ],\r\n        // summary: [\r\n        //   { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        // ],\r\n        organization: [\r\n          { required: true, message: \"请先绑定公司\", trigger: \"blur\" },\r\n        ],\r\n        contact: [\r\n          { required: true, message: \"请先维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"请先维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      appShow: false,\r\n      appinput: \"\",\r\n      demandappList: [],\r\n      demandapptagList: [],\r\n      productTypeList: [],\r\n    };\r\n  },\r\n  created() {\r\n    let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    this.form.organization = userInfo.memberCompanyName;\r\n    this.form.contact = userInfo.memberRealName;\r\n    this.form.phone = userInfo.memberPhone;\r\n    this.getSupplyTypeDict();\r\n    // this.getApplicationData();\r\n    this.getTechTypeDict();\r\n    this.getCooperationDict();\r\n    this.getProductStageDict();\r\n    this.getProductTypeDict();\r\n  },\r\n  methods: {\r\n    changeType(data) {\r\n      // console.log(data);\r\n      // console.log(this.form.type);\r\n    },\r\n    // 供给类型字典\r\n    getSupplyTypeDict() {\r\n      let params = { dictType: \"supply_type\" };\r\n      listData(params).then((response) => {\r\n        this.supplyTypeList = response.rows;\r\n      });\r\n    },\r\n    // 服务类别\r\n    getTechTypeDict() {\r\n      let params = { dictType: \"technology_category\" };\r\n      listData(params).then((response) => {\r\n        this.techTypeList = response.rows;\r\n      });\r\n    },\r\n    // 产品类别\r\n    getProductTypeDict() {\r\n      let params = { dictType: \"product_category\" };\r\n      listData(params).then((response) => {\r\n        this.productTypeList = response.rows;\r\n        console.log(this.productTypeList, '产品类别');\r\n      });\r\n    },\r\n    // 合作方式\r\n    getCooperationDict() {\r\n      let params = { dictType: \"supply_cooperation\" };\r\n      listData(params).then((response) => {\r\n        this.cooperationModeOptions = response.rows;\r\n      });\r\n    },\r\n    // 产品阶段\r\n    getProductStageDict() {\r\n      let params = { dictType: \"supply_process\" };\r\n      listData(params).then((response) => {\r\n        this.productStageList = response.rows;\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.technologyCategory = this.form.technologyCategory.join(\",\");\r\n          this.form.productCategory = this.form.productCategory.join(\",\");\r\n          this.form.imageUrl =\r\n            this.imageUrlList && this.imageUrlList.length > 0\r\n              ? this.imageUrlList[0].url\r\n              : \"\";\r\n          supplyAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              // this.$router.push(\"/supplyDemandDocking?index=1\");\r\n              this.$router.go(-1);\r\n              this.$message.success(\"发布成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$emit(\"cancel\");\r\n    },\r\n    showDialog() {\r\n      this.appShow = true;\r\n      this.appinput = \"\";\r\n    },\r\n    getApplicationData() {\r\n      let params = {\r\n        applicationFieldName: this.appinput,\r\n      };\r\n      applicationData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.demandappList = res.rows;\r\n          let arrList = [];\r\n          this.demandappList.forEach((item) => {\r\n            arrList.push(item.applicationFieldName);\r\n          });\r\n\r\n          if (this.appinput && arrList.indexOf(this.appinput) === -1) {\r\n            this.addAppli();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    //应用领域如果没有数据就调新增\r\n    addAppli() {\r\n      let data = {\r\n        applicationFieldName: this.appinput,\r\n      };\r\n      applicationAdd(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.getApplicationData();\r\n        }\r\n      });\r\n    },\r\n    cancelBtn() {\r\n      this.appinput = \"\";\r\n      this.demandappList = [];\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AAwGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AACA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAIA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,eAAA;QACA;QACA;QACAC,eAAA;QACAC,OAAA;QACA;QACAC,YAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACAC,cAAA;MACAC,YAAA;MACAC,sBAAA;MACAC,gBAAA;MACA;MACAC,KAAA;QACAd,KAAA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAhB,IAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,WAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,kBAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,eAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,eAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,OAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAV,YAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,OAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,KAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,KAAA7B,IAAA,CAAAQ,YAAA,GAAAiB,QAAA,CAAAK,iBAAA;IACA,KAAA9B,IAAA,CAAAS,OAAA,GAAAgB,QAAA,CAAAM,cAAA;IACA,KAAA/B,IAAA,CAAAU,KAAA,GAAAe,QAAA,CAAAO,WAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAC,eAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,mBAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAzC,IAAA;MACA;MACA;IAAA,CACA;IACA;IACAmC,iBAAA,WAAAA,kBAAA;MAAA,IAAAO,KAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAA7B,cAAA,GAAAkC,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAZ,eAAA,WAAAA,gBAAA;MAAA,IAAAa,MAAA;MACA,IAAAN,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAnC,YAAA,GAAAiC,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAT,kBAAA,WAAAA,mBAAA;MAAA,IAAAW,MAAA;MACA,IAAAP,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAAzB,eAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAG,OAAA,CAAAC,GAAA,CAAAF,MAAA,CAAAzB,eAAA;MACA;IACA;IACA;IACAY,kBAAA,WAAAA,mBAAA;MAAA,IAAAgB,MAAA;MACA,IAAAV,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAM,MAAA,CAAAtC,sBAAA,GAAAgC,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAV,mBAAA,WAAAA,oBAAA;MAAA,IAAAgB,MAAA;MACA,IAAAX,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAtC,gBAAA,GAAA+B,QAAA,CAAAC,IAAA;MACA;IACA;IACAO,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAtD,IAAA,CAAAI,kBAAA,GAAAkD,MAAA,CAAAtD,IAAA,CAAAI,kBAAA,CAAAsD,IAAA;UACAJ,MAAA,CAAAtD,IAAA,CAAAK,eAAA,GAAAiD,MAAA,CAAAtD,IAAA,CAAAK,eAAA,CAAAqD,IAAA;UACAJ,MAAA,CAAAtD,IAAA,CAAA2D,QAAA,GACAL,MAAA,CAAAvD,YAAA,IAAAuD,MAAA,CAAAvD,YAAA,CAAA6D,MAAA,OACAN,MAAA,CAAAvD,YAAA,IAAA8D,GAAA,GACA;UACA,IAAAC,kBAAA,EAAAR,MAAA,CAAAtD,IAAA,EAAA4C,IAAA,WAAAmB,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA;cACAV,MAAA,CAAAW,OAAA,CAAAC,EAAA;cACAZ,MAAA,CAAAa,QAAA,CAAAC,OAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAApD,OAAA;MACA,KAAAC,QAAA;IACA;IACAoD,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAAhC,MAAA;QACAiC,oBAAA,OAAAtD;MACA;MACA,IAAAuD,wBAAA,EAAAlC,MAAA,EAAAG,IAAA,WAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAS,MAAA,CAAApD,aAAA,GAAA0C,GAAA,CAAAjB,IAAA;UACA,IAAA8B,OAAA;UACAH,MAAA,CAAApD,aAAA,CAAAwD,OAAA,WAAAC,IAAA;YACAF,OAAA,CAAAG,IAAA,CAAAD,IAAA,CAAAJ,oBAAA;UACA;UAEA,IAAAD,MAAA,CAAArD,QAAA,IAAAwD,OAAA,CAAAI,OAAA,CAAAP,MAAA,CAAArD,QAAA;YACAqD,MAAA,CAAAQ,QAAA;UACA;QACA;MACA;IACA;IACA;IACAA,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAApF,IAAA;QACA4E,oBAAA,OAAAtD;MACA;MACA,IAAA+D,uBAAA,EAAArF,IAAA,EAAA8C,IAAA,WAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkB,MAAA,CAAAV,kBAAA;QACA;MACA;IACA;IACAY,SAAA,WAAAA,UAAA;MACA,KAAAhE,QAAA;MACA,KAAAC,aAAA;IACA;EACA;AACA", "ignoreList": []}]}