-- SSO统一用户表
-- 在主系统的SSO模块中实现统一用户中心

CREATE TABLE IF NOT EXISTS sso_users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名（通常是手机号）',
    password VARCHAR(255) NOT NULL COMMENT '密码（BCrypt加密）',
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(500) COMMENT '头像地址',
    
    -- 状态和权限
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    user_type VARCHAR(20) DEFAULT 'NORMAL' COMMENT '用户类型（NORMAL普通用户 ADMIN管理员）',
    
    -- 系统权限标识
    backend_enabled CHAR(1) DEFAULT '1' COMMENT '主系统权限（0禁用 1启用）',
    market_enabled CHAR(1) DEFAULT '1' COMMENT '市场系统权限（0禁用 1启用）',
    
    -- 登录信息
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    
    -- 审计字段
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    
    -- 索引
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_phone (phone),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO统一用户表';

-- 插入测试数据
INSERT INTO sso_users (
    username, password, real_name, phone, email,
    backend_enabled, market_enabled, status, user_type, remark
) VALUES 
(
    '13800138000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '张三',
    '13800138000',
    '<EMAIL>',
    '1', '1', '0', 'NORMAL',
    'SSO测试用户 - 可访问主系统和市场系统'
),
(
    '13900139000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '李四',
    '13900139000',
    '<EMAIL>',
    '1', '0', '0', 'NORMAL',
    'SSO测试用户 - 只能访问主系统'
),
(
    '13700137000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '王五',
    '13700137000',
    '<EMAIL>',
    '0', '1', '0', 'NORMAL',
    'SSO测试用户 - 只能访问市场系统'
),
(
    'admin',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '系统管理员',
    '13888888888',
    '<EMAIL>',
    '1', '1', '0', 'ADMIN',
    'SSO系统管理员'
);
