#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用MCP Server生成SSO统一用户表
在mcpserver数据库中创建sso_users表
"""

import pymysql
import time
import os

def create_sso_users_table():
    """在mcpserver数据库中创建SSO用户表"""
    
    # 数据库配置 - mcpserver数据库
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'password',  # 请根据实际情况修改密码
        'database': 'mcpserver',
        'charset': 'utf8mb4',
        'autocommit': True
    }

    # SSO用户表创建SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS sso_users (
        user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
        username VARCHAR(100) NOT NULL COMMENT '用户名（通常是手机号）',
        password VARCHAR(255) NOT NULL COMMENT '密码（BCrypt加密）',
        real_name VARCHAR(100) COMMENT '真实姓名',
        phone VARCHAR(20) COMMENT '手机号',
        email VARCHAR(100) COMMENT '邮箱',
        avatar VARCHAR(500) COMMENT '头像地址',
        
        -- 状态和权限
        status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
        user_type VARCHAR(20) DEFAULT 'NORMAL' COMMENT '用户类型（NORMAL普通用户 ADMIN管理员）',
        
        -- 系统权限标识
        backend_enabled CHAR(1) DEFAULT '1' COMMENT '主系统权限（0禁用 1启用）',
        market_enabled CHAR(1) DEFAULT '1' COMMENT '市场系统权限（0禁用 1启用）',
        
        -- 登录信息
        last_login_time DATETIME COMMENT '最后登录时间',
        last_login_ip VARCHAR(50) COMMENT '最后登录IP',
        login_count INT DEFAULT 0 COMMENT '登录次数',
        
        -- 审计字段
        create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
        
        -- 索引
        UNIQUE KEY uk_username (username),
        UNIQUE KEY uk_phone (phone),
        INDEX idx_status (status),
        INDEX idx_user_type (user_type),
        INDEX idx_create_time (create_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO统一用户表';
    """

    # 插入测试数据SQL
    insert_data_sql = """
    INSERT INTO sso_users (
        username, password, real_name, phone, email,
        backend_enabled, market_enabled, status, user_type, remark
    ) VALUES 
    (
        '13800138000',
        '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
        '张三',
        '13800138000',
        '<EMAIL>',
        '1', '1', '0', 'NORMAL',
        'SSO测试用户 - 可访问主系统和市场系统'
    ),
    (
        '13900139000',
        '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
        '李四',
        '13900139000',
        '<EMAIL>',
        '1', '0', '0', 'NORMAL',
        'SSO测试用户 - 只能访问主系统'
    ),
    (
        '13700137000',
        '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
        '王五',
        '13700137000',
        '<EMAIL>',
        '0', '1', '0', 'NORMAL',
        'SSO测试用户 - 只能访问市场系统'
    ),
    (
        'admin',
        '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
        '系统管理员',
        '13888888888',
        '<EMAIL>',
        '1', '1', '0', 'ADMIN',
        'SSO系统管理员'
    )
    ON DUPLICATE KEY UPDATE
        real_name = VALUES(real_name),
        email = VALUES(email),
        backend_enabled = VALUES(backend_enabled),
        market_enabled = VALUES(market_enabled),
        remark = VALUES(remark);
    """

    try:
        print("正在连接mcpserver数据库...")
        print(f"连接信息: {config['host']}:{config['port']}/{config['database']}")
        
        # 连接数据库
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print("数据库连接成功！")
        
        # 创建表
        print("正在创建sso_users表...")
        cursor.execute(create_table_sql)
        print("✅ sso_users表创建成功！")
        
        # 插入测试数据
        print("正在插入测试数据...")
        cursor.execute(insert_data_sql)
        affected_rows = cursor.rowcount
        print(f"✅ 测试数据插入成功！影响行数: {affected_rows}")
        
        # 验证数据
        print("正在验证数据...")
        cursor.execute("SELECT user_id, username, real_name, backend_enabled, market_enabled, status FROM sso_users")
        results = cursor.fetchall()
        
        print("\n📊 当前SSO用户数据:")
        print("ID\t用户名\t\t真实姓名\t主系统\t市场系统\t状态")
        print("-" * 60)
        for row in results:
            user_id, username, real_name, backend_enabled, market_enabled, status = row
            backend_text = "✅" if backend_enabled == '1' else "❌"
            market_text = "✅" if market_enabled == '1' else "❌"
            status_text = "正常" if status == '0' else "停用"
            print(f"{user_id}\t{username}\t{real_name}\t{backend_text}\t{market_text}\t{status_text}")
        
        print(f"\n🎉 SSO统一用户表创建完成！共有 {len(results)} 个用户")
        print("\n📝 测试账号信息:")
        print("- 用户名: 13800138000, 密码: admin123 (可访问主系统和市场系统)")
        print("- 用户名: 13900139000, 密码: admin123 (只能访问主系统)")
        print("- 用户名: 13700137000, 密码: admin123 (只能访问市场系统)")
        print("- 用户名: admin, 密码: admin123 (系统管理员)")
        
    except pymysql.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        print("数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("🚀 开始在mcpserver数据库中创建SSO统一用户表...")
    print("=" * 60)
    
    success = create_sso_users_table()
    
    print("=" * 60)
    if success:
        print("✅ SSO用户表创建成功！")
        print("\n🔧 下一步操作:")
        print("1. 重启SSO服务以加载新的数据库表")
        print("2. 测试SSO登录功能")
        print("3. 验证不同用户的系统访问权限")
    else:
        print("❌ SSO用户表创建失败！请检查数据库连接和权限")
    
    input("\n按回车键退出...")
