{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKdmFyIF9yZWdlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2NvbXBhbnkvbm1kL25tZG5ldy9zaGFyZS1pbnRlbGxpZ2VudC9zaGFyZS1pbnRlbGxpZ2VudC1wb3J0YWwtbmV3L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yLmpzIikpOwp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwp2YXIgX3NvbHV0aW9uID0gcmVxdWlyZSgiQC9hcGkvc29sdXRpb24iKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJkZW1hbmRIYWxsIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGFyYW1zOiB7CiAgICAgICAgcGFyZW50SWQ6ICIiLAogICAgICAgIHNlYXJjaFN0cjogIiIsCiAgICAgICAgc29sdXRpb25UeXBlSWQ6ICIiLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGNhdGVnb3J5OiAxCiAgICAgIH0sCiAgICAgIHRvdGFsOiAwLAogICAgICB0b3RhbDE6IDAsCiAgICAgIGtleXdvcmRzOiAiIiwKICAgICAgZm9ybToge30sCiAgICAgIGZsYWc6ICLlhajpg6giLAogICAgICBhcHBsaVR5cGVEYXRhOiBbewogICAgICAgIGRpY3RWYWx1ZTogIjAiLAogICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIKICAgICAgfSwgewogICAgICAgIGRpY3RMYWJlbDogIuWIm+aWs<PERSON>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"}, {"version": 3, "names": ["_solution", "require", "name", "data", "params", "parentId", "searchStr", "solutionTypeId", "pageNum", "pageSize", "category", "total", "total1", "keywords", "form", "flag", "appliTypeData", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "appliTypeImgList", "url", "demandList", "title", "appliArea", "requireType", "desc", "publishTime", "aaa", "bbb", "typeList", "id", "typeName", "typeNestList", "dataList", "loading", "created", "getTypeNext", "methods", "getDemandList", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "res", "w", "_context", "n", "getSolutionList", "v", "code", "rows", "a", "searchHot", "val", "onSearch", "getappliData", "value", "_this2", "_callee2", "_context2", "getSolutionTypeList", "changeSolve", "changeSolveB", "handleSizeChange", "handleCurrentChange", "goDetail", "$router", "push"], "sources": ["src/views/solution/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">解决方案</div>\r\n      <div style=\"height: 33px; margin-top: 1px\"></div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"params.searchStr\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_search\">\r\n        <span>热门搜索：</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('供应链管理')\">供应链管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('设备智慧物联')\">设备智慧物联</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('生产过程管控')\">生产过程管控</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('科技成果转化')\">科技成果转化</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('企业运营管理')\">企业运营管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产业转型升级')\">产业转型升级</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产融服务')\">产融服务</span>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"content_bottom\">\r\n      <div class=\"icondiv\">\r\n        <div class=\"solutioniconFlex\">\r\n          <div v-for=\"(item, index) in typeList\" :key=\"item.id\"\r\n            :class=\"['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']\" @click=\"changeSolve(item.id)\">\r\n            {{ item.typeName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandContent\" style=\"padding-top: 40px\">\r\n        <div class=\"demandflex\" style=\"height: 715px\">\r\n          <div class=\"leftsolution\">\r\n            <div :class=\"['leftTitle', bbb == 1 ? 'leftTitleHover' : '']\" @click=\"changeSolveB(1)\">\r\n              全部（{{ total1 }}）\r\n            </div>\r\n            <div v-for=\"(item, index) in typeNestList\" :key=\"index\" :class=\"[\r\n              'leftTitle',\r\n              bbb == item.solutionTypeId ? 'leftTitleHover' : '',\r\n            ]\" @click=\"changeSolveB(item.solutionTypeId)\">\r\n              <span class=\"tr2\">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightSolution\" v-if=\"dataList && dataList.length > 0\" v-loading=\"loading\">\r\n            <div v-for=\"(item, index) in dataList\" :key=\"index\" class=\"solutionContent tr2\">\r\n              <div @click=\"goDetail(item.solutionId)\">\r\n                <div class=\"solutionContentTitle tr2\">\r\n                  {{ item.solutionName }}\r\n                </div>\r\n                <div class=\"solutionContentValue tr2 textOverflow\">\r\n                  {{ item.solutionIntroduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightEmpty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n        <!-- 分页 -->\r\n        <div class=\"pageStyle\">\r\n          <el-pagination v-if=\"dataList && dataList.length > 0\" background layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\" :page-size=\"params.pageSize\" :current-page=\"params.pageNum\" :total=\"total\"\r\n            @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getSolutionList, getSolutionTypeList } from \"@/api/solution\";\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      params: {\r\n        parentId: \"\",\r\n        searchStr: \"\",\r\n        solutionTypeId: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        category: 1,\r\n      },\r\n      total: 0,\r\n      total1: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"@/assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      demandList: [\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      aaa: \"1\",\r\n      bbb: 1,\r\n      typeList: [\r\n        {\r\n          id: \"1\",\r\n          typeName: \"行业解决方案\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          typeName: \"领域解决方案\",\r\n        },\r\n      ],\r\n      typeNestList: [],\r\n      dataList: [],\r\n      loading: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getTypeNext('1');\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      this.loading = true;\r\n      this.params.category = this.aaa;\r\n      let res = await getSolutionList(this.params);\r\n      if (res.code == 200) {\r\n        this.dataList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n        if (this.params.solutionTypeId == \"\") {\r\n          this.total1 = res.total;\r\n        }\r\n      }\r\n    },\r\n    searchHot(val) {\r\n      this.params.searchStr = val;\r\n      this.onSearch();\r\n    },\r\n    onSearch() {\r\n      this.params.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getDemandList();\r\n    },\r\n    async getTypeNext(val) {\r\n      let res = await getSolutionTypeList({ category: val });\r\n      if (res.code == 200) {\r\n        this.typeNestList = res.rows;\r\n        this.getDemandList();\r\n      }\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val;\r\n      this.params.parentId = val;\r\n      this.params.solutionTypeId = \"\";\r\n      this.bbb = 1;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      this.getTypeNext(val);\r\n    },\r\n    changeSolveB(val) {\r\n      this.bbb = val;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      if (val == 1) {\r\n        this.params.solutionTypeId = \"\";\r\n      } else {\r\n        this.params.solutionTypeId = val;\r\n      }\r\n      this.getDemandList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.params.pageSize = pageSize;\r\n      this.getDemandList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.params.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n\r\n    goDetail(id) {\r\n      this.$router.push(\"/solutionDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .hot_search {\r\n    font-size: 14px;\r\n    color: #000;\r\n\r\n    .hot_search_item {\r\n      margin-right: 20px;\r\n      color: #000;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  .icondiv {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 100%;\r\n    height: 100px;\r\n    position: relative;\r\n\r\n    .solutioniconFlex {\r\n      display: flex;\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 1200px;\r\n      right: 0;\r\n      left: 0;\r\n      margin: auto;\r\n      justify-content: center;\r\n\r\n      .iconFlexTitle {\r\n        width: 110px;\r\n        height: 45px;\r\n        line-height: 26px;\r\n        border-radius: 2px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 18px;\r\n        text-align: center;\r\n        margin: 0 20px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .activeTitle {\r\n        color: #0cad9d;\r\n        border-bottom: 2px solid #0cad9d;\r\n      }\r\n    }\r\n  }\r\n\r\n  .demandContent {\r\n    width: 100%;\r\n    background: #f7f8fa;\r\n    // background: #fff;\r\n    padding-top: 20px;\r\n    box-shadow: #21c9b8 solid 1px;\r\n    // border: #21c9b8 solid 1px;\r\n\r\n    .demandflex {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      flex-wrap: wrap;\r\n\r\n      .leftsolution {\r\n        width: 185px;\r\n        height: 715px;\r\n        line-height: 20px;\r\n        opacity: 0.95;\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg,\r\n            rgba(244, 246, 249, 1) 0%,\r\n            rgba(255, 255, 255, 1) 100%);\r\n        color: rgba(16, 16, 16, 1);\r\n        font-size: 14px;\r\n        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n        border: 2px solid rgba(255, 255, 255, 1);\r\n        padding: 20px 0;\r\n        box-sizing: border-box;\r\n        overflow-y: auto;\r\n\r\n        .leftTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 16px;\r\n          margin: 30px 0;\r\n          padding-left: 20px;\r\n          border-left: 3px solid transparent;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .leftTitleHover {\r\n          color: #0cad9d;\r\n          border-left: 3px solid #0cad9d;\r\n        }\r\n      }\r\n\r\n      .rightSolution {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        align-content: flex-start;\r\n\r\n        .solutionContent {\r\n          width: 490px;\r\n          height: 124px;\r\n          border: 2px solid transparent;\r\n          padding: 20px;\r\n          box-sizing: border-box;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .solutionContent:hover {\r\n          opacity: 0.95;\r\n          border-radius: 4px;\r\n          background: linear-gradient(180deg,\r\n              rgba(244, 246, 249, 1) 0%,\r\n              rgba(255, 255, 255, 1) 100%);\r\n          color: rgba(16, 16, 16, 1);\r\n          font-size: 14px;\r\n          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n          border: 2px solid rgba(255, 255, 255, 1);\r\n        }\r\n\r\n        .solutionContentTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .solutionContent:hover .solutionContentTitle {\r\n          color: #0cad9d;\r\n        }\r\n\r\n        .solutionContentValue {\r\n          color: rgba(102, 102, 102, 1);\r\n          font-size: 12px;\r\n          line-height: 1.5;\r\n        }\r\n      }\r\n\r\n      .rightEmpty {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AA8EA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,cAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,IAAA;MACAC,IAAA;MACAC,aAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAA,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,EACA;MACAE,gBAAA,GACA;QACAC,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,EACA;MACAoB,UAAA,GACA;QACAC,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,EACA;MACAC,GAAA;MACAC,GAAA;MACAC,QAAA,GACA;QACAC,EAAA;QACAC,QAAA;MACA,GACA;QACAD,EAAA;QACAC,QAAA;MACA,EACA;MACAC,YAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAT,KAAA,CAAAL,OAAA;cACAK,KAAA,CAAAnC,MAAA,CAAAM,QAAA,GAAA6B,KAAA,CAAAZ,GAAA;cAAAoB,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAC,yBAAA,EAAAV,KAAA,CAAAnC,MAAA;YAAA;cAAAyC,GAAA,GAAAE,QAAA,CAAAG,CAAA;cACA,IAAAL,GAAA,CAAAM,IAAA;gBACAZ,KAAA,CAAAN,QAAA,GAAAY,GAAA,CAAAO,IAAA;gBACAb,KAAA,CAAA5B,KAAA,GAAAkC,GAAA,CAAAlC,KAAA;gBACA4B,KAAA,CAAAL,OAAA;gBACA,IAAAK,KAAA,CAAAnC,MAAA,CAAAG,cAAA;kBACAgC,KAAA,CAAA3B,MAAA,GAAAiC,GAAA,CAAAlC,KAAA;gBACA;cACA;YAAA;cAAA,OAAAoC,QAAA,CAAAM,CAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;IACAU,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAnD,MAAA,CAAAE,SAAA,GAAAiD,GAAA;MACA,KAAAC,QAAA;IACA;IACAA,QAAA,WAAAA,SAAA;MACA,KAAApD,MAAA,CAAAI,OAAA;MACA,KAAA8B,aAAA;IACA;IACAmB,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAA3C,IAAA,GAAA2C,KAAA;MACA,KAAApB,aAAA;IACA;IACAF,WAAA,WAAAA,YAAAmB,GAAA;MAAA,IAAAI,MAAA;MAAA,WAAAnB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiB,SAAA;QAAA,IAAAf,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA;YAAA;cAAAa,SAAA,CAAAb,CAAA;cAAA,OACA,IAAAc,6BAAA;gBAAApD,QAAA,EAAA6C;cAAA;YAAA;cAAAV,GAAA,GAAAgB,SAAA,CAAAX,CAAA;cACA,IAAAL,GAAA,CAAAM,IAAA;gBACAQ,MAAA,CAAA3B,YAAA,GAAAa,GAAA,CAAAO,IAAA;gBACAO,MAAA,CAAArB,aAAA;cACA;YAAA;cAAA,OAAAuB,SAAA,CAAAR,CAAA;UAAA;QAAA,GAAAO,QAAA;MAAA;IACA;IACAG,WAAA,WAAAA,YAAAR,GAAA;MACA,KAAA5B,GAAA,GAAA4B,GAAA;MACA,KAAAnD,MAAA,CAAAC,QAAA,GAAAkD,GAAA;MACA,KAAAnD,MAAA,CAAAG,cAAA;MACA,KAAAqB,GAAA;MACA,KAAAxB,MAAA,CAAAK,QAAA;MACA,KAAAL,MAAA,CAAAI,OAAA;MACA,KAAA4B,WAAA,CAAAmB,GAAA;IACA;IACAS,YAAA,WAAAA,aAAAT,GAAA;MACA,KAAA3B,GAAA,GAAA2B,GAAA;MACA,KAAAnD,MAAA,CAAAK,QAAA;MACA,KAAAL,MAAA,CAAAI,OAAA;MACA,IAAA+C,GAAA;QACA,KAAAnD,MAAA,CAAAG,cAAA;MACA;QACA,KAAAH,MAAA,CAAAG,cAAA,GAAAgD,GAAA;MACA;MACA,KAAAjB,aAAA;IACA;IACA2B,gBAAA,WAAAA,iBAAAxD,QAAA;MACA,KAAAL,MAAA,CAAAK,QAAA,GAAAA,QAAA;MACA,KAAA6B,aAAA;IACA;IACA4B,mBAAA,WAAAA,oBAAA1D,OAAA;MACA,KAAAJ,MAAA,CAAAI,OAAA,GAAAA,OAAA;MACA,KAAA8B,aAAA;IACA;IAEA6B,QAAA,WAAAA,SAAArC,EAAA;MACA,KAAAsC,OAAA,CAAAC,IAAA,yBAAAvC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}