<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucSolutionMapper">

    <resultMap type="UucSolution" id="UucSolutionResult">
        <result property="id"    column="id"    />
        <result property="typeId"    column="type_id"    />
        <result property="title"    column="title"    />
        <result property="image"    column="image"    />
        <result property="detail"    column="detail"    />
        <result property="content"    column="content"    />
        <result property="sorts"    column="sorts"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucSolutionVo">
        select id, type_id, title, image, detail, content, sorts, create_by, create_time, update_by, update_time from uuc_solution
    </sql>

    <select id="selectUucSolutionList" parameterType="UucSolution" resultMap="UucSolutionResult">
        <include refid="selectUucSolutionVo"/>
        <where>
            <if test="typeId != null  and typeId != ''"> and type_id = #{typeId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppSolutionList" parameterType="UucSolution" resultMap="UucSolutionResult">
        <include refid="selectUucSolutionVo"/>
        <where>
            <if test="typeId != null  and typeId != ''"> and type_id = #{typeId}</if>
        </where>
        order by sorts desc
    </select>

    <select id="selectUucSolutionById" parameterType="Long" resultMap="UucSolutionResult">
        <include refid="selectUucSolutionVo"/>
        where id = #{id}
    </select>

    <insert id="insertUucSolution" parameterType="UucSolution">
        insert into uuc_solution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="typeId != null and typeId != ''">type_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="image != null and image != ''">image,</if>
            <if test="detail != null and detail != ''">detail,</if>
            <if test="content != null">content,</if>
            <if test="sorts != null">sorts,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="typeId != null and typeId != ''">#{typeId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="image != null and image != ''">#{image},</if>
            <if test="detail != null and detail != ''">#{detail},</if>
            <if test="content != null">#{content},</if>
            <if test="sorts != null">#{sorts},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUucSolution" parameterType="UucSolution">
        update uuc_solution
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null and typeId != ''">type_id = #{typeId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="image != null and image != ''">image = #{image},</if>
            <if test="detail != null and detail != ''">detail = #{detail},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sorts != null">sorts = #{sorts},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucSolutionById" parameterType="Long">
        delete from uuc_solution where id = #{id}
    </delete>

    <delete id="deleteUucSolutionByIds" parameterType="String">
        delete from uuc_solution where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>