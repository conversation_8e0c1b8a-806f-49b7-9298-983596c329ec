package com.ruoyi.system.api;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * SSO远程服务配置
 * 专门为SSO服务优化的Feign配置
 * 
 * <AUTHOR>
 */
@Configuration
public class RemoteSSOServiceConfig {

    /**
     * SSO服务专用超时配置
     * 针对SSO服务的特点进行优化
     */
    @Bean
    public Request.Options ssoFeignOptions() {
        return new Request.Options(
            3000,  // 连接超时：3秒（SSO服务通常响应较快）
            15000, // 读取超时：15秒（给数据库操作足够时间）
            true   // 跟随重定向
        );
    }

    /**
     * SSO服务重试配置
     * 快速失败策略，避免长时间等待
     */
    @Bean
    public Retryer ssoRetryer() {
        // 重试间隔50ms，最大重试间隔500ms，最大重试次数2次
        return new Retryer.Default(50, TimeUnit.MILLISECONDS.toMillis(500), 2);
    }

    /**
     * SSO服务日志级别
     * 便于调试SSO调用问题
     */
    @Bean
    public Logger.Level ssoLoggerLevel() {
        // 开发环境使用FULL，生产环境使用BASIC
        return Logger.Level.BASIC;
    }
}
