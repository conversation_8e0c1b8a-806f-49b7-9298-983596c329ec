{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\scienceFunding\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\scienceFunding\\index.vue", "mtime": 1750385853720}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnZhciBfdGVjaFJlcXVpcmVtZW50ID0gcmVxdWlyZSgiQC9hcGkvdGVjaFJlcXVpcmVtZW50Iik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnU2NpZW5jZUZ1bmRpbmcnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBTeXNUZWNoUmVxdWlyZW1lbnRMaXN0OiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgaXNKb2luOiBmYWxzZQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfdGVjaFJlcXVpcmVtZW50Lmxpc3RTeXNUZWNoUmVxdWlyZW1lbnQpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuU3lzVGVjaFJlcXVpcmVtZW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUpvaW46IGZ1bmN0aW9uIGhhbmRsZUpvaW4ocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL2pvaW5TdXBwbHknLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICByZXF1aXJlbWVudElkOiByb3cucmVxdWlyZW1lbnRJZCwKICAgICAgICAgIHRpdGxlOiByb3cucmVxdWlyZW1lbnRUaXRsZQogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_techRequirement", "require", "name", "data", "SysTechRequirementList", "loading", "queryParams", "pageNum", "pageSize", "is<PERSON><PERSON><PERSON>", "created", "getList", "methods", "_this", "listSysTechRequirement", "then", "response", "rows", "total", "handleJoin", "row", "$router", "push", "path", "query", "requirementId", "title", "requirementTitle"], "sources": ["src/views/innovationSharing/components/scienceFunding/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scienceFunding\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 40px\">众筹科研</div>\r\n      <div style=\"height: 33px; margin-top: 41px;font-size: 20px;\">“众筹科研\"新模式，构建\"共投、共研、共担、共赢”新机制。</div>\r\n    </div>\r\n    <div class=\"card-container\">\r\n      <div class=\"content\">\r\n        <div class=\"desc\">众筹科研由复合材料链主企业、上下游单位及创新平台共同出资，依托共享智造平台整合技术、产能与市场资源，推动材料\r\n          研发、智能制造及产业化协同创新，实现风险共担、利益共亨的市场化运营，助力复材集群产业链升级与生态构建。</div>\r\n        <div class=\"content-item\">\r\n          <div class=\"info\">\r\n            <img src=\"@/assets/scienceFunding/title1.png\" class=\"info-title\" alt=\"\">\r\n            <div class=\"info-text\">由复材产业链链主企业或龙头企业、上下游企业、创新平台等单位发起并出资，按照\r\n              市场化运营方式形成多方共投。\r\n            </div>\r\n          </div>\r\n          <img src=\"@/assets/scienceFunding/img1.png\" class=\"info-img\" alt=\"\">\r\n        </div>\r\n        <div class=\"content-item\">\r\n          <img src=\"@/assets/scienceFunding/img2.jpg\" class=\"info-img\" alt=\"\">\r\n          <div class=\"info\">\r\n            <img src=\"@/assets/scienceFunding/title2.png\" class=\"info-title\" alt=\"\">\r\n            <div class=\"info-text\">以复材产业关键共性技术需求为牵引，组织高度关联方形成研发团队，实现技术应用\r\n              企业和优势高校院所紧密合作，切实提升研发效率和成功率，促进科研与复材产业双向链接。\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"content-item\">\r\n          <div class=\"info\">\r\n            <img src=\"@/assets/scienceFunding/title3.png\" class=\"info-title\" alt=\"\">\r\n            <div class=\"info-text\">坚持市场化运作，各方签订研发协议，提前约定研发内容、绩效目标、投入分配、预\r\n              算安排、任务分工、实施周期、成果归属、风险控制、验收条件等，对于科研产生的风险由各方共同承担。\r\n            </div>\r\n          </div>\r\n          <img src=\"@/assets/scienceFunding/img3.png\" class=\"info-img\" alt=\"\">\r\n        </div>\r\n        <div class=\"content-item\">\r\n          <img src=\"@/assets/scienceFunding/img4.png\" class=\"info-img\" alt=\"\">\r\n          <div class=\"info\">\r\n            <img src=\"@/assets/scienceFunding/title4.png\" class=\"info-title\" alt=\"\">\r\n            <div class=\"info-text\">项目产生的知识产权、数据、论文、新工艺、新技术、新产品、新方法等科技成果，\r\n              由各方按约定共享，并优先在参与单位间使用、转让，所产生收益由各参与方按约定分配。鼓励行业共性技术\r\n              在产业集群扩散，促进区域产业转型升级，实现多方共赢。\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <el-table v-loading=\"loading\" :data=\"SysTechRequirementList\">\r\n          <el-table-column label=\"技术需求\" align=\"center\" prop=\"requirementTitle\" width=\"200\" />\r\n          <el-table-column label=\"发布方出资\" align=\"center\" prop=\"publisherInvestment\" />\r\n          <el-table-column label=\"计划资金\" align=\"center\" prop=\"plannedBudget\" />\r\n          <el-table-column label=\"发布企业\" align=\"center\" prop=\"publisherCompany\" width=\"180\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"contactPerson\" />\r\n          <el-table-column label=\"联系方式\" align=\"center\" prop=\"contactPhone\" width=\"110\" />\r\n          <el-table-column label=\"截止时间\" align=\"center\" prop=\"deadline\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.deadline, '{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" @click=\"handleJoin(scope.row)\">立即报名</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n  listSysTechRequirement\r\n} from \"@/api/techRequirement\";\r\nexport default {\r\n  name: 'ScienceFunding',\r\n  data() {\r\n    return {\r\n      SysTechRequirementList: [],\r\n      loading: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      isJoin: false\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      listSysTechRequirement(this.queryParams).then(response => {\r\n        this.SysTechRequirementList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleJoin(row) {\r\n      this.$router.push({\r\n        path: '/joinSupply',\r\n        query: {\r\n          requirementId: row.requirementId,\r\n          title: row.requirementTitle\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scienceFunding {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #F2F2F2;\r\n\r\n  .content_banner {\r\n    width: 100%;\r\n    height: 300px;\r\n    background-image: url(\"../../../../assets/release/banner.png\");\r\n    background-size: 100% 100%;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    padding-top: 71px;\r\n    font-weight: 500;\r\n    font-size: 40px;\r\n    color: #000;\r\n    font-family: DOUYU;\r\n  }\r\n}\r\n\r\n.card-container {\r\n  width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  .content {\r\n    width: 100%;\r\n    min-height: 500px;\r\n    background-color: #fff;\r\n    margin-top: 30px;\r\n    border-radius: 5px;\r\n    padding: 90px 110px;\r\n    box-sizing: border-box;\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      line-height: 34px;\r\n      margin-bottom: 70px;\r\n    }\r\n\r\n    .content-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin: 60px 0;\r\n      height: 225px;\r\n\r\n      .info {\r\n        width: 590px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: flex-start;\r\n\r\n        .info-title {\r\n          height: 30px;\r\n          margin-bottom: 30px;\r\n          object-fit: contain;\r\n        }\r\n\r\n        .info-text {\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #5A5A5A;\r\n          line-height: 28px;\r\n          max-height: 160px;\r\n          overflow: auto;\r\n        }\r\n      }\r\n\r\n      .info-img {\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAwEA,IAAAA,gBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,sBAAA;MACAC,OAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,uCAAA,OAAAR,WAAA,EAAAS,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAT,sBAAA,GAAAY,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAK,KAAA,GAAAF,QAAA,CAAAE,KAAA;QACAL,KAAA,CAAAR,OAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAC,aAAA,EAAAL,GAAA,CAAAK,aAAA;UACAC,KAAA,EAAAN,GAAA,CAAAO;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}