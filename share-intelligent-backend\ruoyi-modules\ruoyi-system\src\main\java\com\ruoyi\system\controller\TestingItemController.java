package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.dto.TestingItemDTO;
import com.ruoyi.system.domain.dto.TestingItemWithLabsDTO;
import com.ruoyi.system.service.ILaboratoryInfoService;
import com.ruoyi.system.service.ITestingItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.TestingItem;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 检测项目Controller
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RestController
@RequestMapping("/testingItem")
public class TestingItemController extends BaseController
{
    @Autowired
    private ITestingItemService testingItemService;

    @Autowired
    private ILaboratoryInfoService laboratoryInfoService;

    /**
     * 查询检测项目列表
     */

    @GetMapping("/list")
    public TableDataInfo list(TestingItem testingItem)
    {
        startPage();
        List<TestingItem> list = testingItemService.selectTestingItemList(testingItem);
        return getDataTable(list);
    }

    /**
     * 导出检测项目列表
     */

    @Log(title = "检测项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestingItem testingItem)
    {
        List<TestingItem> list = testingItemService.selectTestingItemList(testingItem);
        ExcelUtil<TestingItem> util = new ExcelUtil<TestingItem>(TestingItem.class);
        util.exportExcel(response, list, "检测项目数据");
    }

    /**
     * 获取检测项目详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(testingItemService.selectTestingItemById(id));
    }

    /**
     * 获取所有实验室列表
     */
    @GetMapping("/labs")
    public AjaxResult listLabs()
    {
        LaboratoryInfo laboratoryInfo = new LaboratoryInfo();
        laboratoryInfo.setStatus("0"); // 只查询状态正常的实验室
        List<LaboratoryInfo> list = laboratoryInfoService.selectLaboratoryInfoList(laboratoryInfo);
        return success(list);
    }

    /**
     * 新增检测项目
     */

    @Log(title = "检测项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestingItem testingItem)
    {
        return toAjax(testingItemService.insertTestingItem(testingItem));
    }

    /**
     * 新增检测项目并关联实验室
     */

    @Log(title = "检测项目", businessType = BusinessType.INSERT)
    @PostMapping("/withLabs")
    public AjaxResult addWithLabs(@RequestBody TestingItemDTO testingItemDTO)
    {
        return toAjax(testingItemService.insertTestingItemWithLabs(testingItemDTO));
    }

    /**
     * 查询检测项目列表（包含关联实验室）
     */

    @GetMapping("/withLabs/list")
    public TableDataInfo listWithLabs(TestingItem testingItem)
    {
        startPage();
        List<TestingItemWithLabsDTO> list = testingItemService.selectTestingItemWithLabsList(testingItem);
        return getDataTable(list);
    }

    /**
     * 获取检测项目详细信息（包含关联实验室）
     */

    @GetMapping(value = "/withLabs/{id}")
    public AjaxResult getInfoWithLabs(@PathVariable("id") Long id)
    {
        return success(testingItemService.selectTestingItemWithLabsById(id));
    }

    /**
     * 修改检测项目
     */

    @Log(title = "检测项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestingItem testingItem)
    {
        return toAjax(testingItemService.updateTestingItem(testingItem));
    }

    /**
     * 修改检测项目并更新关联实验室
     */

    @Log(title = "检测项目", businessType = BusinessType.UPDATE)
    @PutMapping("/withLabs")
    public AjaxResult editWithLabs(@RequestBody TestingItemDTO testingItemDTO)
    {
        return toAjax(testingItemService.updateTestingItemWithLabs(testingItemDTO));
    }

    /**
     * 删除检测项目
     */

    @Log(title = "检测项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(testingItemService.deleteTestingItemByIds(ids));
    }
    
    /**
     * 查询检测项目列表并左联查检测实验室
     */
    @GetMapping("/leftJoinLabs")
    public TableDataInfo listLeftJoinLabs(String labType)
    {
        startPage();
        List<TestingItemWithLabsDTO> list = testingItemService.selectTestingItemLeftJoinLabs(labType);
        return getDataTable(list);
    }
}
