<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucCaseMapper">
    
    <resultMap type="UucCase" id="UucCaseResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="subTitle"    column="sub_title"    />
        <result property="logo"    column="logo"    />
        <result property="info"    column="info"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucCaseVo">
        select id, title, sub_title, logo, info, sort, create_by, create_time, update_by, update_time from uuc_case
    </sql>

    <select id="selectUucCaseList" parameterType="UucCase" resultMap="UucCaseResult">
        <include refid="selectUucCaseVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="subTitle != null  and subTitle != ''"> and sub_title like concat('%', #{subTitle}, '%')</if>
            <if test="info != null  and info != ''"> and info like concat('%', #{info}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppCaseList" parameterType="UucCase" resultMap="UucCaseResult">
        <include refid="selectUucCaseVo"/>
        order by sort desc
    </select>

    <select id="selectUucCaseById" parameterType="Long" resultMap="UucCaseResult">
        <include refid="selectUucCaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucCase" parameterType="UucCase" useGeneratedKeys="true" keyProperty="id">
        insert into uuc_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="subTitle != null and subTitle != ''">sub_title,</if>
            <if test="logo != null and logo != ''">logo,</if>
            <if test="info != null and info != ''">info,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="subTitle != null and subTitle != ''">#{subTitle},</if>
            <if test="logo != null and logo != ''">#{logo},</if>
            <if test="info != null and info != ''">#{info},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucCase" parameterType="UucCase">
        update uuc_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="subTitle != null and subTitle != ''">sub_title = #{subTitle},</if>
            <if test="logo != null and logo != ''">logo = #{logo},</if>
            <if test="info != null and info != ''">info = #{info},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucCaseById" parameterType="Long">
        delete from uuc_case where id = #{id}
    </delete>

    <delete id="deleteUucCaseByIds" parameterType="String">
        delete from uuc_case where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>