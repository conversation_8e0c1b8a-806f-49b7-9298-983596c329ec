package com.ruoyi.biz.shop.config;

import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class WechatPayJcscConfig {

    @Value("${wx.pay.merchantId}")
    private String merchantId;

    @Value("${wx.pay.privateKeyPath}")
    private String privateKeyPath;

    @Value("${wx.pay.merchantSerialNumber}")
    private String merchantSerialNumber;

    @Value("${wx.pay.apiV3Key}")
    private String apiV3Key;

//    @Bean
//    public RSAAutoCertificateConfig jcscWeChatPayConfig(){
//        RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
//                        .merchantId(merchantId)
//                        .privateKeyFromPath(privateKeyPath)
//                        .merchantSerialNumber(merchantSerialNumber)
//                        .apiV3Key(apiV3Key)
//                        .build();
//        return config;
//    }

//    @Bean
//    public NativePayService jcscWechatPayNativePayService(){
//        NativePayService weChatPayNativePayService = new NativePayService.Builder().config(jcscWeChatPayConfig()).build();
//        return weChatPayNativePayService;
//    }

}
