package com.ruoyi.auth.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Feign配置类
 * 优化Feign调用性能和超时设置
 * 
 * <AUTHOR>
 */
@Configuration
public class FeignConfig {

    /**
     * Feign请求超时配置
     * 增加连接超时和读取超时时间
     */
    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(
            5000,  // 连接超时时间：5秒
            30000, // 读取超时时间：30秒
            true   // 跟随重定向
        );
    }

    /**
     * Feign重试配置
     * 配置重试策略以提高调用成功率
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔100ms，最大重试间隔1秒，最大重试次数3次
        return new Retryer.Default(100, TimeUnit.SECONDS.toMillis(1), 3);
    }

    /**
     * Feign日志级别配置
     * 用于调试Feign调用问题
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        // 生产环境建议使用BASIC，开发环境可以使用FULL
        return Logger.Level.BASIC;
    }
}
