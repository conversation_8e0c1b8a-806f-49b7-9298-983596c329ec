package com.ruoyi.sso.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.sso.service.SSOUserManagementService;
import com.ruoyi.sso.service.SSOUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * SSO用户管理控制器
 * 提供给其他系统调用的用户管理接口
 * 
 * <AUTHOR>
 */
@Api(tags = "SSO用户管理")
@RestController
@RequestMapping("/sso/user")
public class SSOUserManagementController extends BaseController {

    @Autowired
    private SSOUserManagementService ssoUserManagementService;

    @Autowired
    private SSOUserService ssoUserService;

    /**
     * 为主系统注册创建SSO用户
     * 仅供内部服务调用
     */
    @ApiOperation("创建SSO用户")
    @PostMapping("/create")
    public R<Boolean> createSSOUser(@RequestParam String memberPhone,
                                   @RequestParam String memberRealName,
                                   @RequestParam String password,
                                   @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        // 验证调用来源
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("无权限访问");
        }

        try {
            boolean result = ssoUserManagementService.createSSOUserForRegistration(
                memberPhone, memberRealName, password);
            return R.ok(result);
        } catch (Exception e) {
            logger.error("创建SSO用户失败", e);
            return R.fail("创建SSO用户失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO用户是否存在
     * 供其他系统查询使用
     */
    @ApiOperation("检查用户是否存在")
    @GetMapping("/exists/{phone}")
    public R<Boolean> checkUserExists(@PathVariable String phone,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        // 验证调用来源
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("无权限访问");
        }

        try {
            SSOUser user = ssoUserService.selectSSOUserByUsername(phone);
            return R.ok(user != null);
        } catch (Exception e) {
            logger.error("检查用户存在性失败", e);
            return R.fail("检查用户存在性失败: " + e.getMessage());
        }
    }

    /**
     * 获取SSO用户基础信息
     * 供其他系统获取用户信息使用
     */
    @ApiOperation("获取用户基础信息")
    @GetMapping("/info/{phone}")
    public R<SSOUser> getUserInfo(@PathVariable String phone,
                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        // 验证调用来源
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("无权限访问");
        }

        try {
            SSOUser user = ssoUserService.selectSSOUserByUsername(phone);
            if (user != null) {
                // 清除敏感信息
                user.setPassword(null);
            }
            return R.ok(user);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新SSO用户密码
     * 主系统密码重置时调用
     */
    @ApiOperation("更新用户密码")
    @PutMapping("/password")
    public R<Boolean> updateUserPassword(@RequestParam String phone,
                                        @RequestParam String password,
                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        // 验证调用来源
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("无权限访问");
        }

        try {
            boolean success = ssoUserManagementService.updateSSOUserPassword(phone, password);
            if (success) {
                logger.info("SSO用户密码更新成功: {}", phone);
                return R.ok(true);
            } else {
                logger.warn("SSO用户密码更新失败: {}", phone);
                return R.fail("密码更新失败");
            }
        } catch (Exception e) {
            logger.error("更新SSO用户密码异常: {}", phone, e);
            return R.fail("密码更新异常: " + e.getMessage());
        }
    }

    /**
     * 批量同步现有用户到SSO
     * 用于数据迁移
     */
    @ApiOperation("批量同步用户")
    @PostMapping("/batch-sync")
    public R<Integer> batchSyncUsers(@RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        // 验证调用来源
        if (!SecurityConstants.INNER.equals(source)) {
            return R.fail("无权限访问");
        }

        try {
            int count = ssoUserManagementService.batchSyncMembersToSSO();
            return R.ok(count);
        } catch (Exception e) {
            logger.error("批量同步用户失败", e);
            return R.fail("批量同步用户失败: " + e.getMessage());
        }
    }
}
