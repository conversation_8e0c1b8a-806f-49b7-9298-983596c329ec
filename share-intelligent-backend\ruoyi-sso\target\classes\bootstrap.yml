# Tomcat
server:
  port: 9100

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-sso
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true

# SSO服务配置
sso:
  # 服务基本信息
  service:
    name: SSO认证服务
    version: 1.0.0
    description: 统一单点登录认证服务
  
  # 支持的客户端系统
  clients:
    backend:
      name: 复合材料共享智造平台
      url: http://localhost:9200
      callback-url: http://localhost:9200/sso/callback
      database: industry
      secret: "backend_2024#RuoYi@Share$Key!8888"
    market:
      name: 智能市场系统
      url: http://localhost:9700
      callback-url: http://localhost:9700/sso/callback
      database: market
      secret: "market_2024#RuoYi@Share$Key!9999"
  
  # Token配置
  token:
    # SSO Token有效期（分钟）
    expire-minutes: 30
    # 访问Token有效期（分钟）
    access-token-expire-minutes: 480
    # 刷新Token有效期（天）
    refresh-token-expire-days: 7
    # Token签名密钥（建议生产环境使用更复杂的密钥）
    secret: "sso_jwt_2024_RuoYi#Share@Intelligent$Key!9527"
    # Token前缀
    prefix: "sso_token:"
    access-prefix: "access_token:"
    refresh-prefix: "refresh_token:"
  
  # 会话配置
  session:
    # 会话超时时间（分钟）
    timeout-minutes: 480
    # 最大并发会话数
    max-sessions: 1
    # 会话前缀
    prefix: "sso_session:"
  
  # 安全配置
  security:
    # 是否启用CSRF保护
    csrf-enabled: false
    # 允许的跨域来源
    allowed-origins:
      - http://localhost:9200
      - http://localhost:8081
      - http://localhost:80
      - http://localhost:8000
    # 登录失败最大尝试次数
    max-login-attempts: 5
    # 登录失败锁定时间（分钟）
    lockout-duration-minutes: 15
  
  # 缓存配置
  cache:
    # Redis前缀
    redis-prefix: "sso:"
    # 缓存过期时间（秒）
    default-expire-seconds: 3600

# 全网智能通讯平台配置
qwt:
  login-name: LHBA2010217
  password: LHBA2010217a!
  sign-name: 【易复材】
  api-url: https://wosdk.028lk.com:7072/Api
  fee-type: 2

# Feign配置优化
feign:
  # 启用OkHttp客户端（性能更好）
  okhttp:
    enabled: true
  # 启用压缩
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
  # 启用饥饿加载（解决第一次调用慢的问题）
  eager-load:
    enabled: true
    clients: ruoyi-sso,ruoyi-system,ruoyi-member
  # 客户端配置
  client:
    config:
      default:
        # 连接超时时间（毫秒）
        connectTimeout: 5000
        # 读取超时时间（毫秒）
        readTimeout: 30000
        # 日志级别
        loggerLevel: basic
      # SSO服务特定配置
      ruoyi-sso:
        connectTimeout: 3000
        readTimeout: 15000
        loggerLevel: basic

# Ribbon负载均衡配置
ribbon:
  # 连接超时时间
  ConnectTimeout: 3000
  # 读取超时时间
  ReadTimeout: 15000
  # 重试次数
  MaxAutoRetries: 1
  # 切换服务器重试次数
  MaxAutoRetriesNextServer: 1
  # 是否所有操作都重试
  OkToRetryOnAllOperations: false
  # 启用饥饿加载
  eager-load:
    enabled: true
    clients: ruoyi-sso,ruoyi-system,ruoyi-member
  # 服务列表刷新间隔（秒）
  ServerListRefreshInterval: 30
