{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\supply.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\supply.vue", "mtime": 1750385853721}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovLyBpbXBvcnQgeyBwcm9kdWN0IH0gZnJvbSAicmFtZGEiOw0KLy8gaW1wb3J0IHsgZ2V0IH0gZnJvbSAic29ydGFibGVqcyI7DQppbXBvcnQgeyBsaXN0RGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOw0KaW1wb3J0IHsgYXBwbGljYXRpb25EYXRhLCBhcHBsaWNhdGlvbkFkZCwgc3VwcGx5QWRkIH0gZnJvbSAiQC9hcGkvcmVsZWFzZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaW1hZ2VVcmxMaXN0OiBbXSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICB0eXBlOiAiIiwNCiAgICAgICAgZGVzY3JpcHRpb246ICIiLA0KICAgICAgICB0ZWNobm9sb2d5Q2F0ZWdvcnk6IFtdLA0KICAgICAgICBwcm9kdWN0Q2F0ZWdvcnk6IFtdLA0KICAgICAgICAvLyBhcHBsaWNhdGlvbkFyZWE6IFtdLA0KICAgICAgICAvLyBpbWFnZVVybDogW10sDQogICAgICAgIGNvb3BlcmF0aW9uVHlwZTogIiIsDQogICAgICAgIHByb2Nlc3M6ICIiLA0KICAgICAgICAvLyBhdHRhY2htZW50OiBbXSwNCiAgICAgICAgb3JnYW5pemF0aW9uOiAiIiwNCiAgICAgICAgY29udGFjdDogIiIsDQogICAgICAgIHBob25lOiAiIiwNCiAgICAgIH0sDQogICAgICBzdXBwbHlUeXBlTGlzdDogW10sDQogICAgICB0ZWNoVHlwZUxpc3Q6IFtdLA0KICAgICAgY29vcGVyYXRpb25Nb2RlT3B0aW9uczogW10sDQogICAgICBwcm9kdWN0U3RhZ2VMaXN0OiBbXSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgdGl0bGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5L6b57uZ5qCH6aKYIiwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgICB0eXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeS+m+e7meexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGRlc2NyaXB0aW9uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeS+m+e7meaPj+i/sCIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICB0ZWNobm9sb2d5Q2F0ZWdvcnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5pyN5Yqh57G75YirIiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcHJvZHVjdENhdGVnb3J5OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeS6p+WTgeexu+WIqyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvb3BlcmF0aW9uVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlkIjkvZzmlrnlvI8iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgICBwcm9jZXNzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeS6p+WTgemYtuautSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIC8vIGFwcGxpY2F0aW9uQXJlYTogWw0KICAgICAgICAvLyAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlupTnlKjpoobln58iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICAvLyBdLA0KICAgICAgICAvLyBkaXNwbGF5UmVzdHJpY3Rpb25zOiBbDQogICAgICAgIC8vICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeWxleekuumZkOWItiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIC8vIF0sDQogICAgICAgIC8vIHN1bW1hcnk6IFsNCiAgICAgICAgLy8gICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZyA5rGC5o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIC8vIF0sDQogICAgICAgIG9yZ2FuaXphdGlvbjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7flhYjnu5Hlrprlhazlj7giLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7flhYjnu7TmiqTogZTns7vkuroiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcGhvbmU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35YWI57u05oqk6IGU57O755S16K+dIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgYXBwU2hvdzogZmFsc2UsDQogICAgICBhcHBpbnB1dDogIiIsDQogICAgICBkZW1hbmRhcHBMaXN0OiBbXSwNCiAgICAgIGRlbWFuZGFwcHRhZ0xpc3Q6IFtdLA0KICAgICAgcHJvZHVjdFR5cGVMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIGxldCB1c2VySW5mbyA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlcmluZm8iKSk7DQogICAgdGhpcy5mb3JtLm9yZ2FuaXphdGlvbiA9IHVzZXJJbmZvLm1lbWJlckNvbXBhbnlOYW1lOw0KICAgIHRoaXMuZm9ybS5jb250YWN0ID0gdXNlckluZm8ubWVtYmVyUmVhbE5hbWU7DQogICAgdGhpcy5mb3JtLnBob25lID0gdXNlckluZm8ubWVtYmVyUGhvbmU7DQogICAgdGhpcy5nZXRTdXBwbHlUeXBlRGljdCgpOw0KICAgIC8vIHRoaXMuZ2V0QXBwbGljYXRpb25EYXRhKCk7DQogICAgdGhpcy5nZXRUZWNoVHlwZURpY3QoKTsNCiAgICB0aGlzLmdldENvb3BlcmF0aW9uRGljdCgpOw0KICAgIHRoaXMuZ2V0UHJvZHVjdFN0YWdlRGljdCgpOw0KICAgIHRoaXMuZ2V0UHJvZHVjdFR5cGVEaWN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VUeXBlKGRhdGEpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGEpOw0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5mb3JtLnR5cGUpOw0KICAgIH0sDQogICAgLy8g5L6b57uZ57G75Z6L5a2X5YW4DQogICAgZ2V0U3VwcGx5VHlwZURpY3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogInN1cHBseV90eXBlIiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnN1cHBseVR5cGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pyN5Yqh57G75YirDQogICAgZ2V0VGVjaFR5cGVEaWN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJ0ZWNobm9sb2d5X2NhdGVnb3J5IiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnRlY2hUeXBlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOS6p+WTgeexu+WIqw0KICAgIGdldFByb2R1Y3RUeXBlRGljdCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IGRpY3RUeXBlOiAicHJvZHVjdF9jYXRlZ29yeSIgfTsNCiAgICAgIGxpc3REYXRhKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5wcm9kdWN0VHlwZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnByb2R1Y3RUeXBlTGlzdCwgJ+S6p+WTgeexu+WIqycpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlkIjkvZzmlrnlvI8NCiAgICBnZXRDb29wZXJhdGlvbkRpY3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogInN1cHBseV9jb29wZXJhdGlvbiIgfTsNCiAgICAgIGxpc3REYXRhKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5jb29wZXJhdGlvbk1vZGVPcHRpb25zID0gcmVzcG9uc2Uucm93czsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Lqn5ZOB6Zi25q61DQogICAgZ2V0UHJvZHVjdFN0YWdlRGljdCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IGRpY3RUeXBlOiAic3VwcGx5X3Byb2Nlc3MiIH07DQogICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMucHJvZHVjdFN0YWdlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uU3VibWl0KCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmZvcm0udGVjaG5vbG9neUNhdGVnb3J5ID0gdGhpcy5mb3JtLnRlY2hub2xvZ3lDYXRlZ29yeS5qb2luKCIsIik7DQogICAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RDYXRlZ29yeSA9IHRoaXMuZm9ybS5wcm9kdWN0Q2F0ZWdvcnkuam9pbigiLCIpOw0KICAgICAgICAgIHRoaXMuZm9ybS5pbWFnZVVybCA9DQogICAgICAgICAgICB0aGlzLmltYWdlVXJsTGlzdCAmJiB0aGlzLmltYWdlVXJsTGlzdC5sZW5ndGggPiAwDQogICAgICAgICAgICAgID8gdGhpcy5pbWFnZVVybExpc3RbMF0udXJsDQogICAgICAgICAgICAgIDogIiI7DQogICAgICAgICAgc3VwcGx5QWRkKHRoaXMuZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCgiL3N1cHBseURlbWFuZERvY2tpbmc/aW5kZXg9MSIpOw0KICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWPkeW4g+aIkOWKnyIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uQ2FuY2VsKCkgew0KICAgICAgdGhpcy4kZW1pdCgiY2FuY2VsIik7DQogICAgfSwNCiAgICBzaG93RGlhbG9nKCkgew0KICAgICAgdGhpcy5hcHBTaG93ID0gdHJ1ZTsNCiAgICAgIHRoaXMuYXBwaW5wdXQgPSAiIjsNCiAgICB9LA0KICAgIGdldEFwcGxpY2F0aW9uRGF0YSgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIGFwcGxpY2F0aW9uRmllbGROYW1lOiB0aGlzLmFwcGlucHV0LA0KICAgICAgfTsNCiAgICAgIGFwcGxpY2F0aW9uRGF0YShwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZGVtYW5kYXBwTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICAgIGxldCBhcnJMaXN0ID0gW107DQogICAgICAgICAgdGhpcy5kZW1hbmRhcHBMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGFyckxpc3QucHVzaChpdGVtLmFwcGxpY2F0aW9uRmllbGROYW1lKTsNCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIGlmICh0aGlzLmFwcGlucHV0ICYmIGFyckxpc3QuaW5kZXhPZih0aGlzLmFwcGlucHV0KSA9PT0gLTEpIHsNCiAgICAgICAgICAgIHRoaXMuYWRkQXBwbGkoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/lupTnlKjpoobln5/lpoLmnpzmsqHmnInmlbDmja7lsLHosIPmlrDlop4NCiAgICBhZGRBcHBsaSgpIHsNCiAgICAgIGxldCBkYXRhID0gew0KICAgICAgICBhcHBsaWNhdGlvbkZpZWxkTmFtZTogdGhpcy5hcHBpbnB1dCwNCiAgICAgIH07DQogICAgICBhcHBsaWNhdGlvbkFkZChkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmdldEFwcGxpY2F0aW9uRGF0YSgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNhbmNlbEJ0bigpIHsNCiAgICAgIHRoaXMuYXBwaW5wdXQgPSAiIjsNCiAgICAgIHRoaXMuZGVtYW5kYXBwTGlzdCA9IFtdOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["supply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "supply.vue", "sourceRoot": "src/views/release/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"供给标题\" prop=\"title\">\r\n        <el-input v-model=\"form.title\" maxlength=\"50\" show-word-limit placeholder=\"请输入供给标题\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"type\" label=\"供给类型\">\r\n        <el-radio-group v-model=\"form.type\" placeholder=\"请选择\" clearable @change=\"changeType\">\r\n          <el-radio v-for=\"dict in supplyTypeList\" :key=\"dict.dictValue\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"description\" label=\"供给描述（可按需求产品+应用行业+应用领域进行描述）\">\r\n        <el-input v-model=\"form.description\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\" show-word-limit\r\n          placeholder=\"该供给产品、成果、服务的原理、特点优势，与旧技术的对比数据成本情况等。填写越详细，匹配越准确。\" />\r\n      </el-form-item>\r\n      <el-form-item prop=\"technologyCategory\" label=\"服务类别\" v-if=\"form.type != 2\">\r\n        <el-checkbox-group v-model=\"form.technologyCategory\" placeholder=\"请选择\" clearable>\r\n          <el-checkbox v-for=\"dict in techTypeList\" :key=\"dict.dictValue\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"productCategory\" label=\"产品类别\" v-if=\"form.type == 2\">\r\n        <el-checkbox-group v-model=\"form.productCategory\" placeholder=\"请选择\" clearable>\r\n          <el-checkbox v-for=\"dict in productTypeList\" :key=\"dict.dictCode\" :label=\"dict.dictValue\"\r\n            :value=\"dict.dictValue\">{{ dict.dictLabel }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <!-- <el-form-item prop=\"applications\" label=\"应用领域\">\r\n        <div class=\"apptag\">\r\n          <i\r\n            class=\"el-icon-circle-plus-outline\"\r\n            style=\"font-size: 24px; color: rgba(187, 187, 187, 1)\"\r\n            @click=\"showDialog()\"\r\n          ></i>\r\n          <el-tag\r\n            v-for=\"(item, index) in demandapptagList\"\r\n            :key=\"index\"\r\n            closable\r\n            @close=\"closeTag(item, index)\"\r\n          >\r\n            {{ item.applicationFieldName }}\r\n          </el-tag>\r\n        </div>\r\n      </el-form-item> -->\r\n      <el-form-item label=\"场景图片\" prop=\"imageUrl\">\r\n        <ImageUpload :limit=\"1\" v-model=\"imageUrlList\" />\r\n        <!-- <ImageUpload :limit=\"1\" v-model=\"form.imageUrl\" /> -->\r\n      </el-form-item>\r\n      <el-form-item label=\"合作方式\" prop=\"cooperationType\">\r\n        <el-select v-model=\"form.cooperationType\" placeholder=\"请选择\" clearable style=\"width: 100%\">\r\n          <el-option v-for=\"dict in cooperationModeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品阶段\" prop=\"process\">\r\n        <el-select v-model=\"form.process\" placeholder=\"请选择\" clearable style=\"width: 100%\">\r\n          <el-option v-for=\"dict in productStageList\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.attachment\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\">\r\n        <el-input disabled v-model=\"form.organization\" placeholder=\"请先绑定公司\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\">\r\n        <el-input disabled v-model=\"form.contact\" placeholder=\"请先维护联系人\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\">\r\n        <el-input disabled v-model=\"form.phone\" placeholder=\"请先维护联系方式\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!-- 应用领域弹窗 -->\r\n    <div>\r\n      <el-dialog title=\"\" :visible.sync=\"appShow\" width=\"30%\">\r\n        <div>\r\n          <div class=\"searchBoxApp\">\r\n            <el-input v-model=\"appinput\" placeholder=\"请输入内容\" style=\"width: 90%\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchApp()\"></el-button>\r\n            </el-input>\r\n            <span class=\"cancelBtn\" @click=\"cancelBtn()\">取消</span>\r\n          </div>\r\n\r\n          <div style=\"min-height: 250px; overflow-y: scroll\">\r\n            <div v-for=\"(item, index) in demandappList\" :key=\"index\" class=\"selectBottom\" @click=\"selectApp(item)\">\r\n              <i class=\"el-icon-search\"></i>&nbsp;&nbsp;&nbsp;{{\r\n                item.applicationFieldName\r\n              }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// import { product } from \"ramda\";\r\n// import { get } from \"sortablejs\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { applicationData, applicationAdd, supplyAdd } from \"@/api/release\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      imageUrlList: [],\r\n      form: {\r\n        title: \"\",\r\n        type: \"\",\r\n        description: \"\",\r\n        technologyCategory: [],\r\n        productCategory: [],\r\n        // applicationArea: [],\r\n        // imageUrl: [],\r\n        cooperationType: \"\",\r\n        process: \"\",\r\n        // attachment: [],\r\n        organization: \"\",\r\n        contact: \"\",\r\n        phone: \"\",\r\n      },\r\n      supplyTypeList: [],\r\n      techTypeList: [],\r\n      cooperationModeOptions: [],\r\n      productStageList: [],\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: \"请输入供给标题\", trigger: \"blur\" }],\r\n        type: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"change\" },\r\n        ],\r\n        description: [\r\n          { required: true, message: \"请输入供给描述\", trigger: \"blur\" },\r\n        ],\r\n        technologyCategory: [\r\n          { required: true, message: \"请选择服务类别\", trigger: \"change\" },\r\n        ],\r\n        productCategory: [\r\n          { required: true, message: \"请选择产品类别\", trigger: \"change\" },\r\n        ],\r\n        cooperationType: [\r\n          { required: true, message: \"请选择合作方式\", trigger: \"change\" },\r\n        ],\r\n        process: [\r\n          { required: true, message: \"请选择产品阶段\", trigger: \"change\" },\r\n        ],\r\n        // applicationArea: [\r\n        //   { required: true, message: \"请选择应用领域\", trigger: \"change\" },\r\n        // ],\r\n        // displayRestrictions: [\r\n        //   { required: true, message: \"请选择展示限制\", trigger: \"change\" },\r\n        // ],\r\n        // summary: [\r\n        //   { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        // ],\r\n        organization: [\r\n          { required: true, message: \"请先绑定公司\", trigger: \"blur\" },\r\n        ],\r\n        contact: [\r\n          { required: true, message: \"请先维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"请先维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      appShow: false,\r\n      appinput: \"\",\r\n      demandappList: [],\r\n      demandapptagList: [],\r\n      productTypeList: [],\r\n    };\r\n  },\r\n  created() {\r\n    let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    this.form.organization = userInfo.memberCompanyName;\r\n    this.form.contact = userInfo.memberRealName;\r\n    this.form.phone = userInfo.memberPhone;\r\n    this.getSupplyTypeDict();\r\n    // this.getApplicationData();\r\n    this.getTechTypeDict();\r\n    this.getCooperationDict();\r\n    this.getProductStageDict();\r\n    this.getProductTypeDict();\r\n  },\r\n  methods: {\r\n    changeType(data) {\r\n      // console.log(data);\r\n      // console.log(this.form.type);\r\n    },\r\n    // 供给类型字典\r\n    getSupplyTypeDict() {\r\n      let params = { dictType: \"supply_type\" };\r\n      listData(params).then((response) => {\r\n        this.supplyTypeList = response.rows;\r\n      });\r\n    },\r\n    // 服务类别\r\n    getTechTypeDict() {\r\n      let params = { dictType: \"technology_category\" };\r\n      listData(params).then((response) => {\r\n        this.techTypeList = response.rows;\r\n      });\r\n    },\r\n    // 产品类别\r\n    getProductTypeDict() {\r\n      let params = { dictType: \"product_category\" };\r\n      listData(params).then((response) => {\r\n        this.productTypeList = response.rows;\r\n        console.log(this.productTypeList, '产品类别');\r\n      });\r\n    },\r\n    // 合作方式\r\n    getCooperationDict() {\r\n      let params = { dictType: \"supply_cooperation\" };\r\n      listData(params).then((response) => {\r\n        this.cooperationModeOptions = response.rows;\r\n      });\r\n    },\r\n    // 产品阶段\r\n    getProductStageDict() {\r\n      let params = { dictType: \"supply_process\" };\r\n      listData(params).then((response) => {\r\n        this.productStageList = response.rows;\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.technologyCategory = this.form.technologyCategory.join(\",\");\r\n          this.form.productCategory = this.form.productCategory.join(\",\");\r\n          this.form.imageUrl =\r\n            this.imageUrlList && this.imageUrlList.length > 0\r\n              ? this.imageUrlList[0].url\r\n              : \"\";\r\n          supplyAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              // this.$router.push(\"/supplyDemandDocking?index=1\");\r\n              this.$router.go(-1);\r\n              this.$message.success(\"发布成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$emit(\"cancel\");\r\n    },\r\n    showDialog() {\r\n      this.appShow = true;\r\n      this.appinput = \"\";\r\n    },\r\n    getApplicationData() {\r\n      let params = {\r\n        applicationFieldName: this.appinput,\r\n      };\r\n      applicationData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.demandappList = res.rows;\r\n          let arrList = [];\r\n          this.demandappList.forEach((item) => {\r\n            arrList.push(item.applicationFieldName);\r\n          });\r\n\r\n          if (this.appinput && arrList.indexOf(this.appinput) === -1) {\r\n            this.addAppli();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    //应用领域如果没有数据就调新增\r\n    addAppli() {\r\n      let data = {\r\n        applicationFieldName: this.appinput,\r\n      };\r\n      applicationAdd(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.getApplicationData();\r\n        }\r\n      });\r\n    },\r\n    cancelBtn() {\r\n      this.appinput = \"\";\r\n      this.demandappList = [];\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}