09:14:53.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:14:54.929 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06431772-5467-4aa5-b56d-579944f8b3aa_config-0
09:14:55.008 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:55.050 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:55.063 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:55.213 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 147 ms to scan 237 urls, producing 0 keys and 0 values 
09:14:55.223 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:55.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:55.248 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:55.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 143 ms to scan 237 urls, producing 0 keys and 0 values 
09:14:55.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:55.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
09:14:55.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
09:14:55.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:55.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:55.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:14:57.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295696775_127.0.0.1_50282
09:14:57.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Notify connected event to listeners.
09:14:57.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:57.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:14:57.189 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:15:02.880 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:15:03.293 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22b1c25a-b643-4ee0-8c0e-cac88279b153
09:15:03.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] RpcClient init label, labels = {module=naming, source=sdk}
09:15:03.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:03.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:03.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295703306_127.0.0.1_50354
09:15:03.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Notify connected event to listeners.
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:15:03.801 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:15:04.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0
09:15:04.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:04.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295704851_127.0.0.1_50367
09:15:04.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Notify connected event to listeners.
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:15:05.746 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:15:05.748 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:15:05.928 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:15:05.929 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:15:06.104 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
09:15:06.138 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.393 seconds (JVM running for 14.942)
09:15:06.146 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:15:06.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
09:15:06.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
09:15:06.701 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:15:06.702 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:15:36.095 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:15:36.095 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:17:53.975 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:17:58.171 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0
09:17:58.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 109 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:58.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:58.475 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:58.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 283 ms to scan 237 urls, producing 0 keys and 0 values 
09:17:58.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:58.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:58.828 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:59.114 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 282 ms to scan 237 urls, producing 0 keys and 0 values 
09:17:59.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:59.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/716206126
09:17:59.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1901648626
09:17:59.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:59.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:59.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:03.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:06.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:09.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:09.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:09.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:11.139 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:18:14.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:16.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:19.284 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:21.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:22.051 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:18:24.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:25.716 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 453c6e63-a356-4fd1-ba77-b43e14aaed27
09:18:25.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] RpcClient init label, labels = {module=naming, source=sdk}
09:18:25.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:25.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:25.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:25.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:27.001 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:27.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:29.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:30.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:32.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:32.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:32.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:32.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:33.404 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:18:35.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:36.352 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:36.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/716206126
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1901648626
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:38.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:39.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:39.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:40.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:41.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:42.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:42.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:42.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:42.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:44.229 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:45.861 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:18:45.861 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d359c69[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:18:45.862 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@28f6a008[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 16]
09:18:45.862 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:46.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Client is shutdown, stop reconnect to server
09:18:46.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.795 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:19:49.435 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:19:50.756 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0
09:19:50.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:50.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:50.980 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:51.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 248 ms to scan 237 urls, producing 0 keys and 0 values 
09:19:51.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:51.259 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:51.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:51.476 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 199 ms to scan 237 urls, producing 0 keys and 0 values 
09:19:51.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:51.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
09:19:51.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
09:19:51.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:51.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:51.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:53.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295992979_127.0.0.1_54935
09:19:53.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Notify connected event to listeners.
09:19:53.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:53.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:19:53.358 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:19:59.267 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:19:59.682 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6872d378-fcda-4402-b9e2-aa2bbe2b5119
09:19:59.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] RpcClient init label, labels = {module=naming, source=sdk}
09:19:59.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:59.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:59.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:59.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295999692_127.0.0.1_55084
09:19:59.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Notify connected event to listeners.
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:20:00.182 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:20:00.955 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4050b938-2720-4f54-85cb-df186df61393_config-0
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:00.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:00.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296000959_127.0.0.1_55091
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:01.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Notify connected event to listeners.
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:20:01.818 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:20:01.821 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:20:01.984 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:20:01.986 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:20:01.988 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:20:01.988 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:20:02.404 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:20:02.404 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28c75c93[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:20:02.404 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750295999692_127.0.0.1_55084
09:20:02.407 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750295999692_127.0.0.1_55084]Ignore complete event,isRunning:false,isAbandon=false
09:20:02.412 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3fc7abf6[Running, pool size = 28, active threads = 0, queued tasks = 0, completed tasks = 28]
09:21:37.487 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:21:38.999 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0
09:21:39.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:39.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:39.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:39.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 224 ms to scan 237 urls, producing 0 keys and 0 values 
09:21:39.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:39.464 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:39.484 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:39.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 206 ms to scan 237 urls, producing 0 keys and 0 values 
09:21:39.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:39.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
09:21:39.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
09:21:39.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:39.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:39.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:41.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296101330_127.0.0.1_55916
09:21:41.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Notify connected event to listeners.
09:21:41.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:41.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:41.768 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:21:47.698 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:21:48.115 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cebefb3-73d1-4a5a-9223-ed648bab0093
09:21:48.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] RpcClient init label, labels = {module=naming, source=sdk}
09:21:48.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:48.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:48.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:48.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296108128_127.0.0.1_55999
09:21:48.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Notify connected event to listeners.
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:48.671 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:21:49.580 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2870be57-dc7a-4d0e-a943-006b972122c1_config-0
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:49.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296109587_127.0.0.1_56003
09:21:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:49.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Notify connected event to listeners.
09:21:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:50.806 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:21:50.807 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:21:50.810 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:21:50.811 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:21:51.037 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
09:21:51.142 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 14.568 seconds (JVM running for 15.974)
09:21:51.173 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:21:51.175 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
09:21:51.176 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
09:21:51.539 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:21:51.546 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:22:20.948 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:22:20.950 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 6
11:27:44.369 [nacos-grpc-client-executor-2122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:27:44.369 [nacos-grpc-client-executor-2122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:27:44.879 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:27:44.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:27:45.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:27:45.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@640b7be5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:27:45.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750296108128_127.0.0.1_55999
11:27:45.214 [nacos-grpc-client-executor-2125] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750296108128_127.0.0.1_55999]Ignore complete event,isRunning:false,isAbandon=false
11:27:45.221 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1a51183b[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2126]
11:53:04.359 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:53:05.628 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0
11:53:05.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 58 ms to scan 1 urls, producing 3 keys and 6 values 
11:53:05.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
11:53:05.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
11:53:06.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 216 ms to scan 237 urls, producing 0 keys and 0 values 
11:53:06.042 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
11:53:06.063 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
11:53:06.073 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:53:06.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 206 ms to scan 237 urls, producing 0 keys and 0 values 
11:53:06.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:06.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
11:53:06.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
11:53:06.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:06.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:06.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:08.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305188054_127.0.0.1_53509
11:53:08.362 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Notify connected event to listeners.
11:53:08.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:08.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ea5b8d6-b324-4c76-860b-94aeb3e8c542_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
11:53:08.505 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:53:15.709 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:53:16.175 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2a2e54e-5c39-4694-a279-7d17dbfe6b23
11:53:16.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] RpcClient init label, labels = {module=naming, source=sdk}
11:53:16.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:53:16.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:53:16.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:53:16.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:16.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305196187_127.0.0.1_53689
11:53:16.295 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Notify connected event to listeners.
11:53:16.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:16.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
11:53:16.804 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:53:17.681 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4a0aed5f-9370-4010-adca-87ad52d413af_config-0
11:53:17.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:17.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
11:53:17.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
11:53:17.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:17.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:17.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:17.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305197687_127.0.0.1_53732
11:53:17.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Notify connected event to listeners.
11:53:17.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:17.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a0aed5f-9370-4010-adca-87ad52d413af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
11:53:18.591 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Receive server push request, request = NotifySubscriberRequest, requestId = 38
11:53:18.593 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Ack server push request, request = NotifySubscriberRequest, requestId = 38
11:53:18.760 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:53:18.761 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Ack server push request, request = NotifySubscriberRequest, requestId = 39
11:53:19.069 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
11:53:19.114 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.731 seconds (JVM running for 17.687)
11:53:19.122 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:53:19.124 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
11:53:19.124 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
11:53:19.634 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:53:19.638 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Ack server push request, request = NotifySubscriberRequest, requestId = 40
11:53:49.017 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Receive server push request, request = NotifySubscriberRequest, requestId = 43
11:53:49.017 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a2e54e-5c39-4694-a279-7d17dbfe6b23] Ack server push request, request = NotifySubscriberRequest, requestId = 43
13:38:52.845 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:52.849 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:53.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:53.188 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@75eb53e0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:53.188 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305196187_127.0.0.1_53689
13:38:53.191 [nacos-grpc-client-executor-1778] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305196187_127.0.0.1_53689]Ignore complete event,isRunning:false,isAbandon=false
13:38:53.200 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@78c3acc5[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1779]
13:39:06.474 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
13:39:07.798 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9226d296-a4e0-415e-822b-0b094c3b31f0_config-0
13:39:07.916 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 67 ms to scan 1 urls, producing 3 keys and 6 values 
13:39:07.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
13:39:08.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
13:39:08.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 219 ms to scan 237 urls, producing 0 keys and 0 values 
13:39:08.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:39:08.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
13:39:08.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
13:39:08.541 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 250 ms to scan 237 urls, producing 0 keys and 0 values 
13:39:08.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:39:08.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
13:39:08.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
13:39:08.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:39:08.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:39:08.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:10.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311550537_127.0.0.1_56544
13:39:10.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Notify connected event to listeners.
13:39:10.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:10.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9226d296-a4e0-415e-822b-0b094c3b31f0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
13:39:11.048 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
13:39:18.445 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:39:19.089 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 658f720d-7e6c-47ec-aec2-0fd7a180e79c
13:39:19.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] RpcClient init label, labels = {module=naming, source=sdk}
13:39:19.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:39:19.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:39:19.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:39:19.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:19.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311559104_127.0.0.1_56722
13:39:19.216 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Notify connected event to listeners.
13:39:19.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:19.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
13:39:19.838 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:39:20.788 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0
13:39:20.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:39:20.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
13:39:20.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
13:39:20.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:39:20.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:39:20.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:20.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311560797_127.0.0.1_56769
13:39:20.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Notify connected event to listeners.
13:39:20.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:20.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d09b739-9fb4-49b2-9f0c-66c9dfdd630d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
13:39:21.751 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Receive server push request, request = NotifySubscriberRequest, requestId = 59
13:39:21.753 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Ack server push request, request = NotifySubscriberRequest, requestId = 59
13:39:21.880 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Receive server push request, request = NotifySubscriberRequest, requestId = 60
13:39:21.880 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Ack server push request, request = NotifySubscriberRequest, requestId = 60
13:39:21.883 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Receive server push request, request = NotifySubscriberRequest, requestId = 61
13:39:21.883 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Ack server push request, request = NotifySubscriberRequest, requestId = 61
13:39:22.361 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
13:39:22.408 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 17.217 seconds (JVM running for 19.073)
13:39:22.418 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
13:39:22.419 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
13:39:22.420 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
13:39:22.865 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Receive server push request, request = NotifySubscriberRequest, requestId = 62
13:39:22.872 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Ack server push request, request = NotifySubscriberRequest, requestId = 62
13:39:29.425 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Receive server push request, request = NotifySubscriberRequest, requestId = 64
13:39:29.429 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [658f720d-7e6c-47ec-aec2-0fd7a180e79c] Ack server push request, request = NotifySubscriberRequest, requestId = 64
14:01:59.888 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:01:59.890 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:02:00.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:02:00.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26f7d280[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:02:00.218 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750311559104_127.0.0.1_56722
14:02:00.220 [nacos-grpc-client-executor-431] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750311559104_127.0.0.1_56722]Ignore complete event,isRunning:false,isAbandon=false
14:02:00.228 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@514cbb70[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 432]
14:02:08.751 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
14:02:09.801 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0
14:02:09.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:02:09.960 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
14:02:09.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:02:10.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 189 ms to scan 237 urls, producing 0 keys and 0 values 
14:02:10.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:02:10.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:02:10.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:02:10.387 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 172 ms to scan 237 urls, producing 0 keys and 0 values 
14:02:10.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:02:10.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
14:02:10.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
14:02:10.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:02:10.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:02:10.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:12.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312932005_127.0.0.1_60769
14:02:12.313 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Notify connected event to listeners.
14:02:12.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:12.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90e2bc2a-1787-4fec-a695-d13ac1d45e82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
14:02:12.450 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
14:02:19.500 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:02:20.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 057bd481-0113-4dfe-bc99-53f481149514
14:02:20.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] RpcClient init label, labels = {module=naming, source=sdk}
14:02:20.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:02:20.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:02:20.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:02:20.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:20.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312940059_127.0.0.1_60825
14:02:20.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Notify connected event to listeners.
14:02:20.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:20.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
14:02:20.600 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:02:21.415 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ddf61cdc-6821-447f-8975-2f7475005bf7_config-0
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:02:21.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:21.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312941422_127.0.0.1_60865
14:02:21.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Notify connected event to listeners.
14:02:21.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:21.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf61cdc-6821-447f-8975-2f7475005bf7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
14:02:22.339 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Receive server push request, request = NotifySubscriberRequest, requestId = 71
14:02:22.339 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Ack server push request, request = NotifySubscriberRequest, requestId = 71
14:02:22.516 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Receive server push request, request = NotifySubscriberRequest, requestId = 72
14:02:22.516 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Ack server push request, request = NotifySubscriberRequest, requestId = 72
14:02:22.517 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Receive server push request, request = NotifySubscriberRequest, requestId = 73
14:02:22.518 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Ack server push request, request = NotifySubscriberRequest, requestId = 73
14:02:22.788 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
14:02:22.827 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 14.953 seconds (JVM running for 16.581)
14:02:22.833 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:02:22.834 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
14:02:22.834 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
14:02:23.393 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Receive server push request, request = NotifySubscriberRequest, requestId = 74
14:02:23.399 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Ack server push request, request = NotifySubscriberRequest, requestId = 74
14:02:29.083 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:02:29.086 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057bd481-0113-4dfe-bc99-53f481149514] Ack server push request, request = NotifySubscriberRequest, requestId = 75
16:39:58.848 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:58.849 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:59.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:59.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@680bc72d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:59.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750312940059_127.0.0.1_60825
16:39:59.177 [nacos-grpc-client-executor-2650] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750312940059_127.0.0.1_60825]Ignore complete event,isRunning:false,isAbandon=false
16:39:59.186 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@180449cd[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2651]
16:40:10.264 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:40:11.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0
16:40:11.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:11.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:11.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:11.499 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 175 ms to scan 237 urls, producing 0 keys and 0 values 
16:40:11.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:11.524 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:11.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:11.701 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 164 ms to scan 237 urls, producing 0 keys and 0 values 
16:40:11.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:11.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/423109432
16:40:11.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1715602761
16:40:11.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:11.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:11.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:13.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322413144_127.0.0.1_50473
16:40:13.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Notify connected event to listeners.
16:40:13.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:13.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
16:40:13.642 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:40:20.204 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:40:20.639 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1
16:40:20.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] RpcClient init label, labels = {module=naming, source=sdk}
16:40:20.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:40:20.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:40:20.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:40:20.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:20.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322420651_127.0.0.1_50510
16:40:20.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Notify connected event to listeners.
16:40:20.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:20.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
16:40:21.184 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:40:21.984 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0
16:40:21.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:21.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/423109432
16:40:21.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1715602761
16:40:21.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:21.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:21.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:22.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322421989_127.0.0.1_50518
16:40:22.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:22.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Notify connected event to listeners.
16:40:22.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dbf3e6d6-6433-4edb-bc03-90af984ae20b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
16:40:22.889 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 108
16:40:22.889 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 108
16:40:23.053 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 110
16:40:23.053 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 110
16:40:23.055 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 109
16:40:23.055 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 109
16:40:23.237 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
16:40:23.279 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.788 seconds (JVM running for 15.396)
16:40:23.286 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
16:40:23.287 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
16:40:23.287 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
16:40:23.813 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 111
16:40:23.828 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 111
16:40:31.129 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 113
16:40:31.130 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 113
17:18:39.127 [nacos-grpc-client-executor-685] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 125
17:18:39.127 [nacos-grpc-client-executor-685] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 125
19:30:55.673 [nacos-grpc-client-executor-2891] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 246
19:30:55.674 [nacos-grpc-client-executor-2891] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 246
19:30:56.004 [nacos-grpc-client-executor-2892] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 247
19:30:56.004 [nacos-grpc-client-executor-2892] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 247
19:39:26.090 [nacos-grpc-client-executor-3059] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 249
19:39:26.091 [nacos-grpc-client-executor-3059] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 249
19:51:31.198 [nacos-grpc-client-executor-3310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 251
19:51:31.198 [nacos-grpc-client-executor-3310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 251
19:51:53.376 [nacos-grpc-client-executor-3314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Receive server push request, request = NotifySubscriberRequest, requestId = 253
19:51:53.377 [nacos-grpc-client-executor-3314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8227ba3d-00ee-4808-ab6c-dc68cfc1d2c1] Ack server push request, request = NotifySubscriberRequest, requestId = 253
20:15:10.155 [nacos-grpc-client-executor-2589] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 255
20:15:10.157 [nacos-grpc-client-executor-2589] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e4ff1d-d554-422d-ba54-b895d1b62cf2_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 255
20:15:15.016 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:15:15.018 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:15:15.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:15:15.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e5155f3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:15:15.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322420651_127.0.0.1_50510
20:15:15.360 [nacos-grpc-client-executor-3762] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750322420651_127.0.0.1_50510]Ignore complete event,isRunning:false,isAbandon=false
20:15:15.391 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5cb5d5e3[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 3763]
20:15:25.730 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
20:15:26.724 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0
20:15:26.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
20:15:26.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
20:15:26.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
20:15:27.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 170 ms to scan 234 urls, producing 0 keys and 0 values 
20:15:27.076 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
20:15:27.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
20:15:27.104 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
20:15:27.264 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 158 ms to scan 234 urls, producing 0 keys and 0 values 
20:15:27.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:27.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1707634104
20:15:27.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/2130606983
20:15:27.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:27.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:27.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
20:15:29.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750335328814_127.0.0.1_62287
20:15:29.130 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Notify connected event to listeners.
20:15:29.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:29.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e60e5d-4eef-439d-95e1-c6fecada1aca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/454248060
20:15:29.251 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
20:15:35.413 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
20:15:35.847 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2216c926-a3fa-484b-932b-34cf1d298a0b
20:15:35.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] RpcClient init label, labels = {module=naming, source=sdk}
20:15:35.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:15:35.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:15:35.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:15:35.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
20:15:35.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750335335860_127.0.0.1_62361
20:15:35.978 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Notify connected event to listeners.
20:15:35.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:35.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/454248060
20:15:36.441 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
20:15:37.167 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0
20:15:37.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:37.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1707634104
20:15:37.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/2130606983
20:15:37.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:37.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:37.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
20:15:37.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750335337174_127.0.0.1_62372
20:15:37.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:37.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Notify connected event to listeners.
20:15:37.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7c60db8-796a-4308-81d7-e17f0a24ca3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/454248060
20:15:38.090 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 256
20:15:38.092 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 256
20:15:38.337 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 257
20:15:38.338 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 257
20:15:38.341 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 260
20:15:38.342 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 260
20:15:38.344 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 259
20:15:38.345 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 259
20:15:38.346 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 261
20:15:38.346 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 261
20:15:38.349 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 258
20:15:38.349 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 258
20:15:38.641 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
20:15:38.695 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.714 seconds (JVM running for 15.304)
20:15:38.704 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
20:15:38.704 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
20:15:38.706 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
20:15:39.207 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Receive server push request, request = NotifySubscriberRequest, requestId = 262
20:15:39.214 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2216c926-a3fa-484b-932b-34cf1d298a0b] Ack server push request, request = NotifySubscriberRequest, requestId = 262
