# Tomcat
server:
  port: 9200

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-auth
  profiles:
    # 环境配置
#    active: dev
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# 验证码登录 密钥-私钥
rsa:
  privateKey: cf141ae3-5f7c-4d70-8fd3-7d0c95da355d

# 验证码登录 阿里云配置
ali:
  sign: 柠檬豆平台
  template: SMS_232910346

# 全网智能通讯平台配置
qwt:
  login-name: LHBA2010217
  password: LHBA2010217a!
  sign-name: 【易复材】
  api-url: https://wosdk.028lk.com:7072/Api
  fee-type: 2

# SSO客户端配置
sso:
  server:
    url: http://localhost:9100
  client:
    id: backend
    secret: "backend_2024#RuoYi@Share$Key!8888"
    callback-url: http://localhost:9200/sso/callback

# Feign配置优化
feign:
  # 启用OkHttp客户端（性能更好）
  okhttp:
    enabled: true
  # 启用压缩
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
  # 启用饥饿加载（解决第一次调用慢的问题）
  eager-load:
    enabled: true
    clients: ruoyi-sso,ruoyi-system,ruoyi-member
  # 客户端配置
  client:
    config:
      default:
        # 连接超时时间（毫秒）
        connectTimeout: 5000
        # 读取超时时间（毫秒）
        readTimeout: 30000
        # 日志级别
        loggerLevel: basic
      # SSO服务特定配置
      ruoyi-sso:
        connectTimeout: 3000
        readTimeout: 15000
        loggerLevel: basic

# Ribbon负载均衡配置
ribbon:
  # 连接超时时间
  ConnectTimeout: 3000
  # 读取超时时间
  ReadTimeout: 15000
  # 重试次数
  MaxAutoRetries: 1
  # 切换服务器重试次数
  MaxAutoRetriesNextServer: 1
  # 是否所有操作都重试
  OkToRetryOnAllOperations: false
  # 启用饥饿加载
  eager-load:
    enabled: true
    clients: ruoyi-sso,ruoyi-system,ruoyi-member
  # 服务列表刷新间隔（秒）
  ServerListRefreshInterval: 30
