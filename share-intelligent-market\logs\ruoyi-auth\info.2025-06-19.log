09:14:52.021 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:14:53.459 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0
09:14:53.591 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 77 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:53.661 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:53.683 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:53.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 259 ms to scan 209 urls, producing 0 keys and 0 values 
09:14:53.963 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:53.982 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:53.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:54.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 189 ms to scan 209 urls, producing 0 keys and 0 values 
09:14:54.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:54.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1216611732
09:14:54.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1237743088
09:14:54.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:54.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:14:55.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295695718_127.0.0.1_50279
09:14:56.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Notify connected event to listeners.
09:14:56.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:56.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9934b4aa-de20-46ba-a5c1-25e73c1165a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1780306540
09:14:56.114 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:14:58.753 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:14:58.754 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:58.754 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:14:59.012 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:01.399 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:02.043 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff
09:15:02.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] RpcClient init label, labels = {module=naming, source=sdk}
09:15:02.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:02.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:02.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:02.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:02.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295702052_127.0.0.1_50352
09:15:02.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:02.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Notify connected event to listeners.
09:15:02.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1780306540
09:15:03.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:15:03.562 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
09:15:03.788 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.689 seconds (JVM running for 14.077)
09:15:03.819 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
09:15:03.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
09:15:03.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
09:15:04.098 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:15:04.104 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42014a54-6ae8-49e9-b2fb-8af0a0ffe2ff] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:15:04.117 [RMI TCP Connection(3)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:17:50.621 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:17:55.159 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fcab012e-bd7c-4058-9a56-da09f6206032_config-0
09:17:55.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 61 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:55.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:55.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:55.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 185 ms to scan 209 urls, producing 0 keys and 0 values 
09:17:55.541 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:55.557 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:55.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:55.735 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 162 ms to scan 209 urls, producing 0 keys and 0 values 
09:17:55.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:55.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1521132614
09:17:55.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1818440653
09:17:55.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:55.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:55.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:17:59.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:02.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:05.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:05.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:05.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/849520318
09:18:07.847 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:18:11.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:12.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:12.863 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:18:12.865 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:12.866 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:18:13.258 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:14.841 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:17.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:17.603 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:19.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:20.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 54512e54-b668-4136-86e0-9be5aaffa5d8
09:18:20.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] RpcClient init label, labels = {module=naming, source=sdk}
09:18:20.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:20.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:20.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:20.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:22.511 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:22.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:24.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:25.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:26.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:26.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:26.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/849520318
09:18:28.142 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:30.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:18:30.709 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:18:30.710 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@489b7559[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:18:30.710 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4028aab2[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 8]
09:18:30.713 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:30.920 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54512e54-b668-4136-86e0-9be5aaffa5d8] Client is shutdown, stop reconnect to server
09:18:31.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:32.761 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 31e7c42a-d04b-40c7-869e-9bfcdddf8748
09:18:32.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] RpcClient init label, labels = {module=naming, source=sdk}
09:18:32.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:32.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:32.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:32.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:34.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:35.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:37.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:37.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:39.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:39.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/849520318
09:18:39.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31e7c42a-d04b-40c7-869e-9bfcdddf8748] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:40.127 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:18:40.127 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:18:40.185 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:18:40.189 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:18:40.558 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcab012e-bd7c-4058-9a56-da09f6206032_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:19:52.592 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:19:53.567 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0
09:19:53.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:53.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:53.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:53.875 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 176 ms to scan 209 urls, producing 0 keys and 0 values 
09:19:53.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:53.906 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:53.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:54.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 173 ms to scan 209 urls, producing 0 keys and 0 values 
09:19:54.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:54.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1100288091
09:19:54.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1866340787
09:19:54.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:54.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:54.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:55.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295995452_127.0.0.1_55074
09:19:55.722 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Notify connected event to listeners.
09:19:55.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:55.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dff169d-c2c7-4ce0-a8b9-59c539e06374_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1232948374
09:19:55.825 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:19:58.219 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:19:58.220 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:58.220 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:19:58.489 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:20:00.822 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:20:01.457 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 454fa06f-4589-489e-a69d-fc4029a75633
09:20:01.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] RpcClient init label, labels = {module=naming, source=sdk}
09:20:01.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:20:01.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:20:01.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:20:01.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:20:01.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296001465_127.0.0.1_55126
09:20:01.575 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Notify connected event to listeners.
09:20:01.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:01.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1232948374
09:20:02.947 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:20:03.438 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:20:03.439 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [454fa06f-4589-489e-a69d-fc4029a75633] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:20:03.602 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:20:03.603 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8eeb4c5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:20:03.603 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750296001465_127.0.0.1_55126
09:20:03.605 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750296001465_127.0.0.1_55126]Ignore complete event,isRunning:false,isAbandon=false
09:20:03.612 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@643a73fa[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 7]
09:20:03.682 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a62e8780-9fc1-45a5-af64-882e66b28e0c
09:20:03.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] RpcClient init label, labels = {module=naming, source=sdk}
09:20:03.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:20:03.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:20:03.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:20:03.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:20:03.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296003687_127.0.0.1_55138
09:20:03.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:03.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Notify connected event to listeners.
09:20:03.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a62e8780-9fc1-45a5-af64-882e66b28e0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1232948374
09:20:03.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:20:03.850 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:20:03.857 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:20:03.858 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:21:38.987 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:21:40.332 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0
09:21:40.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:40.442 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:40.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:40.638 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 176 ms to scan 209 urls, producing 0 keys and 0 values 
09:21:40.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:40.667 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:40.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:40.834 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 150 ms to scan 209 urls, producing 0 keys and 0 values 
09:21:40.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:40.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/950729555
09:21:40.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/388623360
09:21:40.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:40.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:40.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:42.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296102180_127.0.0.1_55918
09:21:42.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Notify connected event to listeners.
09:21:42.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:42.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40cd0636-8de3-4a08-b42f-aaf5da766d2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/298862004
09:21:42.560 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:21:44.948 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:21:44.949 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:44.949 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:21:45.203 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:47.499 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:48.185 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7
09:21:48.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] RpcClient init label, labels = {module=naming, source=sdk}
09:21:48.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:48.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:48.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:48.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296108196_127.0.0.1_56000
09:21:48.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:48.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Notify connected event to listeners.
09:21:48.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/298862004
09:21:49.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:21:49.825 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
09:21:50.084 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.858 seconds (JVM running for 13.361)
09:21:50.113 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
09:21:50.114 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
09:21:50.115 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
09:21:50.546 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:21:50.552 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e11c4ca-dbb8-4ac5-a878-2b3ea29d0ab7] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:21:50.561 [RMI TCP Connection(2)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:27:44.995 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:27:44.997 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:27:45.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:27:45.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@43300666[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:27:45.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750296108196_127.0.0.1_56000
11:27:45.326 [nacos-grpc-client-executor-1531] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750296108196_127.0.0.1_56000]Ignore complete event,isRunning:false,isAbandon=false
11:27:45.331 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@39124e27[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1532]
11:53:01.892 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:53:03.575 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0
11:53:03.680 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
11:53:03.726 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
11:53:03.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
11:53:03.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 220 ms to scan 209 urls, producing 0 keys and 0 values 
11:53:03.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
11:53:03.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
11:53:04.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
11:53:04.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 192 ms to scan 209 urls, producing 0 keys and 0 values 
11:53:04.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:04.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1422883946
11:53:04.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/2013332834
11:53:04.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:04.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:04.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:06.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305186118_127.0.0.1_53432
11:53:06.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Notify connected event to listeners.
11:53:06.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:06.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [578572fa-70fc-413a-ac60-5aee5fd11c8c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1492897838
11:53:06.607 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:53:09.715 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:53:09.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:53:09.717 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:53:10.011 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:53:13.126 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:53:13.869 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ba3014da-3435-4d13-8048-53136bbd2831
11:53:13.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] RpcClient init label, labels = {module=naming, source=sdk}
11:53:13.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:53:13.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:53:13.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:53:13.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305193881_127.0.0.1_53609
11:53:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:13.996 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Notify connected event to listeners.
11:53:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1492897838
11:53:15.640 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:53:15.687 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
11:53:15.941 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.241 seconds (JVM running for 17.146)
11:53:15.970 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
11:53:15.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
11:53:15.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
11:53:16.170 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:53:16.173 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Ack server push request, request = NotifySubscriberRequest, requestId = 37
11:53:16.536 [RMI TCP Connection(2)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:56:03.837 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:56:03.837 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba3014da-3435-4d13-8048-53136bbd2831] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:56:04.572 [http-nio-9700-exec-4] INFO  c.r.a.s.SSOClientService - [createLocalUser,226] - 创建本地用户成功: testuser
11:56:04.639 [http-nio-9700-exec-6] INFO  c.r.a.s.SSOClientService - [createLocalUser,226] - 创建本地用户成功: testuser
13:38:53.067 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:53.070 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:53.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:53.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1b6feacd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:53.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305193881_127.0.0.1_53609
13:38:53.407 [nacos-grpc-client-executor-1280] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305193881_127.0.0.1_53609]Ignore complete event,isRunning:false,isAbandon=false
13:38:53.421 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d3ffea8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1281]
13:39:01.110 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
13:39:02.711 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0
13:39:02.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 70 ms to scan 1 urls, producing 3 keys and 6 values 
13:39:02.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
13:39:02.896 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
13:39:03.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 202 ms to scan 209 urls, producing 0 keys and 0 values 
13:39:03.116 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:39:03.138 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
13:39:03.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
13:39:03.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 245 ms to scan 209 urls, producing 0 keys and 0 values 
13:39:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:39:03.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/779051277
13:39:03.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/512140711
13:39:03.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:39:03.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:39:03.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:06.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311545899_127.0.0.1_56351
13:39:06.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Notify connected event to listeners.
13:39:06.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:06.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [178be9f7-0e20-45cf-89e8-4a7720209ab0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1370979551
13:39:06.651 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
13:39:10.480 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:39:10.480 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:39:10.480 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
13:39:10.812 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:39:14.102 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:39:14.873 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24a283a3-ccfa-4e91-aa83-c3791f4bd9d7
13:39:14.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] RpcClient init label, labels = {module=naming, source=sdk}
13:39:14.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:39:14.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:39:14.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:39:14.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:15.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311554884_127.0.0.1_56637
13:39:15.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:15.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Notify connected event to listeners.
13:39:15.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1370979551
13:39:16.598 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:39:16.653 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
13:39:16.924 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.943 seconds (JVM running for 18.549)
13:39:16.953 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
13:39:16.953 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
13:39:16.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
13:39:17.133 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Receive server push request, request = NotifySubscriberRequest, requestId = 58
13:39:17.139 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24a283a3-ccfa-4e91-aa83-c3791f4bd9d7] Ack server push request, request = NotifySubscriberRequest, requestId = 58
13:39:17.444 [RMI TCP Connection(4)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:02:00.045 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:02:00.048 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:02:00.374 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:02:00.375 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48c5566[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:02:00.375 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750311554884_127.0.0.1_56637
14:02:00.377 [nacos-grpc-client-executor-285] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750311554884_127.0.0.1_56637]Ignore complete event,isRunning:false,isAbandon=false
14:02:00.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e25c2de[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 286]
14:02:04.559 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
14:02:06.222 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd92e67d-b036-4eac-b768-e217b6626d9a_config-0
14:02:06.337 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
14:02:06.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
14:02:06.411 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
14:02:06.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 232 ms to scan 209 urls, producing 0 keys and 0 values 
14:02:06.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:02:06.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
14:02:06.692 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
14:02:06.911 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 217 ms to scan 209 urls, producing 0 keys and 0 values 
14:02:06.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:02:06.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/435626542
14:02:06.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1064456966
14:02:06.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:02:06.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:02:06.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:09.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312928805_127.0.0.1_60621
14:02:09.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Notify connected event to listeners.
14:02:09.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:09.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd92e67d-b036-4eac-b768-e217b6626d9a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1835529707
14:02:09.264 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
14:02:12.410 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:02:12.412 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:02:12.413 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
14:02:12.766 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:02:15.652 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:02:16.435 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b443296-5ab5-438c-9589-dfef07646f35
14:02:16.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] RpcClient init label, labels = {module=naming, source=sdk}
14:02:16.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:02:16.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:02:16.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:02:16.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:16.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312936446_127.0.0.1_60796
14:02:16.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:16.552 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Notify connected event to listeners.
14:02:16.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1835529707
14:02:18.296 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:02:18.348 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
14:02:18.668 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.281 seconds (JVM running for 16.877)
14:02:18.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
14:02:18.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
14:02:18.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
14:02:18.847 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Receive server push request, request = NotifySubscriberRequest, requestId = 70
14:02:18.852 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b443296-5ab5-438c-9589-dfef07646f35] Ack server push request, request = NotifySubscriberRequest, requestId = 70
14:02:19.198 [RMI TCP Connection(4)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:39:58.991 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:58.993 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6d56fad4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750312936446_127.0.0.1_60796
16:39:59.320 [nacos-grpc-client-executor-1903] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750312936446_127.0.0.1_60796]Ignore complete event,isRunning:false,isAbandon=false
16:39:59.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2cfd5baa[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1904]
17:17:00.442 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:17:01.530 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f8bdc295-420a-4b82-9145-7c738670dfd0_config-0
17:17:01.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
17:17:01.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
17:17:01.671 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
17:17:01.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 174 ms to scan 207 urls, producing 0 keys and 0 values 
17:17:01.859 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
17:17:01.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
17:17:01.888 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
17:17:02.064 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 174 ms to scan 207 urls, producing 0 keys and 0 values 
17:17:02.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:17:02.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1891673045
17:17:02.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1552836537
17:17:02.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:17:02.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:17:02.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:17:03.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750324623469_127.0.0.1_58883
17:17:03.809 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Notify connected event to listeners.
17:17:03.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:17:03.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8bdc295-420a-4b82-9145-7c738670dfd0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1070619454
17:17:03.918 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:17:06.613 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:17:06.614 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:17:06.614 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:17:06.875 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:17:07.260 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:18:25.280 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:18:26.293 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d12cffc3-caed-47f9-9741-9fd876cf253d_config-0
17:18:26.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
17:18:26.412 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
17:18:26.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
17:18:26.607 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 180 ms to scan 207 urls, producing 0 keys and 0 values 
17:18:26.618 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:18:26.633 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
17:18:26.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:18:26.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 169 ms to scan 207 urls, producing 0 keys and 0 values 
17:18:26.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:18:26.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1440939580
17:18:26.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1172417096
17:18:26.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:18:26.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:18:26.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:18:28.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750324708354_127.0.0.1_59289
17:18:28.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Notify connected event to listeners.
17:18:28.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:18:28.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d12cffc3-caed-47f9-9741-9fd876cf253d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/132659602
17:18:28.743 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:18:31.514 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:18:31.517 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:18:31.517 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:18:31.851 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:18:35.698 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:18:36.668 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 097dcb09-863e-4b64-bc1d-c24ced3fbaac
17:18:36.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] RpcClient init label, labels = {module=naming, source=sdk}
17:18:36.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:18:36.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:18:36.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:18:36.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:18:36.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750324716688_127.0.0.1_59336
17:18:36.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Notify connected event to listeners.
17:18:36.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:18:36.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/132659602
17:18:38.652 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:18:38.705 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
17:18:38.980 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.441 seconds (JVM running for 15.849)
17:18:39.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
17:18:39.009 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:18:39.009 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
17:18:39.174 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Receive server push request, request = NotifySubscriberRequest, requestId = 124
17:18:39.179 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [097dcb09-863e-4b64-bc1d-c24ced3fbaac] Ack server push request, request = NotifySubscriberRequest, requestId = 124
17:18:39.318 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
