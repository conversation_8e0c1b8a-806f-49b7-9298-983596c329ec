<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucDemandFollowMapper">
    
    <resultMap type="UucDemandFollow" id="UucDemandFollowResult">
        <result property="id"    column="id"    />
        <result property="demandId"    column="demand_id"    />
        <result property="demandTitle"    column="demand_title"    />
        <result property="principal"    column="principal"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucDemandFollowVo">
        select id, demand_id, demand_title, principal, remark, create_by, create_time, update_by, update_time from uuc_demand_follow
    </sql>

    <select id="selectUucDemandFollowList" parameterType="UucDemandFollow" resultMap="UucDemandFollowResult">
        <include refid="selectUucDemandFollowVo"/>
        <where>  
            <if test="demandId != null "> and demand_id like concat('%', #{demandId}, '%')</if>
            <if test="demandTitle != null  and demandTitle != ''"> and demand_title like concat('%', #{demandTitle}, '%')</if>
            <if test="principal != null  and principal != ''"> and principal like concat('%', #{principal}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucDemandFollowById" parameterType="Long" resultMap="UucDemandFollowResult">
        <include refid="selectUucDemandFollowVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucDemandFollow" parameterType="UucDemandFollow">
        insert into uuc_demand_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="demandId != null">demand_id,</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title,</if>
            <if test="principal != null and principal != ''">principal,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="demandId != null">#{demandId},</if>
            <if test="demandTitle != null and demandTitle != ''">#{demandTitle},</if>
            <if test="principal != null and principal != ''">#{principal},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucDemandFollow" parameterType="UucDemandFollow">
        update uuc_demand_follow
        <trim prefix="SET" suffixOverrides=",">
            <if test="demandId != null">demand_id = #{demandId},</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title = #{demandTitle},</if>
            <if test="principal != null and principal != ''">principal = #{principal},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucDemandFollowById" parameterType="Long">
        delete from uuc_demand_follow where id = #{id}
    </delete>

    <delete id="deleteUucDemandFollowByIds" parameterType="String">
        delete from uuc_demand_follow where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>