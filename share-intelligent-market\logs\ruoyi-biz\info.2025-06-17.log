17:43:27.923 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:43:30.077 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c79c643-2791-404e-aa99-8bb25f7611a6_config-0
17:43:30.193 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 65 ms to scan 1 urls, producing 3 keys and 6 values 
17:43:30.251 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
17:43:30.270 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:43:30.545 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 271 ms to scan 248 urls, producing 0 keys and 0 values 
17:43:30.560 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 5 values 
17:43:30.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
17:43:30.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:43:30.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 244 ms to scan 248 urls, producing 0 keys and 0 values 
17:43:30.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:43:30.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/377957453
17:43:30.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/1730129134
17:43:30.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:43:30.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:43:30.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:33.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153413175_127.0.0.1_49346
17:43:33.660 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Notify connected event to listeners.
17:43:33.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:33.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c79c643-2791-404e-aa99-8bb25f7611a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1987418535
17:43:33.922 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:43:41.839 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
17:43:41.840 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:43:41.841 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:43:42.237 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:43:47.176 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:50:16.402 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:50:18.062 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5d289677-8773-4f95-a92b-d350e4f42bca_config-0
17:50:18.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
17:50:18.225 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
17:50:18.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:50:18.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 282 ms to scan 248 urls, producing 0 keys and 0 values 
17:50:18.548 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
17:50:18.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
17:50:18.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:50:18.837 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 249 ms to scan 248 urls, producing 0 keys and 0 values 
17:50:18.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:50:18.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1555413896
17:50:18.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/397857830
17:50:18.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:50:18.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:50:18.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:21.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153820763_127.0.0.1_51516
17:50:21.121 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Notify connected event to listeners.
17:50:21.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:21.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d289677-8773-4f95-a92b-d350e4f42bca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/313334570
17:50:21.292 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:50:25.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
17:50:25.741 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:50:25.741 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:50:26.108 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:50:28.888 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
