{"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}}