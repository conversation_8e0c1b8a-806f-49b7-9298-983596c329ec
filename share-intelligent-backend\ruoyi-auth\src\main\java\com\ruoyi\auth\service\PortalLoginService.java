package com.ruoyi.auth.service;

import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.RemoteMemberService;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.enums.MemberStatus;
import com.ruoyi.portalweb.api.model.LoginMember;
import com.ruoyi.system.api.RemoteSSOService;
import cn.hutool.core.codec.Base64;


/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class PortalLoginService {
    @Autowired
    private RemoteMemberService remoteMemberService;
    @Autowired
    private SysRecordLogService recordLogService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private RemoteSSOService remoteSSOService;
    @Value("${rsa.privateKey}")
    private String privateKeyStr;

    /**
     * 登录
     */
    public LoginMember login(String memberphone, String password,String smsCode) {
        if (StringUtils.isAnyBlank(memberphone,smsCode)) {
            throw new ServiceException("用户/验证码 必须填写");
        }
        // 核查验证码
        R<Boolean> r = remoteMemberService.checkUserSmsCode(memberphone, smsCode);

        if (r == null || r.getCode() != Constants.SUCCESS) {
            throw new ServiceException("验证码无效");
        }
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(memberphone, password)) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }

        LoginMember loginMember = checkExists(memberphone);
        // 验证密码
        if (!SecurityUtils.matchesPassword(password,loginMember.getMember().getMemberPassword())) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "用户名或密码错误");
            throw new ServiceException("对不起，您输入的账号或密码错误, 请重新输入");
        }

        recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS, "登录成功");
        return loginMember;
    }

    /**
     * 账号密码登录（不需要验证码）
     */
    public LoginMember loginByPassword(String memberphone, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(memberphone, password)) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }

        LoginMember loginMember = checkExists(memberphone);
        // 验证密码
        if (!SecurityUtils.matchesPassword(password,loginMember.getMember().getMemberPassword())) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "用户名或密码错误");
            throw new ServiceException("对不起，您输入的账号或密码错误, 请重新输入");
        }

        recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS, "登录成功");
        return loginMember;
    }

    /**
     * 验证码登录
     */
    public LoginMember loginByCode(String username, String smsCode) {
        if (StringUtils.isAnyBlank(username, smsCode)) {
            throw new ServiceException("用户/验证码 必须填写");
        }
//        //解密smsCode
//        RSA rsa = new RSA(privateKeyStr, null);
//        byte[] aByte = Base64.decode(smsCode);
//        byte[] decrypt = rsa.decrypt(aByte, KeyType.PrivateKey);
//        String originCOde = StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);
//        System.out.println("--------------------------校验开始--------------------------");
        // 核查验证码
        R<Boolean> r = remoteMemberService.checkUserSmsCode(username, smsCode);

        if (r == null || r.getCode() != Constants.SUCCESS) {
            throw new ServiceException("验证码无效");
        }

//        // 查询用户信息
//        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

//        System.out.println("--------------------------登录开始--------------------------");
        // 查询用户信息
        R<LoginMember> memberResult = remoteMemberService.getMemberSms(username, SecurityConstants.INNER);

        if (R.FAIL == memberResult.getCode()) {
            throw new ServiceException(memberResult.getMsg());
        }
        LoginMember memberInfo = memberResult.getData();
        return memberInfo;
    }


    public void logout(String loginName) {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册（传统注册方式，主系统应该主要使用验证码登录）
     */
    public void register(String memberphone, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(memberphone, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        Member member = new Member();
        member.setMemberPhone(memberphone);
        member.setMemberPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteMemberService.registerMemberInfo(member, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(memberphone, Constants.REGISTER, "注册成功");
    }

    public LoginMember resetPassword(String memberphone, String password) {
        LoginMember loginMember = checkExists(memberphone);

        // 加密密码
        String encryptedPassword = SecurityUtils.encryptPassword(password);

        // 更新主系统Member密码
        Member member = new Member();
        member.setMemberPassword(encryptedPassword);
        member.setMemberPhone(memberphone);
        R<Boolean> booleanR = remoteMemberService.updateMemberPassword(member, SecurityConstants.INNER);
        if (R.FAIL == booleanR.getCode()) {
            throw new ServiceException(booleanR.getMsg());
        }

        // 同步更新SSO用户密码
        updateSSOUserPassword(memberphone, encryptedPassword);

        recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS, "密码重置成功");
        return loginMember;
    }

    /**
     * 同步更新SSO用户密码
     */
    private void updateSSOUserPassword(String memberphone, String encryptedPassword) {
        try {
            R<Boolean> ssoResult = remoteSSOService.updateSSOUserPassword(
                memberphone,
                encryptedPassword,
                SecurityConstants.INNER
            );

            if (ssoResult != null && R.isSuccess(ssoResult)) {
                recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS,
                    "密码重置成功，SSO用户密码已同步更新");
            } else {
                recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS,
                    "密码重置成功，但SSO用户密码更新失败: " + (ssoResult != null ? ssoResult.getMsg() : "SSO服务不可用"));
            }
        } catch (Exception e) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_SUCCESS,
                "密码重置成功，但SSO用户密码更新异常: " + e.getMessage());
        }
    }

    /**
     * 检查用户
     */
    private LoginMember checkExists(String memberphone) throws ServiceException {

        // 查询用户信息
        R<LoginMember> memberResult = remoteMemberService.getMemberInfo(memberphone, SecurityConstants.INNER);

        if (StringUtils.isNull(memberResult) || StringUtils.isNull(memberResult.getData())) {
            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("登录用户：" + memberphone + " 不存在");
        }

        if (R.FAIL == memberResult.getCode()) {
            throw new ServiceException(memberResult.getMsg());
        }

        LoginMember memberInfo = memberResult.getData();
//        Member member = memberResult.getData().getMember();
//        if (MemberStatus.DISABLED.getCode().equals(member.getMemberStatus())) {
//            recordLogService.recordLogininfor(memberphone, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
//            throw new ServiceException("对不起，您的账号：" + member.getMemberPhone() + " 已停用");
//        }

        return memberInfo;
    }


}
