package com.ruoyi.biz.shop.app.callback;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.biz.shop.biz.order.OrderBiz;
import com.ruoyi.biz.shop.service.order.PayRequestFeignService;
import com.ruoyi.biz.shop.util.pay.wechat.entity.WeiXinOrderReq;
import com.ruoyi.biz.shop.util.pay.wechat.entity.WeiXinReturnPay;
import com.ruoyi.biz.shop.util.pay.wechat.entity.WeiXinSubOrderReq;
import com.ruoyi.biz.shop.util.pay.wechat.util.MessageUtil;
import com.ruoyi.biz.shop.util.pay.wechat.util.WebUtil;
import com.ruoyi.biz.shop.util.pay.wechat.util.WeixinUtil;
import com.ruoyi.shop.entity.PayRequest;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 微信支付通知监听器
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/data/wxpay/callback")
@Slf4j
public class WxPayInformController {
    @Resource
    private OrderBiz orderBiz;

    /**
     * apiV3密钥
     * */
    @Value("${wx.pay.apiV3Key}")
    private String apiV3Key;

    /*@Resource
    private Verifier verifier;*/

    private static Logger logger = LoggerFactory.getLogger(WxPayInformController.class);

    @Resource
    private PayRequestFeignService payRequestFeignService;

    @RequestMapping(value = "/nativeCallBack", method = RequestMethod.POST)
    public JSONObject nativeCallBack(HttpServletRequest request){
        //获取 HTTP 请求体 body。切记不要用 JSON 对象序列化后的字符串，避免验签的 body 和原文不一致。

//        Boolean result = orderBiz.paySuccessNotify(request);

        JSONObject ret = new JSONObject();
        ret.put("code","SUCCESS");
        ret.put("message","成功接收回调");
        return ret;
    }


    /**
     * 微信订单支付通知业务处理
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/inform", method = RequestMethod.POST)
    public void orderinform(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WeiXinReturnPay weiXinReturnPay = new WeiXinReturnPay();
        Map<String, String> resultMap = MessageUtil.parseXml((ServletInputStream) request.getInputStream());
        log.info("wxpay/callback/inform resultMap--->",resultMap);
        String out_trade_no = resultMap.get("out_trade_no");
        String transaction_id = resultMap.get("transaction_id");
        String return_code = resultMap.get("return_code");
        String result_code = resultMap.get("result_code");
        //回调成功
        if (StringUtils.isNotBlank(return_code) && "SUCCESS".equals(return_code) &&
                StringUtils.isNotBlank(result_code) && "SUCCESS".equals(result_code)) {
            String weiXinOrderStr = null;
            //主动查询是否支付成功
            if (resultMap.containsKey("sub_mch_id")) {
                WeiXinSubOrderReq weiXinOrderReq = new WeiXinSubOrderReq();
                weiXinOrderReq.setAppid(resultMap.get("appid"));
                weiXinOrderReq.setMch_id(resultMap.get("mch_id"));
                weiXinOrderReq.setNonce_str(WebUtil.getNonceStr());
                weiXinOrderReq.setTransaction_id(transaction_id);
                weiXinOrderReq.setSub_mch_id(resultMap.get("sub_mch_id"));
                String payparam = "appid=" + weiXinOrderReq.getAppid() + "&mch_id=" + weiXinOrderReq.getMch_id() + "&nonce_str=" + weiXinOrderReq.getNonce_str() + "&sub_mch_id=" + weiXinOrderReq.getSub_mch_id()
                        + "&transaction_id=" + weiXinOrderReq.getTransaction_id() + "&key=";
                String sign = SecureUtil.md5(payparam.trim()).toUpperCase();
                weiXinOrderReq.setSign(sign);
                weiXinOrderStr = MessageUtil.weiXinSubOrderToXml(weiXinOrderReq).replace("__", "_");
            } else {
                WeiXinOrderReq weiXinOrderReq = new WeiXinOrderReq();
                weiXinOrderReq.setAppid(resultMap.get("appid"));
                weiXinOrderReq.setMch_id(resultMap.get("mch_id"));
                weiXinOrderReq.setNonce_str(WebUtil.getNonceStr());
                weiXinOrderReq.setTransaction_id(transaction_id);
                String payparam = "appid=" + weiXinOrderReq.getAppid() + "&mch_id=" + weiXinOrderReq.getMch_id() + "&nonce_str=" + weiXinOrderReq.getNonce_str()
                        + "&transaction_id=" + weiXinOrderReq.getTransaction_id() + "&key=";
                String sign = SecureUtil.md5(payparam.trim()).toUpperCase();
                weiXinOrderReq.setSign(sign);
                weiXinOrderStr = MessageUtil.weiXinOrderToXml(weiXinOrderReq).replace("__", "_");
            }
            Map<String, String> map = WeixinUtil.httpRequest_map(MessageUtil.REQ_ORDER_URL, "POST", weiXinOrderStr);
            log.info("wxpay/callback/inform map--->",map);
            //查询订单成功
            if (StringUtils.isNotBlank(map.get("return_code")) && "SUCCESS".equals(map.get("return_code")) &&
                    StringUtils.isNotBlank(map.get("trade_state")) && "SUCCESS".equals(map.get("trade_state"))) {
                Integer total_fee = Integer.valueOf(map.get("total_fee"));
                Integer retVal = 1;
                //根据订单号查询订单，判断金额是否一致
                if (retVal > 0) {
                    logger.error("微信订单修改成功");
                    weiXinReturnPay.setReturn_code("SUCCESS");
                    weiXinReturnPay.setReturn_msg("OK");
                } else {
                    logger.error("微信订单修改失败");
                    weiXinReturnPay.setReturn_code("FAIL");
                    weiXinReturnPay.setReturn_msg("交易失败");
                }
            } else {
                logger.error("微信回调交易失败");
                weiXinReturnPay.setReturn_code("FAIL");
                weiXinReturnPay.setReturn_msg("交易失败");
            }
        } else {
            logger.error("微信回调交易失败");
            weiXinReturnPay.setReturn_code("FAIL");
            weiXinReturnPay.setReturn_msg("交易失败");
        }
        response.setContentType("text/xml");
        String weiXinReturnPayStr = MessageUtil.weiXinReturnPayToXml(weiXinReturnPay).replace("__", "_");
        response.getWriter().write(weiXinReturnPayStr);
        response.getWriter().flush();
        response.getWriter().close();
    }

    @RequestMapping(value = "/union", method = RequestMethod.POST)
    public void union(@RequestBody NotificationRequest request, HttpServletResponse response) throws Exception {
        WeiXinReturnPay weiXinReturnPay = new WeiXinReturnPay();

        NotificationHandler handler = new NotificationHandler(null, apiV3Key.getBytes(StandardCharsets.UTF_8));
        Notification notification = handler.parse(request);
        if (notification.getEventType().equals("TRANSACTION.SUCCESS")) {
            String decryptData = notification.getDecryptData();
            JSONObject retVal = JSONObject.parseObject(decryptData, JSONObject.class);
            JSONArray jsonArray = retVal.getJSONArray("sub_orders");
            Boolean flag = false;
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject subOrder = jsonArray.getJSONObject(i);
                List<PayRequest> payList = Lists.newArrayList();
                if (subOrder.getString("trade_state").equals("SUCCESS")) {
                    PayRequest req = new PayRequest();
                    req.setPayno(subOrder.getString("transaction_id"));
                    req.setRequest_no(subOrder.getString("out_trade_no"));
                    payList.add(req);
                }
                flag = payRequestFeignService.updatePaynoList(payList).getData();
            }
            if(flag){
                weiXinReturnPay.setReturn_code("SUCCESS");
                weiXinReturnPay.setReturn_msg("OK");
            }
            else{
                weiXinReturnPay.setReturn_code("FAIL");
                weiXinReturnPay.setReturn_msg("交易失败");
            }
        } else {
            weiXinReturnPay.setReturn_code("FAIL");
            weiXinReturnPay.setReturn_msg("交易失败");
        }
        response.setContentType("text/xml");
        String weiXinReturnPayStr = MessageUtil.weiXinReturnPayToXml(weiXinReturnPay).replace("__", "_");
        response.getWriter().write(weiXinReturnPayStr);
        response.getWriter().flush();
        response.getWriter().close();
    }

    @RequestMapping(value = "/refund/union", method = RequestMethod.POST)
    public void refundUnion(@RequestBody NotificationRequest request, HttpServletResponse response) throws Exception {
        WeiXinReturnPay weiXinReturnPay = new WeiXinReturnPay();

        NotificationHandler handler = new NotificationHandler(null, apiV3Key.getBytes(StandardCharsets.UTF_8));
        Notification notification = handler.parse(request);
        if (notification.getEventType().equals("REFUND.SUCCESS")) {
            String decryptData = notification.getDecryptData();
            JSONObject retVal = JSONObject.parseObject(decryptData, JSONObject.class);
            JSONArray jsonArray = retVal.getJSONArray("sub_orders");
            Boolean flag = false;
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject subOrder = jsonArray.getJSONObject(i);
                List<PayRequest> payList = Lists.newArrayList();
                if (subOrder.getString("refund_status").equals("SUCCESS")) {
                    PayRequest req = new PayRequest();
                    req.setPayno(subOrder.getString("transaction_id"));
                    req.setRequest_no(subOrder.getString("out_trade_no"));
                    payList.add(req);
                }
                //flag = payRequestFeignService.updatePaynoList(payList).getData();
            }
            if(flag){
                weiXinReturnPay.setReturn_code("SUCCESS");
                weiXinReturnPay.setReturn_msg("退款成功");
            }
            else{
                weiXinReturnPay.setReturn_code("FAIL");
                weiXinReturnPay.setReturn_msg("退款失败");
            }
        } else {
            weiXinReturnPay.setReturn_code("FAIL");
            weiXinReturnPay.setReturn_msg("退款失败");
        }
        response.setContentType("text/xml");
        String weiXinReturnPayStr = MessageUtil.weiXinReturnPayToXml(weiXinReturnPay).replace("__", "_");
        response.getWriter().write(weiXinReturnPayStr);
        response.getWriter().flush();
        response.getWriter().close();
    }
}
