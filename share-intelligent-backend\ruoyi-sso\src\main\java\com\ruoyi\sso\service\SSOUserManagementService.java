package com.ruoyi.sso.service;

import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.system.api.domain.Member;

/**
 * SSO用户管理服务接口
 * 
 * <AUTHOR>
 */
public interface SSOUserManagementService {

    /**
     * 从主系统Member创建SSO用户
     * 
     * @param member 主系统用户
     * @return SSO用户
     */
    SSOUser createFromMember(Member member);

    /**
     * 从主系统Member更新SSO用户
     * 
     * @param member 主系统用户
     * @return 是否成功
     */
    boolean updateFromMember(Member member);

    /**
     * 检查并创建SSO用户（如果不存在）
     * 
     * @param phone 手机号
     * @return SSO用户
     */
    SSOUser ensureSSOUserExists(String phone);

    /**
     * 同步主系统用户到SSO
     * 
     * @param memberId 主系统用户ID
     * @return 是否成功
     */
    boolean syncMemberToSSO(Long memberId);

    /**
     * 批量同步主系统用户到SSO
     * 
     * @return 同步数量
     */
    int batchSyncMembersToSSO();
}
