19:30:11.557 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
19:30:12.610 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0
19:30:12.698 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 3 keys and 6 values 
19:30:12.736 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 4 keys and 9 values 
19:30:12.747 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:30:12.977 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 226 ms to scan 237 urls, producing 0 keys and 0 values 
19:30:12.990 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
19:30:13.006 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
19:30:13.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
19:30:13.265 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 236 ms to scan 237 urls, producing 0 keys and 0 values 
19:30:13.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:30:13.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1790229151
19:30:13.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/665641137
19:30:13.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:30:13.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:30:13.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:30:15.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750332615068_127.0.0.1_51047
19:30:15.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Notify connected event to listeners.
19:30:15.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:15.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08b87f0f-11d9-4023-87db-c7daa26d81a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/798639105
19:30:15.638 [main] INFO  c.r.u.RuoyiUucApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
19:30:20.748 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9705"]
19:30:20.750 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:30:20.750 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
19:30:21.099 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:30:27.493 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:30:28.177 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7165c656-6a1b-462c-9d4e-69039bd8a6db
19:30:28.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] RpcClient init label, labels = {module=naming, source=sdk}
19:30:28.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:30:28.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:30:28.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:30:28.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:30:28.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750332628197_127.0.0.1_51102
19:30:28.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Notify connected event to listeners.
19:30:28.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:28.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/798639105
19:30:31.670 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9705"]
19:30:31.769 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-uuc ************:9705 register finished
19:30:32.165 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Receive server push request, request = NotifySubscriberRequest, requestId = 244
19:30:32.172 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7165c656-6a1b-462c-9d4e-69039bd8a6db] Ack server push request, request = NotifySubscriberRequest, requestId = 244
19:30:32.236 [main] INFO  c.r.u.RuoyiUucApplication - [logStarted,61] - Started RuoyiUucApplication in 21.668 seconds (JVM running for 23.187)
19:30:32.271 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc.yaml, group=DEFAULT_GROUP
19:30:32.271 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc-dev.yaml, group=DEFAULT_GROUP
19:30:32.272 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc, group=DEFAULT_GROUP
19:30:32.974 [RMI TCP Connection(9)-************] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:30:33.025 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
