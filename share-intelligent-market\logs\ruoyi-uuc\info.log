09:11:04.629 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:06.149 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0
09:11:06.270 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 62 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:06.334 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:06.349 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:06.656 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 304 ms to scan 237 urls, producing 0 keys and 0 values 
09:11:06.672 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:06.692 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:06.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:06.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 249 ms to scan 237 urls, producing 0 keys and 0 values 
09:11:06.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:06.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1825923873
09:11:06.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/883735648
09:11:06.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:06.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:06.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:10.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381869543_127.0.0.1_49564
09:11:10.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Notify connected event to listeners.
09:11:10.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:10.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee05ddbd-f4fd-4864-98c0-8793f5178fe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/313881452
09:11:10.271 [main] INFO  c.r.u.RuoyiUucApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:18.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9705"]
09:11:18.510 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:18.511 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:11:19.219 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:32.612 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:34.202 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 01180490-e02d-428e-ba35-86fd50d783e6
09:11:34.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] RpcClient init label, labels = {module=naming, source=sdk}
09:11:34.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:34.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:34.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:34.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:34.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381894231_127.0.0.1_50001
09:11:34.353 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Notify connected event to listeners.
09:11:34.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:34.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/313881452
09:11:44.929 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9705"]
09:11:45.039 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-uuc ************:9705 register finished
09:11:45.295 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:11:45.300 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01180490-e02d-428e-ba35-86fd50d783e6] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:11:45.800 [main] INFO  c.r.u.RuoyiUucApplication - [logStarted,61] - Started RuoyiUucApplication in 42.935 seconds (JVM running for 44.795)
09:11:45.841 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc.yaml, group=DEFAULT_GROUP
09:11:45.844 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc-dev.yaml, group=DEFAULT_GROUP
09:11:45.844 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc, group=DEFAULT_GROUP
09:11:46.662 [RMI TCP Connection(20)-************] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:11:46.856 [RMI TCP Connection(22)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
