package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteSSOService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * SSO服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteSSOFallbackFactory implements FallbackFactory<RemoteSSOService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteSSOFallbackFactory.class);

    @Override
    public RemoteSSOService create(Throwable throwable) {
        log.error("SSO服务调用失败:{}", throwable.getMessage());
        return new RemoteSSOService() {
            
            @Override
            public R<Boolean> createSSOUser(String memberPhone, String memberRealName, String password, String source) {
                log.error("创建SSO用户失败: {}, 原因: {}", memberPhone, throwable.getMessage());
                return R.fail("创建SSO用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> checkUserExists(String phone, String source) {
                log.error("检查SSO用户存在性失败: {}, 原因: {}", phone, throwable.getMessage());
                return R.fail("检查SSO用户存在性失败:" + throwable.getMessage());
            }

            @Override
            public R<Object> getUserInfo(String phone, String source) {
                log.error("获取SSO用户信息失败: {}, 原因: {}", phone, throwable.getMessage());
                return R.fail("获取SSO用户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updateSSOUserPassword(String phone, String password, String source) {
                log.error("更新SSO用户密码失败: {}, 原因: {}", phone, throwable.getMessage());
                return R.fail("更新SSO用户密码失败:" + throwable.getMessage());
            }
        };
    }
}
