<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucBannerMapper">

    <resultMap type="UucBanner" id="UucBannerResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="category"    column="category"    />
        <result property="image"    column="image"    />
        <result property="url"    column="url"    />
        <result property="remark"    column="remark"    />
        <result property="sorts"    column="sorts"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucBannerVo">
        select id, name, category, image, url, remark, sorts, create_by, create_time, update_by, update_time from uuc_banner
    </sql>

    <select id="selectUucBannerList" parameterType="UucBanner" resultMap="UucBannerResult">
        <include refid="selectUucBannerVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppBannerList" parameterType="UucBanner" resultMap="UucBannerResult">
        <include refid="selectUucBannerVo"/>
        <where>
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
        order by sorts desc
    </select>

    <select id="selectUucBannerById" parameterType="Long" resultMap="UucBannerResult">
        <include refid="selectUucBannerVo"/>
        where id = #{id}
    </select>

    <insert id="insertUucBanner" parameterType="UucBanner" useGeneratedKeys="true" keyProperty="id">
        insert into uuc_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="image != null and image != ''">image,</if>
            <if test="url != null">url,</if>
            <if test="remark != null">remark,</if>
            <if test="sorts != null">sorts,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="image != null and image != ''">#{image},</if>
            <if test="url != null">#{url},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sorts != null">#{sorts},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucBanner" parameterType="UucBanner">
        update uuc_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="image != null and image != ''">image = #{image},</if>
            <if test="url != null">url = #{url},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sorts != null">sorts = #{sorts},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucBannerById" parameterType="Long">
        delete from uuc_banner where id = #{id}
    </delete>

    <delete id="deleteUucBannerByIds" parameterType="String">
        delete from uuc_banner where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>