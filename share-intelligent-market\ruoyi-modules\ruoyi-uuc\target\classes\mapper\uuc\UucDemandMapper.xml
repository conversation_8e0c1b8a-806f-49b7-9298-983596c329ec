<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucDemandMapper">

    <resultMap type="UucDemand" id="UucDemandResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="phone"    column="phone"    />
        <result property="btypeName"    column="btype_name"    />
        <result property="linkman"    column="linkman"    />
        <result property="proCost"    column="pro_cost"    />
        <result property="recommendStatus"    column="recommend_status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucDemandVo">
        select id, title, description, phone, btype_name, linkman, pro_cost, recommend_status, remark, create_by, create_time, update_by, update_time from uuc_demand
    </sql>

    <select id="selectUucDemandList" parameterType="UucDemand" resultMap="UucDemandResult">
        <include refid="selectUucDemandVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="btypeName != null  and btypeName != ''"> and btype_name like concat('%', #{btypeName}, '%')</if>
            <if test="linkman != null  and linkman != ''"> and linkman like concat('%', #{linkman}, '%')</if>
            <if test="recommendStatus != null  and recommendStatus != ''"> and recommend_status = #{recommendStatus}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppDemandList" parameterType="UucDemand" resultMap="UucDemandResult">
        <include refid="selectUucDemandVo"/>
        <where>
            <if test="searchValue != null  and searchValue != ''"> and title like concat('%', #{searchValue}, '%')</if>
            and recommend_status = 1
        </where>
        order by id desc
    </select>

    <select id="selectUucDemandById" parameterType="Long" resultMap="UucDemandResult">
        <include refid="selectUucDemandVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppDemandById" parameterType="Long" resultMap="UucDemandResult">
        <include refid="selectUucDemandVo"/>
        where id = #{id} and recommend_status = 1
    </select>

    <insert id="insertUucDemand" parameterType="UucDemand">
        insert into uuc_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="phone != null">phone,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="linkman != null">linkman,</if>
            <if test="proCost != null">pro_cost,</if>
            <if test="recommendStatus != null">recommend_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="phone != null">#{phone},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="proCost != null">#{proCost},</if>
            <if test="recommendStatus != null">#{recommendStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertUucAppDemand" parameterType="UucDemand">
        insert into uuc_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="phone != null">phone,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="linkman != null">linkman,</if>
            <if test="proCost != null">pro_cost,</if>
            recommend_status,
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="phone != null">#{phone},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="proCost != null">#{proCost},</if>
            0,
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUucDemand" parameterType="UucDemand">
        update uuc_demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="btypeName != null">btype_name = #{btypeName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="proCost != null">pro_cost = #{proCost},</if>
            <if test="recommendStatus != null">recommend_status = #{recommendStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucDemandById" parameterType="Long">
        delete from uuc_demand where id = #{id}
    </delete>

    <delete id="deleteUucDemandByIds" parameterType="String">
        delete from uuc_demand where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>