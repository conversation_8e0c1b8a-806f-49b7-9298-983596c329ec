{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue", "mtime": 1750385853725}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_home", "name", "data", "loading", "pageNum", "pageSize", "total", "keywords", "form", "supplyTypeList", "dict<PERSON><PERSON>l", "supplyType", "techTypeList", "techType", "productTypeList", "productType", "achieveStageList", "achieveStage", "cooperationModeList", "cooperationMode", "supplyList", "title", "url", "appliArea", "requireType", "desc", "publishTime", "fit", "created", "getSupplyDict", "getTechTypeDict", "getStageDict", "getCooperationDict", "getProductTypeDict", "getList", "methods", "_this", "params", "dictType", "listData", "then", "response", "rows", "_this2", "unshift", "dict<PERSON><PERSON>ue", "_this3", "_this4", "_this5", "_this6", "type", "technologyCategory", "process", "cooperationType", "keyword", "supplyData", "res", "code", "onSearch", "handleSizeChange", "handleCurrentChange", "switchSupplyType", "value", "switchTechType", "switchProductTypeType", "switchAchieveStage", "switchCooperationMode", "goDetail", "id", "$router", "push", "initPage", "refresh"], "sources": ["src/views/supplyDemandDocking/components/supplyHall.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">供给大厅</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">Supply Hall</div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"keywords\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">供给分类：</div>\r\n        <div class=\"smallCategory\" :class=\"supplyType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in supplyTypeList\" :key=\"index\" @click=\"switchSupplyType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '1'\">\r\n        <div class=\"largeCategory\">服务类别：</div>\r\n        <div class=\"smallCategory\" :class=\"techType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in techTypeList\" :key=\"index\" @click=\"switchTechType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '2'\">\r\n        <div class=\"largeCategory\">产品类别：</div>\r\n        <div class=\"smallCategory\" :class=\"productType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in productTypeList\" :key=\"index\" @click=\"switchProductTypeType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">成果阶段：</div>\r\n        <div class=\"smallCategory\" :class=\"achieveStage === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in achieveStageList\" :key=\"index\" @click=\"switchAchieveStage(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">合作方式：</div>\r\n        <div class=\"smallCategory\" :class=\"cooperationMode === item.dictValue ? 'smallCategoryActive' : ''\r\n          \" v-for=\"(item, index) in cooperationModeList\" :key=\"index\" @click=\"switchCooperationMode(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div> -->\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initPage\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/serviceSharing/reset.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"refresh\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"card-container\" v-loading=\"loading\">\r\n      <div class=\"content_bottom\" v-if=\"supplyList && supplyList.length > 0\">\r\n        <div class=\"content_bottom_item tr2\" v-for=\"(item, index) in supplyList\" :key=\"index\"\r\n          @click=\"goDetail(item.id)\">\r\n          <div class=\"detailTitle textOverflow1 tr2\">\r\n            {{ item.title }}\r\n          </div>\r\n          <div class=\"demandChunk\">\r\n            <!-- 左侧图片 -->\r\n            <div>\r\n              <img style=\"width: 130px; height: 130px\" :src=\"item.imageUrl\r\n                  ? item.imageUrl\r\n                  : require('../../../assets/demand/xqimgdefault.png')\r\n                \" alt=\"\" />\r\n            </div>\r\n            <!-- 右侧内容 -->\r\n            <div class=\"demand_right\">\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">供给方：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.organization }}\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle\">应用领域：</div>\r\n                <div class=\"detailrightContent\">\r\n                  {{ item.applicationAreaName }}\r\n                </div>\r\n              </div> -->\r\n              <!-- <div class=\"detailrightTitle2 textOverflow2\">\r\n                {{ item.desc }}\r\n              </div> -->\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '1'\">\r\n                <div class=\"detailrightTitle tr2\">服务类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.technologyCategoryName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '2'\">\r\n                <div class=\"detailrightTitle tr2\">产品类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.productName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">发布时间：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.createTime }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image style=\"width: 160px; height: 160px\" :src=\"require('@/assets/user/none.png')\" :fit=\"fit\"></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination v-if=\"supplyList && supplyList.length > 0\" background layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n          @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { supplyData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      supplyTypeList: [\r\n        {\r\n          dictLabel: \"喷漆工\",\r\n        },\r\n        {\r\n          dictLabel: \"安全员\",\r\n        },\r\n        {\r\n          dictLabel: \"采购员\",\r\n        },\r\n        {\r\n          dictLabel: \"巡逻员\",\r\n        },\r\n        {\r\n          dictLabel: \"机械制图员\",\r\n        },\r\n        {\r\n          dictLabel: \"运营专员\",\r\n        },\r\n        {\r\n          dictLabel: \"宣传员\",\r\n        },\r\n        {\r\n          dictLabel: \"项目经理\",\r\n        },\r\n        {\r\n          dictLabel: \"文员\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      supplyType: \"1\",\r\n      techTypeList: [\r\n        {\r\n          dictLabel: \"研究生\",\r\n        },\r\n        {\r\n          dictLabel: \"本科\",\r\n        },\r\n        {\r\n          dictLabel: \"大专\",\r\n        },\r\n        {\r\n          dictLabel: \"高中\",\r\n        },\r\n        {\r\n          dictLabel: \"中专\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      techType: \"\",\r\n      productTypeList: [],\r\n      productType: \"\",\r\n      achieveStageList: [\r\n        {\r\n          dictLabel: \"特级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"高级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"助理工程师\",\r\n        },\r\n      ],\r\n      achieveStage: \"\",\r\n      cooperationModeList: [\r\n        {\r\n          dictLabel: \"在职\",\r\n        },\r\n        {\r\n          dictLabel: \"离职\",\r\n        },\r\n      ],\r\n      cooperationMode: \"\",\r\n      supplyList: [\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getSupplyDict(); // 供给类型\r\n    this.getTechTypeDict(); // 技术类别\r\n    this.getStageDict(); // 成果阶段\r\n    this.getCooperationDict(); // 合作方式\r\n    this.getProductTypeDict(); // 产品类别\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getSupplyDict() {\r\n      let params = { dictType: \"supply_type\" };\r\n      listData(params).then((response) => {\r\n        this.supplyTypeList = response.rows;\r\n        // this.supplyTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getTechTypeDict() {\r\n      let params = { dictType: \"technology_category\" };\r\n      listData(params).then((response) => {\r\n        this.techTypeList = response.rows;\r\n        this.techTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getProductTypeDict() {\r\n      let params = { dictType: \"product_category\" };\r\n      listData(params).then((response) => {\r\n        this.productTypeList = response.rows;\r\n        this.productTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getStageDict() {\r\n      let params = { dictType: \"supply_process\" };\r\n      listData(params).then((response) => {\r\n        this.achieveStageList = response.rows;\r\n        this.achieveStageList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getCooperationDict() {\r\n      let params = { dictType: \"supply_cooperation\" };\r\n      listData(params).then((response) => {\r\n        this.cooperationModeList = response.rows;\r\n        this.cooperationModeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        type: this.supplyType,\r\n        technologyCategory: this.techType,\r\n        productType: this.productType,\r\n        process: this.achieveStage,\r\n        cooperationType: this.cooperationMode,\r\n        keyword: this.keywords,\r\n      };\r\n      supplyData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    onSearch() {\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    switchSupplyType(value) {\r\n      this.pageNum = 1;\r\n      this.supplyType = value;\r\n      this.getList();\r\n    },\r\n    switchTechType(value) {\r\n      this.pageNum = 1;\r\n      this.techType = value;\r\n      this.getList();\r\n    },\r\n    switchProductTypeType(value) {\r\n      this.pageNum = 1;\r\n      this.productType = value;\r\n      this.getList();\r\n    },\r\n    switchAchieveStage(value) {\r\n      this.pageNum = 1;\r\n      this.achieveStage = value;\r\n      this.getList();\r\n    },\r\n    switchCooperationMode(value) {\r\n      this.pageNum = 1;\r\n      this.cooperationMode = value;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/supplyDetail?id=\" + id);\r\n    },\r\n    initPage() {\r\n      this.getList();\r\n    },\r\n    refresh() {\r\n      this.pageNum = 1;\r\n      this.supplyType = '1';\r\n      this.techType = \"\";\r\n      this.achieveStage = \"\";\r\n      this.cooperationMode = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 58px 60px 32px 62px;\r\n\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n\r\n    &:before {\r\n      content: \"\";\r\n      z-index: -1;\r\n      position: absolute;\r\n      top: 100%;\r\n      left: 100%;\r\n      width: 86px;\r\n      height: 86px;\r\n      border-radius: 50%;\r\n      background-color: #21c9b8;\r\n      transform-origin: center;\r\n      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);\r\n      transition: transform 0.3s ease-in;\r\n    }\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .textOverflow2 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n\r\n    div {\r\n      color: #ffffff !important;\r\n    }\r\n\r\n    &::before {\r\n      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAqIA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;MACAC,IAAA;MACAC,cAAA,GACA;QACAC,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,EACA;MACAC,UAAA;MACAC,YAAA,GACA;QACAF,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,EACA;MACAG,QAAA;MACAC,eAAA;MACAC,WAAA;MACAC,gBAAA,GACA;QACAN,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,GACA;QACAA,SAAA;MACA,EACA;MACAO,YAAA;MACAC,mBAAA,GACA;QACAR,SAAA;MACA,GACA;QACAA,SAAA;MACA,EACA;MACAS,eAAA;MACAC,UAAA,GACA;QACAC,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAL,KAAA;QACAC,GAAA,EAAAvB,OAAA;QACAwB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,EACA;MACAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,eAAA;IACA,KAAAC,YAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAN,aAAA,WAAAA,cAAA;MAAA,IAAAO,KAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAA3B,cAAA,GAAAgC,QAAA,CAAAC,IAAA;QACA;MACA;IACA;IACAZ,eAAA,WAAAA,gBAAA;MAAA,IAAAa,MAAA;MACA,IAAAN,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA/B,YAAA,GAAA6B,QAAA,CAAAC,IAAA;QACAC,MAAA,CAAA/B,YAAA,CAAAgC,OAAA;UAAAC,SAAA;UAAAnC,SAAA;QAAA;MACA;IACA;IACAuB,kBAAA,WAAAA,mBAAA;MAAA,IAAAa,MAAA;MACA,IAAAT,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAAhC,eAAA,GAAA2B,QAAA,CAAAC,IAAA;QACAI,MAAA,CAAAhC,eAAA,CAAA8B,OAAA;UAAAC,SAAA;UAAAnC,SAAA;QAAA;MACA;IACA;IACAqB,YAAA,WAAAA,aAAA;MAAA,IAAAgB,MAAA;MACA,IAAAV,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAM,MAAA,CAAA/B,gBAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAK,MAAA,CAAA/B,gBAAA,CAAA4B,OAAA;UAAAC,SAAA;UAAAnC,SAAA;QAAA;MACA;IACA;IACAsB,kBAAA,WAAAA,mBAAA;MAAA,IAAAgB,MAAA;MACA,IAAAX,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAA9B,mBAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAM,MAAA,CAAA9B,mBAAA,CAAA0B,OAAA;UAAAC,SAAA;UAAAnC,SAAA;QAAA;MACA;IACA;IACAwB,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,KAAA9C,OAAA;MACA,IAAAkC,MAAA;QACAjC,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACA6C,IAAA,OAAAvC,UAAA;QACAwC,kBAAA,OAAAtC,QAAA;QACAE,WAAA,OAAAA,WAAA;QACAqC,OAAA,OAAAnC,YAAA;QACAoC,eAAA,OAAAlC,eAAA;QACAmC,OAAA,OAAA/C;MACA;MACA,IAAAgD,gBAAA,EAAAlB,MAAA,EAAAG,IAAA,WAAAgB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAR,MAAA,CAAA7B,UAAA,GAAAoC,GAAA,CAAAd,IAAA;UACAO,MAAA,CAAA3C,KAAA,GAAAkD,GAAA,CAAAlD,KAAA;UACA2C,MAAA,CAAA9C,OAAA;QACA;MACA;IACA;IACAuD,QAAA,WAAAA,SAAA;MACA,KAAAxB,OAAA;IACA;IACAyB,gBAAA,WAAAA,iBAAAtD,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA6B,OAAA;IACA;IACA0B,mBAAA,WAAAA,oBAAAxD,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAA8B,OAAA;IACA;IACA2B,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAA1D,OAAA;MACA,KAAAO,UAAA,GAAAmD,KAAA;MACA,KAAA5B,OAAA;IACA;IACA6B,cAAA,WAAAA,eAAAD,KAAA;MACA,KAAA1D,OAAA;MACA,KAAAS,QAAA,GAAAiD,KAAA;MACA,KAAA5B,OAAA;IACA;IACA8B,qBAAA,WAAAA,sBAAAF,KAAA;MACA,KAAA1D,OAAA;MACA,KAAAW,WAAA,GAAA+C,KAAA;MACA,KAAA5B,OAAA;IACA;IACA+B,kBAAA,WAAAA,mBAAAH,KAAA;MACA,KAAA1D,OAAA;MACA,KAAAa,YAAA,GAAA6C,KAAA;MACA,KAAA5B,OAAA;IACA;IACAgC,qBAAA,WAAAA,sBAAAJ,KAAA;MACA,KAAA1D,OAAA;MACA,KAAAe,eAAA,GAAA2C,KAAA;MACA,KAAA5B,OAAA;IACA;IACAiC,QAAA,WAAAA,SAAAC,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,uBAAAF,EAAA;IACA;IACAG,QAAA,WAAAA,SAAA;MACA,KAAArC,OAAA;IACA;IACAsC,OAAA,WAAAA,QAAA;MACA,KAAApE,OAAA;MACA,KAAAO,UAAA;MACA,KAAAE,QAAA;MACA,KAAAI,YAAA;MACA,KAAAE,eAAA;MACA,KAAAe,OAAA;IACA;EACA;AACA", "ignoreList": []}]}