09:11:37.364 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:11:37.483 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:38.229 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:38.229 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:43.311 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:11:52.250 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:11:53.600 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:11:54.425 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:54.425 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:55.697 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:55.697 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:55.932 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
09:11:56.237 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
09:11:56.277 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 20.615 seconds (JVM running for 22.784)
09:11:56.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:11:56.284 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
09:11:56.285 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
09:30:03.316 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:30:03.316 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:30:03.316 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:30:03.327 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:30:03.327 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:30:03.327 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:31:24.919 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
09:31:24.919 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:42:59.784 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portalloginByPassword
09:42:59.784 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.546 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
09:43:00.547 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.891 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:00.891 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:00.891 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:00.891 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.891 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.893 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:03.773 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:03.774 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:03.776 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:03.776 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:04.077 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:04.077 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:05.990 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
09:43:05.991 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.339 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:06.339 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:06.339 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:06.340 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.340 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.340 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:44:33.593 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
09:44:33.594 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:53:57.014 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
11:53:57.014 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
11:53:57.014 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
11:53:57.015 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:53:57.015 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:53:57.015 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:54:04.274 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
11:54:04.275 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:56:34.117 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
11:56:34.117 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
11:57:10.737 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:57:10.741 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:57:25.134 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:57:25.198 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:57:25.723 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:25.724 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:28.614 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:57:35.454 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:57:36.705 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:57:37.465 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:37.466 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:38.635 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:38.635 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:38.863 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
11:57:39.152 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
11:57:39.196 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 14.81 seconds (JVM running for 16.512)
11:57:39.203 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:57:39.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
11:57:39.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
13:38:10.883 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
13:38:10.891 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:38:32.184 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
13:38:32.184 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:38:35.522 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:38:35.523 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:38:35.692 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:38:35.692 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:38:35.692 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:38:35.693 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:38:35.693 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:38:35.693 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:41:47.532 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:41:47.532 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:41:49.517 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:41:49.517 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:41:49.517 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:41:49.518 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:41:49.825 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:41:49.825 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:00.689 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
13:42:00.690 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:41.265 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
13:42:41.266 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:41.530 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:42:41.531 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:41.615 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:42:41.616 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:41.691 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:42:41.691 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:42:41.693 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:41.693 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:47.216 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:42:47.217 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:48.920 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:42:48.920 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:48.935 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:42:48.936 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:49.228 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:42:49.228 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:57.318 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portalloginByPassword
13:42:57.318 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:57.677 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:42:57.678 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:57.710 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:42:57.710 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:42:57.711 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:57.711 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:42:57.711 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:42:57.711 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:02.883 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:43:02.883 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:43:02.883 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:43:02.883 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:02.883 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:02.883 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:05.129 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:43:05.129 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:06.116 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:43:06.116 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:43:06.116 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:43:06.116 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:06.116 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:06.116 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:14.149 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
13:43:14.149 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:34.202 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
13:43:34.202 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:34.468 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:43:34.469 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:34.546 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:43:34.546 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:34.612 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:43:34.612 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:43:34.612 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:43:34.612 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:04.006 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:55:04.006 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:05.912 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:55:05.912 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:55:05.912 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:55:05.912 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:05.912 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:05.912 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:33.501 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
13:55:33.503 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:58.847 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
13:55:58.848 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:59.112 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:55:59.112 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:59.194 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:55:59.194 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:59.270 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:55:59.270 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:55:59.271 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:55:59.271 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:06.745 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:56:06.745 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:08.423 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:56:08.423 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:56:08.424 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:08.424 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:08.730 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:56:08.730 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:19.548 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portalloginByPassword
13:56:19.548 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:19.907 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:56:19.908 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:19.933 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:56:19.933 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:56:19.933 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:56:19.935 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:19.935 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:19.935 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:31.059 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:56:31.059 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:56:31.059 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:56:31.060 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:31.060 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:31.060 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:41.008 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:56:41.008 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:56:41.008 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:56:41.009 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:41.009 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:41.009 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:52.357 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portalloginByPassword
13:56:52.357 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:52.717 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:56:52.717 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:52.739 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:56:52.739 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:56:52.739 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:52.739 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:56:52.739 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:56:52.740 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:07.744 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:57:07.744 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:57:07.744 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:57:07.745 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:07.745 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:07.745 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:09.499 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/supply/list
13:57:09.499 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
13:57:09.500 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:09.500 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:09.805 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
13:57:09.805 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
13:57:09.805 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:09.805 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:22.834 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
13:57:22.834 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:24.181 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
13:57:24.181 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
13:57:24.181 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
13:57:24.181 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:24.181 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
13:57:24.181 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
14:18:57.673 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
14:18:57.674 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
14:18:57.675 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
14:18:57.675 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
14:18:57.675 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
14:18:57.675 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
14:26:05.529 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:26:05.625 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:06.457 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:06.458 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:11.498 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:26:21.447 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:26:22.916 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:26:23.920 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:23.920 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:27:16.124 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:27:16.214 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:16.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:27:16.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:27:27.890 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:27:34.603 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:27:35.888 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:27:36.776 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:27:36.777 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:27:45.334 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:27:45.334 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:12.519 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:28:12.614 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:28:13.222 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:28:13.223 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:17.183 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:28:25.012 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:28:26.292 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:28:27.183 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:28:27.183 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:28.529 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:28:28.529 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:28.889 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
14:28:29.254 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
14:28:29.314 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 17.695 seconds (JVM running for 19.171)
14:28:29.322 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:28:29.322 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
14:28:29.324 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
