09:11:54.028 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:11:54.119 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:54.802 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:54.803 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:12:06.023 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:12:13.171 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:12:14.621 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:12:15.600 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:12:15.601 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:12:18.459 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:12:18.459 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:12:18.932 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
09:12:19.273 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
09:12:19.318 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 26.07 seconds (JVM running for 28.44)
09:12:19.325 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:12:19.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
09:12:19.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
09:18:25.761 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:18:25.873 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:26.785 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:18:26.786 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:18:39.023 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:18:52.723 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:18:55.085 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:18:56.531 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:18:56.531 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:22:59.683 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:22:59.748 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:00.205 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:23:00.205 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:23:02.616 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:23:07.862 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:23:08.799 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:23:09.527 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:23:09.527 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:23:10.564 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:23:10.565 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:23:10.779 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
09:23:11.038 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
09:23:11.073 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 12.034 seconds (JVM running for 13.756)
09:23:11.079 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:23:11.080 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
09:23:11.080 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
09:54:16.793 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
09:54:16.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
09:54:25.107 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:54:25.183 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:54:25.819 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:54:25.819 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:54:28.835 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:54:35.452 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:54:36.750 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:54:37.717 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:54:37.717 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:54:39.069 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:54:39.069 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:54:39.306 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
09:54:39.637 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
09:54:39.681 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.444 seconds (JVM running for 17.326)
09:54:39.687 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:54:39.689 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
09:54:39.690 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
10:15:01.069 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:15:01.072 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:15:10.767 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:15:10.826 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:15:11.263 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:15:11.263 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:15:13.760 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:15:19.241 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:15:20.183 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:15:20.866 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:15:20.867 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:15:22.075 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:15:22.075 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:15:22.291 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:15:22.549 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:15:22.586 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 12.469 seconds (JVM running for 14.151)
10:15:22.592 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:15:22.593 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:15:22.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
10:45:21.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:45:21.134 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:45:30.484 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:45:30.555 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:45:31.036 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:45:31.036 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:45:33.637 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:45:39.343 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:45:40.342 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:45:41.022 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:45:41.022 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:45:42.077 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:45:42.077 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:45:42.288 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:45:42.561 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:45:42.601 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 12.82 seconds (JVM running for 14.499)
10:45:42.608 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:45:42.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:45:42.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
11:20:30.779 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:20:30.781 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:20:40.431 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:20:40.502 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:20:41.152 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:20:41.152 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:20:44.030 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:20:49.876 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:20:50.931 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:20:51.661 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:20:51.661 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:20:52.852 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:20:52.852 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:20:53.068 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
11:20:53.355 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
11:20:53.398 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.747 seconds (JVM running for 15.557)
11:20:53.403 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:20:53.404 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
11:20:53.405 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
11:27:37.886 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:27:37.889 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:52:57.656 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:52:57.759 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:52:58.510 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:52:58.511 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:53:02.948 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:53:10.509 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:53:11.867 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:53:12.891 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:53:12.891 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:53:14.155 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:53:14.155 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:53:14.382 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
11:53:14.700 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
11:53:14.738 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 18.147 seconds (JVM running for 20.183)
11:53:14.745 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:53:14.746 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
11:53:14.746 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
13:17:22.315 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
13:17:22.318 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
13:17:34.351 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:17:34.406 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:17:34.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:17:34.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:17:37.570 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:17:44.017 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:17:45.107 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:17:45.870 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:17:45.870 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:17:47.049 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:17:47.049 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:17:47.272 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
13:17:47.552 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
13:17:47.586 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.966 seconds (JVM running for 15.651)
13:17:47.593 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
13:17:47.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
13:17:47.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
13:33:54.190 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
13:33:54.193 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
13:34:04.105 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:34:04.202 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:34:04.877 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:34:04.877 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:34:07.911 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:34:14.794 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:34:16.255 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:34:17.117 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:34:17.118 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:34:18.418 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:34:18.418 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:34:18.643 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
13:34:18.929 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
13:34:18.974 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.807 seconds (JVM running for 17.584)
13:34:18.981 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
13:34:18.982 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
13:34:18.982 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
13:38:46.045 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
13:38:46.049 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
13:38:53.860 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:38:53.927 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:38:54.475 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:38:54.476 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:38:57.875 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:39:07.895 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:39:09.746 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:39:10.824 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:39:10.824 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:39:12.253 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:39:12.254 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:39:12.486 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
13:39:12.830 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
13:39:12.878 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 19.745 seconds (JVM running for 21.558)
13:39:12.886 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
13:39:12.886 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
13:39:12.887 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
14:01:40.885 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:01:40.888 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:01:49.729 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:01:49.805 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:01:50.343 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:01:50.343 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:01:53.368 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:01:59.525 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:02:00.731 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:02:01.910 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:02:01.910 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:02:03.671 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:02:03.671 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:02:03.945 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
14:02:04.737 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
14:02:04.847 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.806 seconds (JVM running for 17.499)
14:02:04.861 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:02:04.863 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
14:02:04.865 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
14:12:30.983 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
14:12:30.983 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
14:12:30.983 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
14:12:30.993 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
14:12:30.993 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
14:12:30.993 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
14:26:05.393 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:26:05.395 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:26:14.896 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:26:14.978 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:15.756 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:15.756 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:18.760 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:26:25.564 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:26:26.825 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:26:27.562 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:27.563 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:28.739 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:28.740 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:28.955 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
14:26:29.211 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
14:26:29.249 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.263 seconds (JVM running for 17.09)
14:26:29.255 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:26:29.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
14:26:29.257 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
16:02:56.194 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:02:56.194 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:02:56.194 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:02:56.206 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:02:56.206 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:02:56.206 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:03:01.818 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
16:03:01.819 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:03:05.026 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
16:03:05.027 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:19.724 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:04:19.727 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:04:26.552 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:04:26.617 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:04:27.021 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:04:27.022 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:04:29.344 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:04:34.497 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:04:35.460 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:04:36.166 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:04:36.166 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:04:37.237 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:04:37.237 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:04:37.470 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
16:04:37.734 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
16:04:37.770 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 11.855 seconds (JVM running for 13.325)
16:04:37.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
16:04:37.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
16:04:37.778 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
16:04:56.205 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:04:56.208 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:05:01.739 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:05:01.798 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:05:02.193 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:05:02.193 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:05:04.459 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:05:09.324 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:05:10.247 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:05:10.937 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:05:10.937 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:05:11.976 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:05:11.976 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:05:12.182 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
16:05:12.439 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
16:05:12.480 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 11.347 seconds (JVM running for 12.834)
16:05:12.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
16:05:12.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
16:05:12.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
16:06:55.631 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
16:06:55.631 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
16:06:55.638 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:06:55.638 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:06:57.467 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
16:06:57.468 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:06:59.361 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
16:06:59.361 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:07:15.605 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
16:07:15.605 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:07:29.585 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
16:07:29.587 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:08.318 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
16:08:08.319 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:10.746 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:08:10.747 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:11.140 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:08:11.140 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:08:11.140 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:11.140 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:08:11.140 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:11.141 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:15.098 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:08:15.098 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:08:15.098 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/supply/list
16:08:15.099 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:15.099 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:15.099 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:15.611 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:08:15.612 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:17.230 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:08:17.231 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:17.539 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:17.539 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/solutionType/listDesk
16:08:17.539 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:17.539 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:17.539 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:17.539 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:17.539 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/region/tree
16:08:17.541 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:20.420 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMyCompany
16:08:20.421 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:24.008 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:24.008 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:24.008 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:08:24.010 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:24.010 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:24.010 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:24.770 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:08:24.771 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:25.081 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:08:25.081 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/supply/list
16:08:25.081 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:08:25.081 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:25.081 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:25.081 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:26.560 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/workInfo/user/list
16:08:26.560 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:26.560 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:26.560 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:27.080 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:27.082 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:27.384 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:08:27.384 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:28.932 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/solutionType/listDesk
16:08:28.932 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:08:28.932 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:28.932 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:08:28.932 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:28.932 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:28.932 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:28.932 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:08:30.135 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMyCompany
16:08:30.136 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:03.114 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/manufactureOrder/list
16:09:03.114 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:03.670 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:09:03.670 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:03.985 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:09:03.985 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:09:03.986 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:03.986 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:18.964 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:09:18.964 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:09:18.964 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/supply/list
16:09:18.964 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:09:18.966 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:18.966 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:18.966 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:18.966 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:52.633 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/manufactureOrder/list
16:09:52.633 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:53.405 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/jobInfo/user/list
16:09:53.407 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:09:54.998 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/manufactureOrder/list
16:09:54.998 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:28.492 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/jobInfo/user/list
16:21:28.492 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:29.145 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/manufactureOrder/list
16:21:29.145 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:30.471 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:21:30.471 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:21:30.472 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:30.472 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:30.826 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMyCompany
16:21:30.828 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:33.628 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:21:33.628 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/solutionType/listDesk
16:21:33.628 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:21:33.628 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:21:33.628 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:33.628 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:33.628 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:21:33.628 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:31:08.970 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/manufactureOrder/list
16:31:08.971 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:43:40.255 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
16:43:40.255 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:43:41.450 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:43:41.450 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:43:41.450 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:43:41.450 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:43:41.450 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:43:41.450 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:15.790 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
16:44:15.791 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:34.606 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
16:44:34.606 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:34.944 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:44:34.945 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:35.031 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:44:35.031 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:44:35.031 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:44:35.031 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:35.031 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:35.031 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:38.679 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:44:38.679 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/supply/list
16:44:38.679 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/list
16:44:38.679 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:38.679 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:38.679 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:38.975 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/IntentionApply/list
16:44:38.975 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:40.953 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
16:44:40.954 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:41.825 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:44:41.825 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:44:41.826 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:44:41.826 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:41.826 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:44:41.826 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:46:38.675 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
16:46:38.676 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:00.038 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
16:47:00.038 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:00.435 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
16:47:00.435 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:00.500 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:47:00.501 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:00.505 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:47:00.505 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:47:00.505 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:00.505 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:05.486 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
16:47:05.486 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:06.343 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
16:47:06.343 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:06.651 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
16:47:06.651 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
16:47:06.651 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:06.651 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
16:47:14.389 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
16:47:14.390 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:06:43.349 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
17:06:43.350 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
17:06:56.908 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:06:56.985 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:06:57.436 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:06:57.436 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:07:00.078 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:07:05.876 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:07:06.949 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:07:07.696 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:07:07.696 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:07:08.943 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:07:08.943 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:07:09.160 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
17:07:09.435 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
17:07:09.476 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.272 seconds (JVM running for 14.777)
17:07:09.482 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
17:07:09.483 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
17:07:09.484 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
17:28:54.244 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:28:54.252 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:29:06.203 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:29:06.203 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:29:27.223 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:29:27.224 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:31:42.546 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:31:42.546 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:35:19.231 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:35:19.232 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:35:54.797 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:35:54.797 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:36:22.433 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:36:22.434 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:37:36.170 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:37:36.170 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:40:07.323 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:40:07.324 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:40:50.155 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
17:40:50.155 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:08.781 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
17:44:08.781 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:19.448 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
17:44:19.448 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:20.253 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
17:44:20.254 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:20.736 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
17:44:20.736 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
17:44:20.736 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:20.736 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:44:20.781 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
17:44:20.782 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:04.885 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
17:45:04.885 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:07.127 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
17:45:07.127 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
17:45:07.127 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
17:45:07.128 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:07.128 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:07.128 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:12.343 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/single/util/get_qwt_code
17:45:12.343 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:24.828 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
17:45:24.830 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:45:27.184 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/loginBySmsCode
17:45:27.185 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:59:13.758 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogin
17:59:13.759 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:59:14.024 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
17:59:14.025 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:59:14.087 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
17:59:14.087 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:59:14.231 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
17:59:14.231 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
17:59:14.231 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
17:59:14.231 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:09.185 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:00:09.185 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:09.191 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:00:09.193 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:09.196 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:00:09.197 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:20.705 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
18:00:20.706 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:25.968 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:00:25.968 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:00:25.968 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:00:25.968 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:25.968 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:25.968 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:29.616 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:00:29.616 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:00:29.616 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:29.616 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:29.617 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:00:29.617 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:00:31.958 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/logout
18:00:31.959 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:04:12.290 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:04:12.290 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:04:12.290 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:04:12.291 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:04:12.291 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:04:12.291 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:25.189 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:05:25.189 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:25.196 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:05:25.196 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:05:25.196 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:25.196 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:38.647 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
18:05:38.648 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:56.667 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:05:56.667 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:05:56.667 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:05:56.669 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:56.669 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:05:56.669 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:06:17.670 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogin
18:06:17.670 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:06:17.918 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
18:06:17.918 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:06:17.998 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:06:17.999 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:06:18.030 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:06:18.030 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:06:18.030 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:06:18.031 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:18.260 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:07:18.260 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:07:18.260 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:07:18.260 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:18.260 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:18.260 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:34.066 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogin
18:07:34.066 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:34.333 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
18:07:34.333 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:34.397 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:07:34.397 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:34.430 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:07:34.430 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:07:34.431 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:07:34.431 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:08:38.682 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogin
18:08:38.682 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:08:39.007 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
18:08:39.007 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:08:39.037 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
18:08:39.037 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
18:08:39.037 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
18:08:39.039 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:08:39.039 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:08:39.039 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**]
18:10:15.987 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:10:15.990 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:10:21.966 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:10:22.065 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:10:22.734 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:10:22.734 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:10:25.457 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:10:33.392 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
18:10:34.693 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
18:10:36.069 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:10:36.069 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:10:37.359 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:10:37.359 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:10:37.590 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
18:10:37.950 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
18:10:37.999 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 16.741 seconds (JVM running for 18.38)
18:10:38.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
18:10:38.009 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
18:10:38.010 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
