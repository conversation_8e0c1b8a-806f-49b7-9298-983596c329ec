09:11:37.364 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:11:37.483 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:38.229 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:38.229 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:43.311 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:11:52.250 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:11:53.600 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:11:54.425 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:54.425 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:55.697 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:55.697 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:55.932 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
09:11:56.237 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
09:11:56.277 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 20.615 seconds (JVM running for 22.784)
09:11:56.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:11:56.284 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
09:11:56.285 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
09:30:03.316 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:30:03.316 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:30:03.316 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:30:03.327 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:30:03.327 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:30:03.327 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:31:24.919 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
09:31:24.919 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:42:59.784 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portalloginByPassword
09:42:59.784 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.546 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/Member/getMemberInfo
09:43:00.547 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.891 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:00.891 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:00.891 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:00.891 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.891 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:00.893 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:03.773 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:03.774 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:03.776 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:03.776 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:04.077 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:04.077 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:05.990 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
09:43:05.991 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.339 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
09:43:06.339 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
09:43:06.339 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
09:43:06.340 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.340 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:43:06.340 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
09:44:33.593 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
09:44:33.594 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
