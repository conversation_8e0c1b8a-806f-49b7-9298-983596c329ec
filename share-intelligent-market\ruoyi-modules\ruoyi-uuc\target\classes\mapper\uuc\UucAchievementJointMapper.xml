<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucAchievementJointMapper">
    
    <resultMap type="UucAchievementJoint" id="UucAchievementJointResult">
        <result property="id"    column="id"    />
        <result property="achievementId"    column="achievement_id"    />
        <result property="achievementName"    column="achievement_name"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkTel"    column="link_tel"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucAchievementJointVo">
        select id, achievement_id, achievement_name, link_man, link_tel, remark, create_by, create_time, update_by, update_time from uuc_achievement_joint
    </sql>

    <select id="selectUucAchievementJointList" parameterType="UucAchievementJoint" resultMap="UucAchievementJointResult">
        <include refid="selectUucAchievementJointVo"/>
        <where>  
            <if test="achievementId != null  and achievementId != ''"> and achievement_id like concat('%', #{achievementId}, '%')</if>
            <if test="achievementName != null  and achievementName != ''"> and achievement_name like concat('%', #{achievementName}, '%')</if>
            <if test="linkMan != null  and linkMan != ''"> and link_man like concat('%', #{linkMan}, '%')</if>
            <if test="linkTel != null  and linkTel != ''"> and link_tel like concat('%', #{linkTel}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucAchievementJointById" parameterType="Long" resultMap="UucAchievementJointResult">
        <include refid="selectUucAchievementJointVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucAchievementJoint" parameterType="UucAchievementJoint">
        insert into uuc_achievement_joint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="achievementId != null and achievementId != ''">achievement_id,</if>
            <if test="achievementName != null and achievementName != ''">achievement_name,</if>
            <if test="linkMan != null and linkMan != ''">link_man,</if>
            <if test="linkTel != null and linkTel != ''">link_tel,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="achievementId != null and achievementId != ''">#{achievementId},</if>
            <if test="achievementName != null and achievementName != ''">#{achievementName},</if>
            <if test="linkMan != null and linkMan != ''">#{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">#{linkTel},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucAchievementJoint" parameterType="UucAchievementJoint">
        update uuc_achievement_joint
        <trim prefix="SET" suffixOverrides=",">
            <if test="achievementId != null and achievementId != ''">achievement_id = #{achievementId},</if>
            <if test="achievementName != null and achievementName != ''">achievement_name = #{achievementName},</if>
            <if test="linkMan != null and linkMan != ''">link_man = #{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">link_tel = #{linkTel},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucAchievementJointById" parameterType="Long">
        delete from uuc_achievement_joint where id = #{id}
    </delete>

    <delete id="deleteUucAchievementJointByIds" parameterType="String">
        delete from uuc_achievement_joint where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>