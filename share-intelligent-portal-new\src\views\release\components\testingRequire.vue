<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item label="检测内容" prop="testingContent">
        <el-input
          v-model="form.testingContent"
          maxlength="50"
          show-word-limit
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="检测要求" prop="testingRequirements">
        <el-input
          v-model="form.testingRequirements"
          type="textarea"
          resize="none"
          :rows="8"
          maxlength="500"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="其他要求" prop="basicRequirements">
        <el-input
          v-model="form.basicRequirements"
          type="textarea"
          resize="none"
          :rows="8"
          maxlength="500"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="场景图片" prop="imageUrl">
        <ImageUpload v-model="form.imageUrl" :limit="1" />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          disabled
          v-model="form.companyName"
          placeholder="请先绑定公司"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.enclosure" />
      </el-form-item> -->
      <el-form-item label="联系人" prop="contactPerson">
        <el-input
          disabled
          v-model="form.contactPerson"
          placeholder="请先维护联系人"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input
          disabled
          v-model="form.contactPhone"
          placeholder="请先维护联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button type="primary" @click="onSubmit">发布</el-button>
        <el-button style="margin-left: 140px" @click.once="onCancel"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { releaseDetection } from "@/api/release";

export default {
  data() {
    return {
      form: {
        testingContent: "",
        testingRequirements: "",
        basicRequirements: "",
        imageUrl: "",
        companyName: "",
        contactPerson: "",
        contactPhone: "",
      },
      // 表单校验
      rules: {
        testingContent: [
          { required: true, message: "检测内容不能为空", trigger: "blur" },
        ],
        testingRequirements: [
          { required: true, message: "检测要求不能为空", trigger: "blur" },
        ],
        basicRequirements: [
          { required: true, message: "其他要求不能为空", trigger: "blur" },
        ],
        // 添加场景图片必填校验规则
        imageUrl: [
          { required: true, message: "场景图片不能为空", trigger: "change" },
        ],
        // contactPerson: [
        //   { required: true, message: "请先维护联系人", trigger: "blur" },
        // ],
        // contactPhone: [
        //   { required: true, message: "请先维护联系方式", trigger: "blur" },
        // ],
      },
    };
  },
  created() {
    let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
    if (userinfo && userinfo != "null") {
      this.form.companyName = userinfo.memberCompanyName;
      this.form.contact = userinfo.memberRealName;
      this.form.phone = userinfo.memberPhone;
    }
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          releaseDetection(this.form).then((res) => {
            if (res.code == 200) {
              this.$message.success("发布成功");
              this.onCancel();
            } else {
              this.$message.error("发布失败");
            }
          });
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>
