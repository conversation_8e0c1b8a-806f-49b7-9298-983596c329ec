{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=template&id=2720d356&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}