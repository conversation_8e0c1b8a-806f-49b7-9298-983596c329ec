09:18:03.022 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:18:03.114 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:03.709 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:18:03.709 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:18:18.186 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:18:25.840 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
09:18:25.849 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:25.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
09:18:26.534 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:28.882 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
09:18:28.887 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
09:18:28.888 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:38.231 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
09:18:43.909 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
09:18:43.900 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-19T01:18:43.900Z
09:18:44.208 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-19T01:18:44.208Z
09:18:47.073 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:51.758 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
09:18:57.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
09:18:57.245 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:18:57.245 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:18:57.626 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
09:19:01.071 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 58.977 seconds (JVM running for 60.749)
09:19:01.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
09:19:01.158 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
09:19:01.159 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
09:19:02.148 [RMI TCP Connection(23)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:20:58.603 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
09:20:58.710 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
09:21:03.921 [lettuce-nioEventLoop-4-4] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 127.0.0.1:6379
09:22:39.899 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
09:22:39.902 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
09:22:40.082 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
09:22:40.089 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
09:22:40.104 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
09:22:40.105 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
09:22:40.105 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
09:22:47.546 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:22:47.619 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:48.007 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:22:48.009 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:07:44.524 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:07:44.618 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:07:45.093 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:07:45.094 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:07:47.946 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:07:51.494 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
16:07:51.498 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:07:51.499 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:07:51.845 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:07:53.223 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
16:07:53.226 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
16:07:53.227 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:07:57.335 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
16:07:58.761 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-19T08:07:58.761Z
16:07:58.772 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
16:07:59.045 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-19T08:07:59.044Z
16:07:59.635 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:08:00.911 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
16:08:03.059 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
16:08:03.186 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:08:03.186 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:08:03.413 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
16:08:05.180 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 21.325 seconds (JVM running for 22.812)
16:08:05.244 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
16:08:05.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
16:08:05.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
16:08:05.897 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:07:58.764 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-19T09:07:58.764Z
17:07:59.001 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-19T09:07:59.001Z
18:07:40.690 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:07:40.693 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:07:41.014 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
18:07:41.036 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
18:07:41.064 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
18:07:41.065 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
18:07:41.065 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
18:07:45.330 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:07:45.394 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:07:45.839 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:07:45.840 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:07:48.399 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:07:51.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
18:07:51.859 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:07:51.859 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:07:52.170 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:07:53.643 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
18:07:53.648 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
18:07:53.648 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:07:58.292 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
18:08:00.012 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-19T10:08:00.012Z
18:08:00.028 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
18:08:00.243 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-19T10:08:00.243Z
18:08:01.005 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:08:02.976 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
18:08:05.305 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
18:08:05.405 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:08:05.406 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:08:05.638 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
18:08:07.598 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 22.942 seconds (JVM running for 24.575)
18:08:07.658 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
18:08:07.659 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
18:08:07.660 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
18:08:07.998 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:11:03.965 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:11:03.968 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:11:04.260 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
18:11:04.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
18:11:04.288 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
18:11:04.288 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
18:11:04.288 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
18:11:07.798 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:11:07.860 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:11:08.250 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:11:08.250 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:11:10.707 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:11:14.114 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
18:11:14.118 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:11:14.118 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:11:14.455 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:11:15.919 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
18:11:15.924 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
18:11:15.924 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:11:20.918 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
18:11:22.524 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
18:11:22.514 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-19T10:11:22.514Z
18:11:22.762 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-19T10:11:22.762Z
18:11:23.424 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:11:24.959 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
18:11:27.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
18:11:27.629 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:11:27.629 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:11:27.887 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
18:11:30.032 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 22.859 seconds (JVM running for 24.456)
18:11:30.103 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
18:11:30.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
18:11:30.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
18:11:30.640 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
