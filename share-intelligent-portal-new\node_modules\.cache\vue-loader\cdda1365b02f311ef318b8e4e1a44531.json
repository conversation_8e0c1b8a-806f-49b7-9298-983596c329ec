{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue?vue&type=style&index=0&id=8cb17206&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue", "mtime": 1750385853725}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY29udGVudCB7DQogIHdpZHRoOiAxMDAlOw0KICBwYWRkaW5nLWJvdHRvbTogNjBweDsNCn0NCg0KLmNvbnRlbnRfYmFubmVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMzAwcHg7DQogIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vLi4vYXNzZXRzL3JlbGVhc2UvYmFubmVyLnBuZyIpOw0KICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW46IDAgYXV0bzsNCiAgcGFkZGluZy10b3A6IDcxcHg7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGZvbnQtc2l6ZTogNDBweDsNCiAgY29sb3I6ICMwMDA7DQp9DQoNCi5hY3Rpdml0eS10aXRsZS1jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQoNCiAgLy8gYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgLmFjdGl2aXR5LXRpdGxlLWJveCB7DQogICAgd2lkdGg6IDMzNnB4Ow0KICAgIG1hcmdpbjogMCBhdXRvOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgcGFkZGluZzogNjBweCAwIDQwcHg7DQoNCiAgICAuYWN0aXZpdHktdGl0bGUgew0KICAgICAgZm9udC1zaXplOiA0MHB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICBjb2xvcjogIzMzMzsNCiAgICAgIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICAgICAgcGFkZGluZzogMCA0MHB4Ow0KICAgIH0NCg0KICAgIC5hY3Rpdml0eS1kaXZpZGVyIHsNCiAgICAgIHdpZHRoOiA0OHB4Ow0KICAgICAgaGVpZ2h0OiA0cHg7DQogICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgIH0NCiAgfQ0KDQogIC5hY3Rpdml0eS1zZWFyY2gtYm94IHsNCiAgICBtYXJnaW4tdG9wOiA0MHB4Ow0KDQogICAgLmFjdGl2aXR5LXNlYXJjaC1mb3JtIHsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgICAgLmFjdGl2aXR5LXNlYXJjaC1pbnB1dCB7DQogICAgICAgIHdpZHRoOiA3OTJweDsNCiAgICAgICAgaGVpZ2h0OiA1NHB4Ow0KDQogICAgICAgIC5hY3Rpdml0eS1zZWFyY2gtYnRuIHsNCiAgICAgICAgICB3aWR0aDogMTAwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmNhcmRfdG9wIHsNCiAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICBtYXJnaW4tdG9wOiAzMHB4Ow0KICBwYWRkaW5nOiA1OHB4IDYwcHggMzJweCA2MnB4Ow0KDQogIC5jYXJkX3RvcF9pdGVtIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCg0KICAgIC5sYXJnZUNhdGVnb3J5IHsNCiAgICAgIHdpZHRoOiA5MHB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBjb2xvcjogIzIyMjIyMjsNCiAgICAgIG1hcmdpbi1yaWdodDogMjhweDsNCiAgICB9DQoNCiAgICAuc21hbGxDYXRlZ29yeSB7DQogICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgcGFkZGluZzogMTJweCAyNHB4Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCg0KICAgIC5zbWFsbENhdGVnb3J5QWN0aXZlIHsNCiAgICAgIGJhY2tncm91bmQ6ICNlMGY3ZjU7DQogICAgICBib3JkZXItcmFkaXVzOiAycHg7DQogICAgICBjb2xvcjogIzIxYzliODsNCiAgICB9DQogIH0NCg0KICAuY2FyZF90b3BfaXRlbTpudGgtY2hpbGQoMSkgew0KICAgIG1hcmdpbi10b3A6IDA7DQogIH0NCg0KICAuY2FyZF90b3BfaXRlbUxpbmUgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMXB4Ow0KICAgIGJhY2tncm91bmQ6ICNlZWVlZWU7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgfQ0KDQogIC5idXR0b25TdHlsZSB7DQogICAgbWFyZ2luLXRvcDogOXB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KDQogICAgLmltZ1N0eWxlIHsNCiAgICAgIHdpZHRoOiAxOXB4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCg0KICAgIC5idXR0b25UZXh0IHsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB9DQogIH0NCn0NCg0KLmNvbnRlbnRfYm90dG9tIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZmxleC13cmFwOiB3cmFwOw0KDQogIC5jb250ZW50X2JvdHRvbV9pdGVtIHsNCiAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KICAgIHdpZHRoOiA1OTBweDsNCiAgICBoZWlnaHQ6IDIwOHB4Ow0KICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgYm94LXNoYWRvdzogMHB4IDRweCAxOHB4IDJweCAjZThmMWZhOw0KICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7DQogICAgcGFkZGluZzogMjBweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIHotaW5kZXg6IDE7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgICY6YmVmb3JlIHsNCiAgICAgIGNvbnRlbnQ6ICIiOw0KICAgICAgei1pbmRleDogLTE7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDEwMCU7DQogICAgICBsZWZ0OiAxMDAlOw0KICAgICAgd2lkdGg6IDg2cHg7DQogICAgICBoZWlnaHQ6IDg2cHg7DQogICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFjOWI4Ow0KICAgICAgdHJhbnNmb3JtLW9yaWdpbjogY2VudGVyOw0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUzZCgtNTAlLCAtNTAlLCAwKSBzY2FsZTNkKDAsIDAsIDApOw0KICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZS1pbjsNCiAgICB9DQoNCiAgICAuZGV0YWlsVGl0bGUgew0KICAgICAgaGVpZ2h0OiAzMHB4Ow0KICAgICAgY29sb3I6IHJnYmEoNTEsIDUxLCA1MSwgMSk7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIH0NCg0KICAgIC50ZXh0T3ZlcmZsb3cxIHsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAxOw0KICAgICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsNCiAgICB9DQoNCiAgICAudGV4dE92ZXJmbG93MiB7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCiAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsNCiAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgfQ0KDQogICAgLmRlbWFuZENodW5rIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQoNCiAgICAgIC5kZW1hbmRfcmlnaHQgew0KICAgICAgICB3aWR0aDogNDEzcHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIH0NCg0KICAgICAgLmRlbWFuZFRvcFJpZ2h0ZmxleCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgfQ0KDQogICAgICAuZGV0YWlscmlnaHRUaXRsZSB7DQogICAgICAgIGNvbG9yOiByZ2JhKDE1MywgMTUzLCAxNTMsIDEpOw0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICB9DQoNCiAgICAgIC5kZXRhaWxyaWdodFRpdGxlMiB7DQogICAgICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuODUpOw0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICB9DQoNCiAgICAgIC5kZXRhaWxyaWdodENvbnRlbnQgew0KICAgICAgICB3aWR0aDogMzQzcHg7DQogICAgICAgIGNvbG9yOiByZ2JhKDUxLCA1MSwgNTEsIDEpOw0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmNvbnRlbnRfYm90dG9tX2l0ZW06aG92ZXIgew0KICAgIGJveC1zaGFkb3c6IDBweCAzcHggMjBweCAwcHggcmdiYSgxMzIsIDIxMiwgMTc4LCAwLjYpOw0KICAgIHNjYWxlOiAxLjAxOw0KDQogICAgZGl2IHsNCiAgICAgIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQogICAgfQ0KDQogICAgJjo6YmVmb3JlIHsNCiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlM2QoLTUwJSwgLTUwJSwgMCkgc2NhbGUzZCgxNSwgMTUsIDE1KTsNCiAgICB9DQogIH0NCg0KICAuY29udGVudF9ib3R0b21faXRlbTpudGgtY2hpbGQoMm4pIHsNCiAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgfQ0KfQ0KDQoucGFnZVN0eWxlIHsNCiAgbWFyZ2luLXRvcDogNjBweDsNCiAgd2lkdGg6IDEwMCU7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg=="}, {"version": 3, "sources": ["supplyHall.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8aA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "supplyHall.vue", "sourceRoot": "src/views/supplyDemandDocking/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">供给大厅</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">Supply Hall</div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"keywords\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">供给分类：</div>\r\n        <div class=\"smallCategory\" :class=\"supplyType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in supplyTypeList\" :key=\"index\" @click=\"switchSupplyType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '1'\">\r\n        <div class=\"largeCategory\">服务类别：</div>\r\n        <div class=\"smallCategory\" :class=\"techType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in techTypeList\" :key=\"index\" @click=\"switchTechType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '2'\">\r\n        <div class=\"largeCategory\">产品类别：</div>\r\n        <div class=\"smallCategory\" :class=\"productType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in productTypeList\" :key=\"index\" @click=\"switchProductTypeType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">成果阶段：</div>\r\n        <div class=\"smallCategory\" :class=\"achieveStage === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in achieveStageList\" :key=\"index\" @click=\"switchAchieveStage(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">合作方式：</div>\r\n        <div class=\"smallCategory\" :class=\"cooperationMode === item.dictValue ? 'smallCategoryActive' : ''\r\n          \" v-for=\"(item, index) in cooperationModeList\" :key=\"index\" @click=\"switchCooperationMode(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div> -->\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initPage\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/serviceSharing/reset.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"refresh\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"card-container\" v-loading=\"loading\">\r\n      <div class=\"content_bottom\" v-if=\"supplyList && supplyList.length > 0\">\r\n        <div class=\"content_bottom_item tr2\" v-for=\"(item, index) in supplyList\" :key=\"index\"\r\n          @click=\"goDetail(item.id)\">\r\n          <div class=\"detailTitle textOverflow1 tr2\">\r\n            {{ item.title }}\r\n          </div>\r\n          <div class=\"demandChunk\">\r\n            <!-- 左侧图片 -->\r\n            <div>\r\n              <img style=\"width: 130px; height: 130px\" :src=\"item.imageUrl\r\n                  ? item.imageUrl\r\n                  : require('../../../assets/demand/xqimgdefault.png')\r\n                \" alt=\"\" />\r\n            </div>\r\n            <!-- 右侧内容 -->\r\n            <div class=\"demand_right\">\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">供给方：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.organization }}\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle\">应用领域：</div>\r\n                <div class=\"detailrightContent\">\r\n                  {{ item.applicationAreaName }}\r\n                </div>\r\n              </div> -->\r\n              <!-- <div class=\"detailrightTitle2 textOverflow2\">\r\n                {{ item.desc }}\r\n              </div> -->\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '1'\">\r\n                <div class=\"detailrightTitle tr2\">服务类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.technologyCategoryName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '2'\">\r\n                <div class=\"detailrightTitle tr2\">产品类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.productName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">发布时间：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.createTime }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image style=\"width: 160px; height: 160px\" :src=\"require('@/assets/user/none.png')\" :fit=\"fit\"></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination v-if=\"supplyList && supplyList.length > 0\" background layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n          @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { supplyData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      supplyTypeList: [\r\n        {\r\n          dictLabel: \"喷漆工\",\r\n        },\r\n        {\r\n          dictLabel: \"安全员\",\r\n        },\r\n        {\r\n          dictLabel: \"采购员\",\r\n        },\r\n        {\r\n          dictLabel: \"巡逻员\",\r\n        },\r\n        {\r\n          dictLabel: \"机械制图员\",\r\n        },\r\n        {\r\n          dictLabel: \"运营专员\",\r\n        },\r\n        {\r\n          dictLabel: \"宣传员\",\r\n        },\r\n        {\r\n          dictLabel: \"项目经理\",\r\n        },\r\n        {\r\n          dictLabel: \"文员\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      supplyType: \"1\",\r\n      techTypeList: [\r\n        {\r\n          dictLabel: \"研究生\",\r\n        },\r\n        {\r\n          dictLabel: \"本科\",\r\n        },\r\n        {\r\n          dictLabel: \"大专\",\r\n        },\r\n        {\r\n          dictLabel: \"高中\",\r\n        },\r\n        {\r\n          dictLabel: \"中专\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      techType: \"\",\r\n      productTypeList: [],\r\n      productType: \"\",\r\n      achieveStageList: [\r\n        {\r\n          dictLabel: \"特级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"高级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"助理工程师\",\r\n        },\r\n      ],\r\n      achieveStage: \"\",\r\n      cooperationModeList: [\r\n        {\r\n          dictLabel: \"在职\",\r\n        },\r\n        {\r\n          dictLabel: \"离职\",\r\n        },\r\n      ],\r\n      cooperationMode: \"\",\r\n      supplyList: [\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getSupplyDict(); // 供给类型\r\n    this.getTechTypeDict(); // 技术类别\r\n    this.getStageDict(); // 成果阶段\r\n    this.getCooperationDict(); // 合作方式\r\n    this.getProductTypeDict(); // 产品类别\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getSupplyDict() {\r\n      let params = { dictType: \"supply_type\" };\r\n      listData(params).then((response) => {\r\n        this.supplyTypeList = response.rows;\r\n        // this.supplyTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getTechTypeDict() {\r\n      let params = { dictType: \"technology_category\" };\r\n      listData(params).then((response) => {\r\n        this.techTypeList = response.rows;\r\n        this.techTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getProductTypeDict() {\r\n      let params = { dictType: \"product_category\" };\r\n      listData(params).then((response) => {\r\n        this.productTypeList = response.rows;\r\n        this.productTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getStageDict() {\r\n      let params = { dictType: \"supply_process\" };\r\n      listData(params).then((response) => {\r\n        this.achieveStageList = response.rows;\r\n        this.achieveStageList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getCooperationDict() {\r\n      let params = { dictType: \"supply_cooperation\" };\r\n      listData(params).then((response) => {\r\n        this.cooperationModeList = response.rows;\r\n        this.cooperationModeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        type: this.supplyType,\r\n        technologyCategory: this.techType,\r\n        productType: this.productType,\r\n        process: this.achieveStage,\r\n        cooperationType: this.cooperationMode,\r\n        keyword: this.keywords,\r\n      };\r\n      supplyData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    onSearch() {\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    switchSupplyType(value) {\r\n      this.pageNum = 1;\r\n      this.supplyType = value;\r\n      this.getList();\r\n    },\r\n    switchTechType(value) {\r\n      this.pageNum = 1;\r\n      this.techType = value;\r\n      this.getList();\r\n    },\r\n    switchProductTypeType(value) {\r\n      this.pageNum = 1;\r\n      this.productType = value;\r\n      this.getList();\r\n    },\r\n    switchAchieveStage(value) {\r\n      this.pageNum = 1;\r\n      this.achieveStage = value;\r\n      this.getList();\r\n    },\r\n    switchCooperationMode(value) {\r\n      this.pageNum = 1;\r\n      this.cooperationMode = value;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/supplyDetail?id=\" + id);\r\n    },\r\n    initPage() {\r\n      this.getList();\r\n    },\r\n    refresh() {\r\n      this.pageNum = 1;\r\n      this.supplyType = '1';\r\n      this.techType = \"\";\r\n      this.achieveStage = \"\";\r\n      this.cooperationMode = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 58px 60px 32px 62px;\r\n\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n\r\n    &:before {\r\n      content: \"\";\r\n      z-index: -1;\r\n      position: absolute;\r\n      top: 100%;\r\n      left: 100%;\r\n      width: 86px;\r\n      height: 86px;\r\n      border-radius: 50%;\r\n      background-color: #21c9b8;\r\n      transform-origin: center;\r\n      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);\r\n      transition: transform 0.3s ease-in;\r\n    }\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .textOverflow2 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n\r\n    div {\r\n      color: #ffffff !important;\r\n    }\r\n\r\n    &::before {\r\n      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}