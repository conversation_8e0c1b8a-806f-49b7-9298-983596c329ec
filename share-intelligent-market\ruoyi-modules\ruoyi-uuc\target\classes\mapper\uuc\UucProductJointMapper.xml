<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucProductJointMapper">
    
    <resultMap type="UucProductJoint" id="UucProductJointResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productNo"    column="product_no"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkTel"    column="link_tel"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucProductJointVo">
        select id, product_id, product_name, product_no, link_man, link_tel, remark, create_by, create_time, update_by, update_time from uuc_product_joint
    </sql>

    <select id="selectUucProductJointList" parameterType="UucProductJoint" resultMap="UucProductJointResult">
        <include refid="selectUucProductJointVo"/>
        <where>  
            <if test="productId != null  and productId != ''"> and product_id like concat('%', #{productId}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="linkMan != null  and linkMan != ''"> and link_man like concat('%', #{linkMan}, '%')</if>
            <if test="linkTel != null  and linkTel != ''"> and link_tel like concat('%', #{linkTel}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucProductJointById" parameterType="Long" resultMap="UucProductJointResult">
        <include refid="selectUucProductJointVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucProductJoint" parameterType="UucProductJoint">
        insert into uuc_product_joint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productId != null and productId != ''">product_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productNo != null and productNo != ''">product_no,</if>
            <if test="linkMan != null and linkMan != ''">link_man,</if>
            <if test="linkTel != null and linkTel != ''">link_tel,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productId != null and productId != ''">#{productId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productNo != null and productNo != ''">#{productNo},</if>
            <if test="linkMan != null and linkMan != ''">#{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">#{linkTel},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucProductJoint" parameterType="UucProductJoint">
        update uuc_product_joint
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null and productId != ''">product_id = #{productId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productNo != null and productNo != ''">product_no = #{productNo},</if>
            <if test="linkMan != null and linkMan != ''">link_man = #{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">link_tel = #{linkTel},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucProductJointById" parameterType="Long">
        delete from uuc_product_joint where id = #{id}
    </delete>

    <delete id="deleteUucProductJointByIds" parameterType="String">
        delete from uuc_product_joint where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>