{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue?vue&type=style&index=0&id=0091a0c8&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\testingRequire.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5mb290ZXItc3VibWl0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbi10b3A6IDYwcHg7DQoNCiAgLmVsLWJ1dHRvbiB7DQogICAgd2lkdGg6IDE0MHB4Ow0KICAgIGhlaWdodDogNTBweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["testingRequire.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "testingRequire.vue", "sourceRoot": "src/views/release/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"检测内容\" prop=\"testingContent\">\r\n        <el-input\r\n          v-model=\"form.testingContent\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"检测要求\" prop=\"testingRequirements\">\r\n        <el-input\r\n          v-model=\"form.testingRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"其他要求\" prop=\"basicRequirements\">\r\n        <el-input\r\n          v-model=\"form.basicRequirements\"\r\n          type=\"textarea\"\r\n          resize=\"none\"\r\n          :rows=\"8\"\r\n          maxlength=\"500\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"场景图片\" prop=\"imageUrl\">\r\n        <ImageUpload v-model=\"form.imageUrl\" :limit=\"1\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPerson\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactPhone\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n          >取消</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseDetection } from \"@/api/release\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        testingContent: \"\",\r\n        testingRequirements: \"\",\r\n        basicRequirements: \"\",\r\n        imageUrl: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        testingContent: [\r\n          { required: true, message: \"检测内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        testingRequirements: [\r\n          { required: true, message: \"检测要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        basicRequirements: [\r\n          { required: true, message: \"其他要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n        // 添加场景图片必填校验规则\r\n        imageUrl: [\r\n          { required: true, message: \"场景图片不能为空\", trigger: \"change\" },\r\n        ],\r\n        // contactPerson: [\r\n        //   { required: true, message: \"请先维护联系人\", trigger: \"blur\" },\r\n        // ],\r\n        // contactPhone: [\r\n        //   { required: true, message: \"请先维护联系方式\", trigger: \"blur\" },\r\n        // ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo && userinfo != \"null\") {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contact = userinfo.memberRealName;\r\n      this.form.phone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          releaseDetection(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel();\r\n            } else {\r\n              this.$message.error(\"发布失败\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}