import request from '@/utils/request'

// 登录页面--验证码登录
export const loginCode = (params) => {
  return request({
    url: '/data/login/code',
    method: 'post',
    params:params
  })
}



// 获取短信验证码
export const getLoginCode = (params) => {
  return request({
    url: '/data/util/code',
    method: 'get',
    params:params
  })
}


// 密码登录
export const login = (params) => {
  return request({
    url: '/data/login/pass',
    method: 'post',
    params:params
  })
}

// 获取权限
export const getPermi = (params) => {
  return request({
    url: '/purchase/user/permission',
    method: 'get',
    params:params
  })
}

// 获取图片验证码
export const getCodeImg = (params) => {
  return request({
    url: '/data/login/randomImage/'+params,
    method: 'get'
  })
}

// SSO登录 - 获取SSO登录地址
export const getSSOLoginUrl = (redirect) => {
  return request({
    url: '/auth/sso/loginUrl',
    method: 'get',
    params: { redirect }
  })
}

// SSO登录 - 处理SSO回调
export const handleSSOCallback = (code, state) => {
  return request({
    url: '/auth/sso/callback',
    method: 'get',
    params: { code, state }
  })
}
