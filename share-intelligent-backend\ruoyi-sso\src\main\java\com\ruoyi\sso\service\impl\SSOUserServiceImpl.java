package com.ruoyi.sso.service.impl;

import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.sso.mapper.SSOUserMapper;
import com.ruoyi.sso.service.SSOUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * SSO用户服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOUserServiceImpl implements SSOUserService {

    private static final Logger log = LoggerFactory.getLogger(SSOUserServiceImpl.class);

    @Autowired
    private SSOUserMapper ssoUserMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public SSOUser selectSSOUserByUsername(String username) {
        try {
            return ssoUserMapper.selectByUsername(username);
        } catch (Exception e) {
            log.error("根据用户名查询SSO用户异常: {}", username, e);
            return null;
        }
    }

    @Override
    public SSOUser selectSSOUserByPhone(String phone) {
        try {
            return ssoUserMapper.selectByPhone(phone);
        } catch (Exception e) {
            log.error("根据手机号查询SSO用户异常: {}", phone, e);
            return null;
        }
    }

    @Override
    public SSOUser selectSSOUserById(Long id) {
        try {
            return ssoUserMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID查询SSO用户异常: {}", id, e);
            return null;
        }
    }

    @Override
    public int insertSSOUser(SSOUser ssoUser) {
        try {
            // 验证必要字段
            if (ssoUser == null || ssoUser.getUsername() == null || ssoUser.getPassword() == null) {
                log.warn("SSO用户信息不完整，无法插入");
                return 0;
            }

            // 检查用户名是否已存在
            SSOUser existingUser = ssoUserMapper.selectByUsername(ssoUser.getUsername());
            if (existingUser != null) {
                log.warn("SSO用户名已存在: {}", ssoUser.getUsername());
                return 0;
            }

            // 插入用户
            int result = ssoUserMapper.insert(ssoUser);
            if (result > 0) {
                log.info("SSO用户创建成功: {}", ssoUser.getUsername());
            } else {
                log.warn("SSO用户创建失败: {}", ssoUser.getUsername());
            }
            return result;
        } catch (Exception e) {
            log.error("插入SSO用户异常: {}", ssoUser != null ? ssoUser.getUsername() : "null", e);
            return 0;
        }
    }

    @Override
    public int updateSSOUser(SSOUser ssoUser) {
        try {
            if (ssoUser == null || ssoUser.getId() == null) {
                log.warn("SSO用户信息不完整，无法更新");
                return 0;
            }

            int result = ssoUserMapper.update(ssoUser);
            if (result > 0) {
                log.info("SSO用户更新成功: {}", ssoUser.getUsername());
            } else {
                log.warn("SSO用户更新失败: {}", ssoUser.getUsername());
            }
            return result;
        } catch (Exception e) {
            log.error("更新SSO用户异常: {}", ssoUser != null ? ssoUser.getUsername() : "null", e);
            return 0;
        }
    }

    @Override
    public int updateLastLoginTime(Long userId) {
        try {
            if (userId == null) {
                log.warn("用户ID为空，无法更新最后登录时间");
                return 0;
            }

            int result = ssoUserMapper.updateLastLoginTime(userId);
            if (result > 0) {
                log.debug("更新用户最后登录时间成功: {}", userId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新用户最后登录时间异常: {}", userId, e);
            return 0;
        }
    }

    @Override
    public boolean validatePassword(String username, String password) {
        try {
            if (username == null || password == null) {
                return false;
            }

            SSOUser ssoUser = selectSSOUserByUsername(username);
            if (ssoUser == null) {
                // 尝试用手机号查询
                ssoUser = selectSSOUserByPhone(username);
            }

            if (ssoUser == null) {
                log.warn("SSO用户不存在: {}", username);
                return false;
            }

            // 检查用户状态
            if (!ssoUser.isActive()) {
                log.warn("SSO用户已被禁用: {}", username);
                return false;
            }

            // 验证密码
            boolean matches = passwordEncoder.matches(password, ssoUser.getPassword());
            if (matches) {
                log.info("SSO用户密码验证成功: {}", username);
                // 更新最后登录时间
                updateLastLoginTime(ssoUser.getId());
            } else {
                log.warn("SSO用户密码验证失败: {}", username);
            }

            return matches;
        } catch (Exception e) {
            log.error("验证SSO用户密码异常: {}", username, e);
            return false;
        }
    }
}
