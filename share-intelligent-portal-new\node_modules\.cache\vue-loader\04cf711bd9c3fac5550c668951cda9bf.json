{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue?vue&type=style&index=0&id=12f4cb28&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\compositeExhibitionHall\\index.vue", "mtime": 1750385853718}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/compositeExhibitionHall", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"left\">\r\n      <div class=\"hall-bg\">\r\n        <img class=\"hall-img\" :src=\"currentItem.url\" alt=\"\" />\r\n        <div class=\"card\">\r\n          <div class=\"card-top\">\r\n            <div class=\"card-left\">\r\n              <img class=\"card-img\" :src=\"currentItem.imageUrl\" alt=\"\" />\r\n              <el-button type=\"primary\" :icon=\"videoIcon\" @click=\"handlePlayAudio\">播放音频</el-button>\r\n            </div>\r\n            <div class=\"card-right\">\r\n              <div class=\"card-title\">{{ currentItem.materialName }}</div>\r\n              <div class=\"card-content\">{{ currentItem.description }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-bottom\" v-if=\"materialList.length > 0\">\r\n            <div class=\"card-item\" v-for=\"(item, index) in materialList\" @click=\"toFactory(item)\" :key=\"index\">\r\n              <div class=\"card-item-title\">{{ item.productName }}</div>\r\n              <img :src=\"item.productImageUrl\" alt=\"\">\r\n            </div>\r\n          </div>\r\n          <div class=\"card-empty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"hall-list\">\r\n        <div class=\"hall-item\" v-for=\"(item, index) in deviceMenuList\" @click=\"handleClick(item)\" :key=\"index\">\r\n          <img :src=\"item.url\" alt=\"\" />\r\n          <div class=\"hall-title\">{{ item.materialName }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getCompositeExhibitionHallList } from '@/api/compositeExhibitionHall'\r\nimport { listSysProduct } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceSharing\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      bgUrl: [\r\n        require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n      ],\r\n      deviceMenuList: [\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall2.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall3.png\"),\r\n        }, {\r\n          url: require(\"@/assets/compositeExhibitionHall/hall4.png\"),\r\n        },\r\n      ],\r\n      currentItem: {\r\n        url: require(\"@/assets/compositeExhibitionHall/hall1.png\"),\r\n        imageUrl: require(\"@/assets/compositeExhibitionHall/hall_img.png\"),\r\n        materialName: \"化工环保展厅\",\r\n        description: '暂无介绍',\r\n      },\r\n      videoIcon: 'el-icon-video-play',\r\n      isPlay: false,\r\n      materialList: [\r\n        {\r\n          productName: '玻璃钢槽式电缆桥架',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material1.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢格栅',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material2.png\"),\r\n        },\r\n        {\r\n          productName: '钢筋钢板',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material3.png\"),\r\n        },\r\n        {\r\n          productName: '玻璃钢管道',\r\n          productImageUrl: require(\"@/assets/compositeExhibitionHall/material4.png\"),\r\n        }\r\n      ],\r\n      productId: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.productId = this.$route.query.productId ? this.$route.query.productId : '';\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      getCompositeExhibitionHallList(params).then((response) => {\r\n        if (response.code === 200 && response.rows.length > 0) {\r\n          this.deviceMenuList = response.rows;\r\n          this.deviceMenuList.sort((a, b) => a.sortOrder - b.sortOrder);\r\n          // 按顺序循环bgUrl\r\n          this.deviceMenuList.forEach((item, index) => {\r\n            item.url = this.bgUrl[index % this.bgUrl.length];\r\n          })\r\n          if (this.productId) {\r\n            getCompositeExhibitionHallList({\r\n              pageNum: 1,\r\n              pageSize: 1000,\r\n              productId: this.productId,\r\n            }).then((res) => {\r\n              if (res.code === 200 && res.rows.length > 0) {\r\n                let index = this.deviceMenuList.findIndex(item => item.id === res.rows[0].id);\r\n                this.handleClick(this.deviceMenuList[index]);\r\n              }\r\n            })\r\n          } else {\r\n            this.handleClick(this.deviceMenuList[0]);\r\n          }\r\n          // this.handleClick(this.deviceMenuList[0]);\r\n        }\r\n      });\r\n    },\r\n    handleClick(item) {\r\n      console.log(item, 'item')\r\n      this.currentItem.url = item.url;\r\n      this.currentItem.materialName = item.materialName;\r\n      this.currentItem.imageUrl = item.imageUrl ? item.imageUrl : require(\"@/assets/compositeExhibitionHall/hall_img.png\");\r\n      this.currentItem.description = item.description ? item.description : '暂无介绍';\r\n      // if (item.imageList) {\r\n      //   this.materialList = item.imageList.split(',').map(item => {\r\n      //     let name = item.split('/')[item.split('/').length - 1].split('_');\r\n      //     name.pop();\r\n      //     return {\r\n      //       name: name.join('_'),\r\n      //       img: item\r\n      //     }\r\n      //   })\r\n      // } else {\r\n      //   this.materialList = []\r\n      // }\r\n      listSysProduct({\r\n        exhibitionHallId: item.id,\r\n        pageNum: 1,\r\n        pageSize: 1000,\r\n      }).then((res) => {\r\n        if (res.code === 200) {\r\n          this.materialList = res.rows || [];\r\n        }\r\n      });\r\n    },\r\n    handlePlayAudio() {\r\n      if (this.isPlay) {\r\n        this.videoIcon = 'el-icon-video-play'\r\n        this.isPlay = false\r\n      } else {\r\n        this.videoIcon = 'el-icon-video-pause'\r\n        this.isPlay = true\r\n      }\r\n    },\r\n    toFactory(item) {\r\n      this.$router.push({\r\n        path: '/manufacturingSharing',\r\n        query: {\r\n          index: 2,\r\n          page: 2,\r\n          productId: item.productId,\r\n        }\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  height: auto;\r\n  padding: 40px;\r\n  box-sizing: border-box;\r\n  background-color: #F7FBFF;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .left {\r\n    width: 80%;\r\n    position: relative;\r\n\r\n    .hall-img {\r\n      width: 100%;\r\n    }\r\n\r\n    .card {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      background-color: #F7FBFF;\r\n      border-radius: 10px;\r\n      padding: 40px;\r\n      box-sizing: border-box;\r\n      width: 84%;\r\n      height: 74%;\r\n\r\n      .card-top {\r\n        height: 52%;\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        .card-left {\r\n          width: 29%;\r\n\r\n          .card-img {\r\n            width: 100%;\r\n            margin-bottom: 1vw;\r\n          }\r\n        }\r\n\r\n        .card-right {\r\n          width: 65%;\r\n\r\n          .card-title {\r\n            font-weight: 500;\r\n            font-size: 1.1vw;\r\n            color: #222222;\r\n            margin-bottom: 0.8vw;\r\n          }\r\n\r\n          .card-content {\r\n            font-weight: 400;\r\n            font-size: 0.7vw;\r\n            line-height: 1vw;\r\n            color: #787878;\r\n            overflow-y: auto;\r\n            box-sizing: border-box;\r\n            height: 90%;\r\n            overflow: auto;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-bottom {\r\n        height: 48%;\r\n        margin-top: 2vw;\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        overflow-x: auto;\r\n\r\n        .card-item {\r\n          min-width: 22%;\r\n          margin: 0.5vw;\r\n          margin-right: 1.3vw;\r\n\r\n          .card-item-title {\r\n            width: 100%;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            font-weight: 400;\r\n            font-size: 0.9vw;\r\n            color: #222222;\r\n            margin-bottom: 14px;\r\n          }\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 75%;\r\n          }\r\n        }\r\n        \r\n      }\r\n\r\n\r\n\r\n      .card-empty {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 18%;\r\n    height: 46vw;\r\n    overflow-y: auto;\r\n    background-color: transparent;\r\n\r\n    .hall-list {\r\n      width: 100%;\r\n\r\n      .hall-item {\r\n        width: 100%;\r\n        position: relative;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 100%;\r\n          margin-bottom: 1.5vw;\r\n        }\r\n\r\n        .hall-title {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          font-weight: 500;\r\n          font-size: 1.1vw;\r\n          color: #000000;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}