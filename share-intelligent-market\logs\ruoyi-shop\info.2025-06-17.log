17:44:20.478 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:44:21.588 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 523ad288-f724-45bb-9f1d-c01d8727f862_config-0
17:44:21.664 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 39 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:21.714 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:21.733 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:21.973 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 234 ms to scan 294 urls, producing 0 keys and 0 values 
17:44:21.984 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:21.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:22.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:22.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 173 ms to scan 294 urls, producing 0 keys and 0 values 
17:44:22.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:22.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/991875403
17:44:22.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1762378078
17:44:22.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:22.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:22.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:44:23.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153463665_127.0.0.1_50172
17:44:23.945 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Notify connected event to listeners.
17:44:23.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:23.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [523ad288-f724-45bb-9f1d-c01d8727f862_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/1861616277
17:44:24.055 [main] INFO  c.r.s.RuoYiShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:44:26.606 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
17:44:26.617 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
17:44:26.716 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:44:26.734 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
17:44:26.736 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:44:26.890 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
17:44:27.112 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:44:27.112 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
17:44:27.132 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
17:44:27.133 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
17:44:27.143 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:44:27.145 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
17:44:27.145 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
17:44:28.687 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9707"]
17:44:28.687 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:28.687 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:44:28.973 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:29.747 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
17:44:29.949 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:50:14.529 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:50:16.261 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0
17:50:16.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 3 keys and 6 values 
17:50:16.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
17:50:16.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
17:50:16.804 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 308 ms to scan 294 urls, producing 0 keys and 0 values 
17:50:16.824 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 
17:50:16.853 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
17:50:16.880 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 2 keys and 8 values 
17:50:17.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 272 ms to scan 294 urls, producing 0 keys and 0 values 
17:50:17.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:50:17.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/2023349777
17:50:17.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/688722159
17:50:17.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:50:17.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:50:17.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:19.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153819389_127.0.0.1_51449
17:50:19.667 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Notify connected event to listeners.
17:50:19.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:19.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22d6bd76-b9ba-4591-8fa8-9ffc1fecc685_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/1625258377
17:50:19.832 [main] INFO  c.r.s.RuoYiShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:50:23.647 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
17:50:23.662 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
17:50:23.805 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:50:23.828 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
17:50:23.831 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:50:24.005 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
17:50:24.272 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:50:24.272 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
17:50:24.301 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
17:50:24.302 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
17:50:24.320 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:50:24.321 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
17:50:24.321 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
17:50:26.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9707"]
17:50:26.385 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:50:26.386 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:50:26.756 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:50:27.938 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
17:50:28.738 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
17:50:37.586 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:50:38.475 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1986f470-82af-4c36-ba62-f2ee325ec1db
17:50:38.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] RpcClient init label, labels = {module=naming, source=sdk}
17:50:38.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:50:38.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:50:38.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:50:38.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:38.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153838492_127.0.0.1_51691
17:50:38.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Notify connected event to listeners.
17:50:38.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:38.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/1625258377
17:50:43.130 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9707"]
17:50:43.218 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-shop 192.168.0.68:9707 register finished
17:50:43.637 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Receive server push request, request = NotifySubscriberRequest, requestId = 12
17:50:43.645 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1986f470-82af-4c36-ba62-f2ee325ec1db] Ack server push request, request = NotifySubscriberRequest, requestId = 12
17:50:43.656 [main] INFO  c.r.s.RuoYiShopApplication - [logStarted,61] - Started RuoYiShopApplication in 30.186 seconds (JVM running for 31.765)
17:50:43.748 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop-dev.yaml, group=DEFAULT_GROUP
17:50:43.749 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop, group=DEFAULT_GROUP
17:50:43.749 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop.yaml, group=DEFAULT_GROUP
17:50:44.176 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
