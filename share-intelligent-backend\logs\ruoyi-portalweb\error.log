09:18:43.880 [main] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.initCertificates(CertManagerSingleton.java:169)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.init(CertManagerSingleton.java:88)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.initCertManager(ScheduledUpdateCertificatesVerifier.java:32)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.<init>(ScheduledUpdateCertificatesVerifier.java:24)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3.getVerifier(WxPayConfigV3.java:79)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$12baa70d.CGLIB$getVerifier$16(<generated>)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$12baa70d$$FastClassBySpringCGLIB$$a7548af3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$12baa70d.getVerifier(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:641)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:477)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:548)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:524)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:677)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.portalweb.RuoYiPortalwebApplication.main(RuoYiPortalwebApplication.java:24)
09:18:44.207 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
16:07:58.760 [main] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.initCertificates(CertManagerSingleton.java:169)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.init(CertManagerSingleton.java:88)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.initCertManager(ScheduledUpdateCertificatesVerifier.java:32)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.<init>(ScheduledUpdateCertificatesVerifier.java:24)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3.getVerifier(WxPayConfigV3.java:79)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$db753674.CGLIB$getVerifier$7(<generated>)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$db753674$$FastClassBySpringCGLIB$$f87a3e85.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$db753674.getVerifier(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:641)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:477)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:548)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:524)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:677)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.portalweb.RuoYiPortalwebApplication.main(RuoYiPortalwebApplication.java:24)
16:07:59.044 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
17:07:59.000 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
17:59:14.102 [http-nio-9212-exec-10] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
17:59:14.237 [http-nio-9212-exec-9] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
17:59:14.238 [http-nio-9212-exec-2] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:09.192 [http-nio-9212-exec-7] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:09.198 [http-nio-9212-exec-1] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:09.206 [http-nio-9212-exec-6] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:25.975 [http-nio-9212-exec-8] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:25.975 [http-nio-9212-exec-3] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:25.975 [http-nio-9212-exec-5] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:29.622 [http-nio-9212-exec-4] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:29.622 [http-nio-9212-exec-10] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:00:29.624 [http-nio-9212-exec-9] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:04:12.298 [http-nio-9212-exec-3] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:04:12.298 [http-nio-9212-exec-5] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:04:12.298 [http-nio-9212-exec-7] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:05:25.197 [http-nio-9212-exec-4] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:05:25.201 [http-nio-9212-exec-2] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:05:25.201 [http-nio-9212-exec-6] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:06:18.003 [http-nio-9212-exec-5] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:06:18.035 [http-nio-9212-exec-10] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:06:18.035 [http-nio-9212-exec-9] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:07:34.401 [http-nio-9212-exec-4] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:07:34.434 [http-nio-9212-exec-7] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:07:34.434 [http-nio-9212-exec-1] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:08:00.011 [main] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.initCertificates(CertManagerSingleton.java:169)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.init(CertManagerSingleton.java:88)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.initCertManager(ScheduledUpdateCertificatesVerifier.java:32)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.<init>(ScheduledUpdateCertificatesVerifier.java:24)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3.getVerifier(WxPayConfigV3.java:79)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$7bc0e83d.CGLIB$getVerifier$10(<generated>)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$7bc0e83d$$FastClassBySpringCGLIB$$cae2e5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$7bc0e83d.getVerifier(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:641)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:477)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:548)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:524)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:677)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.portalweb.RuoYiPortalwebApplication.main(RuoYiPortalwebApplication.java:24)
18:08:00.242 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
18:08:40.448 [http-nio-9212-exec-2] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:08:40.448 [http-nio-9212-exec-1] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:08:40.448 [http-nio-9212-exec-3] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:11:22.511 [main] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.initCertificates(CertManagerSingleton.java:169)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.init(CertManagerSingleton.java:88)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.initCertManager(ScheduledUpdateCertificatesVerifier.java:32)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.<init>(ScheduledUpdateCertificatesVerifier.java:24)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3.getVerifier(WxPayConfigV3.java:79)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$32cdda1c.CGLIB$getVerifier$4(<generated>)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$32cdda1c$$FastClassBySpringCGLIB$$78f7feb8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$32cdda1c.getVerifier(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:641)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:477)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:548)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:524)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:677)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.portalweb.RuoYiPortalwebApplication.main(RuoYiPortalwebApplication.java:24)
18:11:22.761 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
18:11:50.770 [http-nio-9212-exec-5] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:11:50.770 [http-nio-9212-exec-6] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
18:11:50.770 [http-nio-9212-exec-4] ERROR c.r.c.s.s.TokenService - [getLoginUser,114] - 获取用户信息异常'JWT strings must contain exactly 2 period characters. Found: 0'
