<template>
  <div class="scienceFunding">
    <div class="content_banner">
      <div style="height: 40px">众筹科研</div>
      <div style="height: 33px; margin-top: 41px;font-size: 20px;">“众筹科研"新模式，构建"共投、共研、共担、共赢”新机制。</div>
    </div>
    <div class="card-container">
      <div class="content">
        <div class="desc">众筹科研由复合材料链主企业、上下游单位及创新平台共同出资，依托共享智造平台整合技术、产能与市场资源，推动材料
          研发、智能制造及产业化协同创新，实现风险共担、利益共亨的市场化运营，助力复材集群产业链升级与生态构建。</div>
        <div class="content-item">
          <div class="info">
            <img src="@/assets/scienceFunding/title1.png" class="info-title" alt="">
            <div class="info-text">由复材产业链链主企业或龙头企业、上下游企业、创新平台等单位发起并出资，按照
              市场化运营方式形成多方共投。
            </div>
          </div>
          <img src="@/assets/scienceFunding/img1.png" class="info-img" alt="">
        </div>
        <div class="content-item">
          <img src="@/assets/scienceFunding/img2.jpg" class="info-img" alt="">
          <div class="info">
            <img src="@/assets/scienceFunding/title2.png" class="info-title" alt="">
            <div class="info-text">以复材产业关键共性技术需求为牵引，组织高度关联方形成研发团队，实现技术应用
              企业和优势高校院所紧密合作，切实提升研发效率和成功率，促进科研与复材产业双向链接。
            </div>
          </div>
        </div>
        <div class="content-item">
          <div class="info">
            <img src="@/assets/scienceFunding/title3.png" class="info-title" alt="">
            <div class="info-text">坚持市场化运作，各方签订研发协议，提前约定研发内容、绩效目标、投入分配、预
              算安排、任务分工、实施周期、成果归属、风险控制、验收条件等，对于科研产生的风险由各方共同承担。
            </div>
          </div>
          <img src="@/assets/scienceFunding/img3.png" class="info-img" alt="">
        </div>
        <div class="content-item">
          <img src="@/assets/scienceFunding/img4.png" class="info-img" alt="">
          <div class="info">
            <img src="@/assets/scienceFunding/title4.png" class="info-title" alt="">
            <div class="info-text">项目产生的知识产权、数据、论文、新工艺、新技术、新产品、新方法等科技成果，
              由各方按约定共享，并优先在参与单位间使用、转让，所产生收益由各参与方按约定分配。鼓励行业共性技术
              在产业集群扩散，促进区域产业转型升级，实现多方共赢。
            </div>
          </div>
        </div>
        <el-table v-loading="loading" :data="SysTechRequirementList">
          <el-table-column label="技术需求" align="center" prop="requirementTitle" width="200" />
          <el-table-column label="发布方出资" align="center" prop="publisherInvestment" />
          <el-table-column label="计划资金" align="center" prop="plannedBudget" />
          <el-table-column label="发布企业" align="center" prop="publisherCompany" width="180" />
          <el-table-column label="联系人" align="center" prop="contactPerson" />
          <el-table-column label="联系方式" align="center" prop="contactPhone" width="110" />
          <el-table-column label="截止时间" align="center" prop="deadline" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.deadline, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleJoin(scope.row)">立即报名</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>


<script>
import {
  listSysTechRequirement
} from "@/api/techRequirement";
export default {
  name: 'ScienceFunding',
  data() {
    return {
      SysTechRequirementList: [],
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      isJoin: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true;
      listSysTechRequirement(this.queryParams).then(response => {
        this.SysTechRequirementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleJoin(row) {
      this.$router.push({
        path: '/joinSupply',
        query: {
          requirementId: row.requirementId,
          title: row.requirementTitle
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scienceFunding {
  width: 100%;
  padding-bottom: 60px;
  background-color: #F2F2F2;

  .content_banner {
    width: 100%;
    height: 300px;
    background-image: url("../../../../assets/release/banner.png");
    background-size: 100% 100%;
    text-align: center;
    margin: 0 auto;
    padding-top: 71px;
    font-weight: 500;
    font-size: 40px;
    color: #000;
    font-family: DOUYU;
  }
}

.card-container {
  width: 1200px;
  margin: 0 auto;

  .content {
    width: 100%;
    min-height: 500px;
    background-color: #fff;
    margin-top: 30px;
    border-radius: 5px;
    padding: 90px 110px;
    box-sizing: border-box;

    .desc {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      line-height: 34px;
      margin-bottom: 70px;
    }

    .content-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 60px 0;
      height: 225px;

      .info {
        width: 590px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .info-title {
          height: 30px;
          margin-bottom: 30px;
          object-fit: contain;
        }

        .info-text {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #5A5A5A;
          line-height: 28px;
          max-height: 160px;
          overflow: auto;
        }
      }

      .info-img {
        height: 100%;
      }
    }
  }

}
</style>
