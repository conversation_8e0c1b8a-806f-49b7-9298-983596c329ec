<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucScientificMapper">

    <resultMap type="UucScientific" id="UucScientificResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="image"    column="image"    />
        <result property="btypeName"    column="btype_name"    />
        <result property="btypeCode"    column="btype_code"    />
        <result property="duty"    column="duty"    />
        <result property="label"    column="label"    />
        <result property="briefIntr"    column="brief_intr"    />
        <result property="remark"    column="remark"    />
        <result property="sort"    column="sort"    />
        <result property="introduction"    column="introduction"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucScientificVo">
        select id, user_id, user_name, image, btype_name, btype_code, duty, label, brief_intr, remark, sort, introduction, status, del_flag, create_by, create_time, update_by, update_time from uuc_scientific
    </sql>

    <select id="selectUucScientificList" parameterType="UucScientific" resultMap="UucScientificResult">
        <include refid="selectUucScientificVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and user_id like concat('%', #{userId}, '%')</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="btypeName != null  and btypeName != ''"> and btype_name like concat('%', #{btypeName}, '%')</if>
            <if test="btypeCode != null  and btypeCode != ''"> and btype_code like concat('%', #{btypeCode}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppScientificList" parameterType="UucScientific" resultMap="UucScientificResult">
        <include refid="selectUucScientificVo"/>
        <where>
            <if test="searchValue != null  and searchValue != ''"> and (user_name like concat('%', #{searchValue}, '%') or btype_name like concat('%', #{searchValue}, '%') or brief_intr like concat('%', #{searchValue}, '%'))</if>
             and status = 1
        </where>
        order by sort desc
    </select>

    <select id="selectUucScientificById" parameterType="Long" resultMap="UucScientificResult">
        <include refid="selectUucScientificVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppScientificById" parameterType="Long" resultMap="UucScientificResult">
        <include refid="selectUucScientificVo"/>
        where id = #{id} and status = 1
    </select>

    <insert id="insertUucScientific" parameterType="UucScientific">
        insert into uuc_scientific
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="image != null">image,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="btypeCode != null">btype_code,</if>
            <if test="duty != null">duty,</if>
            <if test="label != null">label,</if>
            <if test="briefIntr != null">brief_intr,</if>
            <if test="remark != null">remark,</if>
            <if test="sort != null">sort,</if>
            <if test="introduction != null">introduction,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="image != null">#{image},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="btypeCode != null">#{btypeCode},</if>
            <if test="duty != null">#{duty},</if>
            <if test="label != null">#{label},</if>
            <if test="briefIntr != null">#{briefIntr},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sort != null">#{sort},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUucScientific" parameterType="UucScientific">
        update uuc_scientific
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="image != null">image = #{image},</if>
            <if test="btypeName != null">btype_name = #{btypeName},</if>
            <if test="btypeCode != null">btype_code = #{btypeCode},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="label != null">label = #{label},</if>
            <if test="briefIntr != null">brief_intr = #{briefIntr},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucScientificById" parameterType="Long">
        delete from uuc_scientific where id = #{id}
    </delete>

    <delete id="deleteUucScientificByIds" parameterType="String">
        delete from uuc_scientific where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>