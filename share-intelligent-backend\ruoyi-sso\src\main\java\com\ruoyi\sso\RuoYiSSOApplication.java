package com.ruoyi.sso;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.mybatis.spring.annotation.MapperScan;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;

/**
 * SSO单点登录认证服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
    HibernateJpaAutoConfiguration.class
})
@EnableDiscoveryClient
@EnableRyFeignClients
@MapperScan("com.ruoyi.sso.mapper")
public class RuoyiSSOApplication {

    public static void main(String[] args) {
        SpringApplication.run(RuoyiSSOApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  SSO认证服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
