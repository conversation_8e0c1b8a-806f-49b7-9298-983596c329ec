<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucScientificJointMapper">
    
    <resultMap type="UucScientificJoint" id="UucScientificJointResult">
        <result property="id"    column="id"    />
        <result property="scientifiId"    column="scientifi_id"    />
        <result property="scientifiName"    column="scientifi_name"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkTel"    column="link_tel"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucScientificJointVo">
        select id, scientifi_id, scientifi_name, link_man, link_tel, remark, create_by, create_time, update_by, update_time from uuc_scientific_joint
    </sql>

    <select id="selectUucScientificJointList" parameterType="UucScientificJoint" resultMap="UucScientificJointResult">
        <include refid="selectUucScientificJointVo"/>
        <where>  
            <if test="scientifiId != null  and scientifiId != ''"> and scientifi_id like concat('%', #{scientifiId}, '%')</if>
            <if test="scientifiName != null  and scientifiName != ''"> and scientifi_name like concat('%', #{scientifiName}, '%')</if>
            <if test="linkMan != null  and linkMan != ''"> and link_man like concat('%', #{linkMan}, '%')</if>
            <if test="linkTel != null  and linkTel != ''"> and link_tel like concat('%', #{linkTel}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUucScientificJointById" parameterType="Long" resultMap="UucScientificJointResult">
        <include refid="selectUucScientificJointVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUucScientificJoint" parameterType="UucScientificJoint">
        insert into uuc_scientific_joint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="scientifiId != null and scientifiId != ''">scientifi_id,</if>
            <if test="scientifiName != null and scientifiName != ''">scientifi_name,</if>
            <if test="linkMan != null and linkMan != ''">link_man,</if>
            <if test="linkTel != null and linkTel != ''">link_tel,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="scientifiId != null and scientifiId != ''">#{scientifiId},</if>
            <if test="scientifiName != null and scientifiName != ''">#{scientifiName},</if>
            <if test="linkMan != null and linkMan != ''">#{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">#{linkTel},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucScientificJoint" parameterType="UucScientificJoint">
        update uuc_scientific_joint
        <trim prefix="SET" suffixOverrides=",">
            <if test="scientifiId != null and scientifiId != ''">scientifi_id = #{scientifiId},</if>
            <if test="scientifiName != null and scientifiName != ''">scientifi_name = #{scientifiName},</if>
            <if test="linkMan != null and linkMan != ''">link_man = #{linkMan},</if>
            <if test="linkTel != null and linkTel != ''">link_tel = #{linkTel},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucScientificJointById" parameterType="Long">
        delete from uuc_scientific_joint where id = #{id}
    </delete>

    <delete id="deleteUucScientificJointByIds" parameterType="String">
        delete from uuc_scientific_joint where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>