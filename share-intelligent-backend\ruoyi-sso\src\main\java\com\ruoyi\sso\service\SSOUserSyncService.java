package com.ruoyi.sso.service;

import com.ruoyi.sso.domain.SSOUser;

/**
 * SSO用户同步服务接口
 * 负责将SSO用户信息同步到各个系统
 *
 * <AUTHOR>
 */
public interface SSOUserSyncService {

    /**
     * 同步用户信息到主系统
     *
     * @param ssoUser SSO用户信息
     * @return 同步结果
     */
    boolean syncToMainSystem(SSOUser ssoUser);

    /**
     * 同步用户信息到市场系统
     *
     * @param ssoUser SSO用户信息
     * @return 同步结果
     */
    boolean syncToMarketSystem(SSOUser ssoUser);

    /**
     * 同步用户信息到所有系统
     *
     * @param ssoUser SSO用户信息
     * @return 同步结果
     */
    boolean syncToAllSystems(SSOUser ssoUser);
}
