{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=style&index=0&id=2720d356&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750385853722}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8UA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">解决方案</div>\r\n      <div style=\"height: 33px; margin-top: 1px\"></div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"params.searchStr\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_search\">\r\n        <span>热门搜索：</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('供应链管理')\">供应链管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('设备智慧物联')\">设备智慧物联</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('生产过程管控')\">生产过程管控</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('科技成果转化')\">科技成果转化</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('企业运营管理')\">企业运营管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产业转型升级')\">产业转型升级</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产融服务')\">产融服务</span>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"content_bottom\">\r\n      <div class=\"icondiv\">\r\n        <div class=\"solutioniconFlex\">\r\n          <div v-for=\"(item, index) in typeList\" :key=\"item.id\"\r\n            :class=\"['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']\" @click=\"changeSolve(item.id)\">\r\n            {{ item.typeName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandContent\" style=\"padding-top: 40px\">\r\n        <div class=\"demandflex\" style=\"height: 715px\">\r\n          <div class=\"leftsolution\">\r\n            <div :class=\"['leftTitle', bbb == 1 ? 'leftTitleHover' : '']\" @click=\"changeSolveB(1)\">\r\n              全部（{{ total1 }}）\r\n            </div>\r\n            <div v-for=\"(item, index) in typeNestList\" :key=\"index\" :class=\"[\r\n              'leftTitle',\r\n              bbb == item.solutionTypeId ? 'leftTitleHover' : '',\r\n            ]\" @click=\"changeSolveB(item.solutionTypeId)\">\r\n              <span class=\"tr2\">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightSolution\" v-if=\"dataList && dataList.length > 0\" v-loading=\"loading\">\r\n            <div v-for=\"(item, index) in dataList\" :key=\"index\" class=\"solutionContent tr2\">\r\n              <div @click=\"goDetail(item.solutionId)\">\r\n                <div class=\"solutionContentTitle tr2\">\r\n                  {{ item.solutionName }}\r\n                </div>\r\n                <div class=\"solutionContentValue tr2 textOverflow\">\r\n                  {{ item.solutionIntroduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightEmpty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n        <!-- 分页 -->\r\n        <div class=\"pageStyle\">\r\n          <el-pagination v-if=\"dataList && dataList.length > 0\" background layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\" :page-size=\"params.pageSize\" :current-page=\"params.pageNum\" :total=\"total\"\r\n            @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getSolutionList, getSolutionTypeList } from \"@/api/solution\";\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      params: {\r\n        parentId: \"\",\r\n        searchStr: \"\",\r\n        solutionTypeId: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        category: 1,\r\n      },\r\n      total: 0,\r\n      total1: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"@/assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      demandList: [\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      aaa: \"1\",\r\n      bbb: 1,\r\n      typeList: [\r\n        {\r\n          id: \"1\",\r\n          typeName: \"行业解决方案\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          typeName: \"领域解决方案\",\r\n        },\r\n      ],\r\n      typeNestList: [],\r\n      dataList: [],\r\n      loading: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getTypeNext('1');\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      this.loading = true;\r\n      this.params.category = this.aaa;\r\n      let res = await getSolutionList(this.params);\r\n      if (res.code == 200) {\r\n        this.dataList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n        if (this.params.solutionTypeId == \"\") {\r\n          this.total1 = res.total;\r\n        }\r\n      }\r\n    },\r\n    searchHot(val) {\r\n      this.params.searchStr = val;\r\n      this.onSearch();\r\n    },\r\n    onSearch() {\r\n      this.params.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getDemandList();\r\n    },\r\n    async getTypeNext(val) {\r\n      let res = await getSolutionTypeList({ category: val });\r\n      if (res.code == 200) {\r\n        this.typeNestList = res.rows;\r\n        this.getDemandList();\r\n      }\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val;\r\n      this.params.parentId = val;\r\n      this.params.solutionTypeId = \"\";\r\n      this.bbb = 1;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      this.getTypeNext(val);\r\n    },\r\n    changeSolveB(val) {\r\n      this.bbb = val;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      if (val == 1) {\r\n        this.params.solutionTypeId = \"\";\r\n      } else {\r\n        this.params.solutionTypeId = val;\r\n      }\r\n      this.getDemandList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.params.pageSize = pageSize;\r\n      this.getDemandList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.params.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n\r\n    goDetail(id) {\r\n      this.$router.push(\"/solutionDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .hot_search {\r\n    font-size: 14px;\r\n    color: #000;\r\n\r\n    .hot_search_item {\r\n      margin-right: 20px;\r\n      color: #000;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  .icondiv {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 100%;\r\n    height: 100px;\r\n    position: relative;\r\n\r\n    .solutioniconFlex {\r\n      display: flex;\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 1200px;\r\n      right: 0;\r\n      left: 0;\r\n      margin: auto;\r\n      justify-content: center;\r\n\r\n      .iconFlexTitle {\r\n        width: 110px;\r\n        height: 45px;\r\n        line-height: 26px;\r\n        border-radius: 2px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 18px;\r\n        text-align: center;\r\n        margin: 0 20px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .activeTitle {\r\n        color: #0cad9d;\r\n        border-bottom: 2px solid #0cad9d;\r\n      }\r\n    }\r\n  }\r\n\r\n  .demandContent {\r\n    width: 100%;\r\n    background: #f7f8fa;\r\n    // background: #fff;\r\n    padding-top: 20px;\r\n    box-shadow: #21c9b8 solid 1px;\r\n    // border: #21c9b8 solid 1px;\r\n\r\n    .demandflex {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      flex-wrap: wrap;\r\n\r\n      .leftsolution {\r\n        width: 185px;\r\n        height: 715px;\r\n        line-height: 20px;\r\n        opacity: 0.95;\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg,\r\n            rgba(244, 246, 249, 1) 0%,\r\n            rgba(255, 255, 255, 1) 100%);\r\n        color: rgba(16, 16, 16, 1);\r\n        font-size: 14px;\r\n        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n        border: 2px solid rgba(255, 255, 255, 1);\r\n        padding: 20px 0;\r\n        box-sizing: border-box;\r\n        overflow-y: auto;\r\n\r\n        .leftTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 16px;\r\n          margin: 30px 0;\r\n          padding-left: 20px;\r\n          border-left: 3px solid transparent;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .leftTitleHover {\r\n          color: #0cad9d;\r\n          border-left: 3px solid #0cad9d;\r\n        }\r\n      }\r\n\r\n      .rightSolution {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        align-content: flex-start;\r\n\r\n        .solutionContent {\r\n          width: 490px;\r\n          height: 124px;\r\n          border: 2px solid transparent;\r\n          padding: 20px;\r\n          box-sizing: border-box;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .solutionContent:hover {\r\n          opacity: 0.95;\r\n          border-radius: 4px;\r\n          background: linear-gradient(180deg,\r\n              rgba(244, 246, 249, 1) 0%,\r\n              rgba(255, 255, 255, 1) 100%);\r\n          color: rgba(16, 16, 16, 1);\r\n          font-size: 14px;\r\n          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n          border: 2px solid rgba(255, 255, 255, 1);\r\n        }\r\n\r\n        .solutionContentTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .solutionContent:hover .solutionContentTitle {\r\n          color: #0cad9d;\r\n        }\r\n\r\n        .solutionContentValue {\r\n          color: rgba(102, 102, 102, 1);\r\n          font-size: 12px;\r\n          line-height: 1.5;\r\n        }\r\n      }\r\n\r\n      .rightEmpty {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}