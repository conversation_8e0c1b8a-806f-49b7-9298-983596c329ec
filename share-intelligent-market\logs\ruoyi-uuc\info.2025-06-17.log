17:43:51.092 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:43:53.406 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0
17:43:53.592 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 93 ms to scan 1 urls, producing 3 keys and 6 values 
17:43:53.669 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 4 keys and 9 values 
17:43:53.690 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
17:43:53.997 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 302 ms to scan 238 urls, producing 0 keys and 0 values 
17:43:54.014 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 5 values 
17:43:54.034 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
17:43:54.048 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
17:43:54.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 232 ms to scan 238 urls, producing 0 keys and 0 values 
17:43:54.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:43:54.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/947462790
17:43:54.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/886343183
17:43:54.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:43:54.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:43:54.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:56.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153436499_127.0.0.1_49790
17:43:56.928 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Notify connected event to listeners.
17:43:56.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:56.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [418e2978-0c1e-48d5-9d70-45a5fde6e3b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/95980430
17:43:57.174 [main] INFO  c.r.u.RuoyiUucApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:44:04.023 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9705"]
17:44:04.024 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:04.024 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:44:04.372 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:05.318 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:50:18.435 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:50:19.804 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0
17:50:19.899 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
17:50:19.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
17:50:19.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
17:50:20.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 223 ms to scan 238 urls, producing 0 keys and 0 values 
17:50:20.204 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
17:50:20.228 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
17:50:20.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:50:20.467 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 221 ms to scan 238 urls, producing 0 keys and 0 values 
17:50:20.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:50:20.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1841099284
17:50:20.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1231696346
17:50:20.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:50:20.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:50:20.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:22.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153822329_127.0.0.1_51524
17:50:22.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Notify connected event to listeners.
17:50:22.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:22.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c7d3dce-0474-4d2d-8e75-feeb364a1da2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/1879202713
17:50:22.848 [main] INFO  c.r.u.RuoyiUucApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:50:26.743 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9705"]
17:50:26.744 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:50:26.744 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:50:27.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:50:31.245 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:50:31.966 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07f93bfe-64f8-4cbb-8836-66113209afff
17:50:31.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] RpcClient init label, labels = {module=naming, source=sdk}
17:50:31.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:50:31.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:50:31.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:50:31.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:32.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153831979_127.0.0.1_51632
17:50:32.081 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Notify connected event to listeners.
17:50:32.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:32.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/1879202713
17:50:35.243 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9705"]
17:50:35.290 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-uuc ************:9705 register finished
17:50:35.695 [main] INFO  c.r.u.RuoyiUucApplication - [logStarted,61] - Started RuoyiUucApplication in 18.514 seconds (JVM running for 20.297)
17:50:35.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc.yaml, group=DEFAULT_GROUP
17:50:35.743 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc-dev.yaml, group=DEFAULT_GROUP
17:50:35.743 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc, group=DEFAULT_GROUP
17:50:35.758 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Receive server push request, request = NotifySubscriberRequest, requestId = 10
17:50:35.762 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f93bfe-64f8-4cbb-8836-66113209afff] Ack server push request, request = NotifySubscriberRequest, requestId = 10
17:50:36.447 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:50:36.541 [RMI TCP Connection(4)-************] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
