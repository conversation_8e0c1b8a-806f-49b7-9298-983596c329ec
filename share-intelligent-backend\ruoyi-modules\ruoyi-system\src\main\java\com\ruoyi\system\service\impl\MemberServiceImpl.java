package com.ruoyi.system.service.impl;

import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.mapper.MemberMapper;
import com.ruoyi.system.service.IMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MemberServiceImpl implements IMemberService {

    @Autowired
    private MemberMapper memberMapper;

    @Override
    public List<Long> selectMemberIdList() {
        return memberMapper.selectMemberIdList();
    }

    @Override
    public Member selectMemberByPhone(String memberPhone) {
        return memberMapper.selectMemberByMemberPhone(memberPhone);
    }
}
