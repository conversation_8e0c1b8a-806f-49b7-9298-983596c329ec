package com.ruoyi.sso.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 市场系统用户远程服务接口
 * 
 * <AUTHOR>
 */
@FeignClient(name = "market-system", url = "${sso.clients.market.url:http://localhost:9700}")
public interface RemoteMarketUserService {

    /**
     * 通过手机号查询市场系统用户信息
     *
     * @param phone 手机号
     * @return 结果
     */
    @GetMapping("/api/user/phone/{phone}")
    R<SysUser> getUserByPhone(@PathVariable("phone") String phone);

    /**
     * 在市场系统中注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @PostMapping("/api/user/register")
    R<Boolean> registerUser(@RequestBody SysUser sysUser);

    /**
     * 更新市场系统用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @PutMapping("/api/user/update")
    R<Boolean> updateUser(@RequestBody SysUser sysUser);
}
