{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\platIntroduction.vue?vue&type=style&index=1&id=3527ced8&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\platIntroduction.vue", "mtime": 1750385853717}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["platIntroduction.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "platIntroduction.vue", "sourceRoot": "src/views/aboutUs/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div class=\"content_bannerTitle\">易复材共享智造工业互联网平台</div>\r\n      <div class=\"content_bannerBox\"></div>\r\n      <div class=\"content_bannerContent\">创国内引领的行业级工业互联网平台</div>\r\n    </div>\r\n    <!-- <div class=\"aboutUsBody\">\r\n      <img src=\"@/assets/aboutUs/aboutUsBody.png\" alt=\"\" />\r\n    </div> -->\r\n    <div class=\"company\">\r\n      <div class=\"card-container\">\r\n        <div class=\"company-top\">\r\n          <img src=\"@/assets/aboutUs/company.png\" alt=\"\">\r\n          <div class=\"company-message\">\r\n            <div class=\"company-title\">易复材共享智造工业互联网平台</div>\r\n            <div class=\"company-content\">\r\n              易复材共享智造工业互联网平台以“4+1+1”模式为核心，通过制造、集采、服务、创新四大共享，\r\n              整合资源协同生产；依托共享示范工厂推动高端应用落地, 以数字化底座赋能设备互联与智能管理，\r\n              助力复合材料产业向集约化、智能化、绿色化转型升级\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"description\">\r\n          <ul class=\"list\">\r\n            <li>\r\n              <div class=\"imagebox\">\r\n                为集群赋能，创共享生态\r\n                <!-- <img src=\"@/assets/aboutUs/company.jpg\" alt=\"\" /> -->\r\n              </div>\r\n              <div class=\"listR\">\r\n                <p>\r\n                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业\r\n                  集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能\r\n                  力，打造 “4+1+1” 共享智造创新平台。\r\n                </p>\r\n                <p>\r\n                  通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合\r\n                  材料产业向高端化、智能化、绿色化转型。\r\n                  制造共享整合集群设备与订单资源，实现分布式协同生产，\r\n                  盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，\r\n                  降低采 购成本， 并为企业提供高效资金支持。\r\n                </p>\r\n                <p>\r\n                  服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；\r\n                  创新共享推动产学研深度融合，加速技术攻关与成果转化。\r\n                  平台依托共享示范工厂引入先进工艺设备，\r\n                  承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。\r\n                  以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为\r\n                  区域经济高质量发展注入核心动能。\r\n                </p>\r\n                <!--  <p>\r\n                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能力，打造“4+1+1”共享智造创新平台。通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合材料产业向高端化、智能化、绿色化转型。\r\n                  制造共享整合集群设备与订单资源，实现分布式协同生产，盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，降低采购成本，并为企业提供高效资金支持；服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；创新共享推动产学研深度融合，加速技术攻关与成果转化。\r\n                  平台依托共享示范工厂引入先进工艺设备，承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为区域经济高质量发展注入核心动能。\r\n                </p> -->\r\n              </div>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- <div class=\"course_box\">\r\n      <div class=\"aboutMengdou\">发展历程</div>\r\n      <ul class=\"course_ul wow\">\r\n        <li v-for=\"item, i of list\" :ref=\"`list_item${i + 1}`\" class=\"animate__animated\" :key=\"i\">\r\n          <div class=\"course_ul_l\" :class=\"[i % 2 != 0 ? 'course_ul_la' : 'course_ul_lb']\">\r\n            <span>{{ item.time }}</span>\r\n          </div>\r\n          <div class=\"course_ul_r\">\r\n            <p>{{ item.text }}</p>\r\n          </div>\r\n        </li>\r\n      </ul>\r\n    </div> -->\r\n    <div class=\"flexFooter \">\r\n      <div class=\"card-container\">\r\n        <div class=\"process-title\">平台发展历程</div>\r\n        <div class=\"process\">\r\n          <!-- <img class=\"line\" src=\"@/assets/aboutUs/process-line.png\" alt=\"\">\r\n          <div class=\"process-2021\">\r\n            <div class=\"process-time\">2021年</div>\r\n            <div class=\"process-content\">\r\n              寻共需\r\n            </div>\r\n          </div>\r\n          <div class=\"process-2022\">\r\n            <div class=\"process-content\">\r\n              促共享\r\n            </div>\r\n            <div class=\"process-time\">2022年</div>\r\n          </div>\r\n          <div class=\"process-2023\">\r\n            <div class=\"process-time\">2023年</div>\r\n            <div class=\"process-content\">\r\n              谋发展\r\n            </div>\r\n          </div>\r\n          <div class=\"process-2025\">\r\n            <div class=\"process-content\">\r\n              创未来\r\n            </div>\r\n            <div class=\"process-time\">2025年</div>\r\n          </div> -->\r\n        </div>\r\n\r\n        <video class=\"videoRef\" src=\"@/assets/video/aboutUs.mp4\" loop isMuted=\"true\" controls autoplay\r\n          playsinline></video>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  // padding-bottom: 60px;\r\n}\r\n\r\n.aboutUsBody {\r\n  width: 1200px;\r\n  height: 650px;\r\n  margin: 30px auto;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .content_bannerBox {\r\n    height: 11px;\r\n  }\r\n\r\n  .content_bannerContent {\r\n    font-size: 22px;\r\n    color: #222222;\r\n  }\r\n\r\n  .content_bannerTitle {\r\n    font-size: 40px;\r\n    color: #222222;\r\n  }\r\n}\r\n\r\n.flexFooter {\r\n  padding-bottom: 80px;\r\n  box-sizing: border-box;\r\n  background: url(\"../../../assets/aboutUs/aboutUs-footer.png\");\r\n  background-size: 100% auto;\r\n  background-position: bottom;\r\n  background-repeat: no-repeat;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n\r\n  .process-title {\r\n    font-weight: 500;\r\n    font-size: 34px;\r\n    color: #000000;\r\n  }\r\n\r\n  .process {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 473px;\r\n    background-image: url(\"../../../assets/aboutUs/process-all.png\");\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    .line {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      width: 100%;\r\n    }\r\n\r\n    .process-2021 {\r\n      position: absolute;\r\n      left: 12%;\r\n      top: 65%;\r\n    }\r\n\r\n    .process-2022 {\r\n      position: absolute;\r\n      left: 33%;\r\n      top: 45%;\r\n    }\r\n\r\n    .process-2023 {\r\n      position: absolute;\r\n      left: 53%;\r\n      top: 50%;\r\n    }\r\n\r\n    .process-2025 {\r\n      position: absolute;\r\n      left: 75%;\r\n      top: 30%;\r\n    }\r\n\r\n    .process-time {\r\n      font-weight: 500;\r\n      font-size: 18px;\r\n      color: #000000;\r\n    }\r\n\r\n    .process-content {\r\n      font-weight: 400;\r\n      font-size: 22px;\r\n      color: #3E3D3D;\r\n    }\r\n  }\r\n\r\n  .videoRef {\r\n    width: 1200px;\r\n  }\r\n\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.company {\r\n  width: 100%;\r\n  height: 890px;\r\n  background-image: url(\"../../../assets/aboutUs/company-bg.png\");\r\n  background-size: 100%;\r\n  /* 背景图片覆盖整个元素 */\r\n  background-repeat: no-repeat;\r\n  /* 背景图片不重复 */\r\n  background-position: 0 0;\r\n  /* 背景图片居中 */\r\n  padding-top: 75px;\r\n  box-sizing: border-box;\r\n\r\n  .company-top {\r\n    width: 100%;\r\n    height: 300px;\r\n    display: flex;\r\n\r\n    img {\r\n      height: 100%;\r\n    }\r\n\r\n    .company-message {\r\n      height: 100%;\r\n      background-color: #F5F6F6;\r\n      padding: 0 60px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n\r\n      .company-title {\r\n        font-size: 34px;\r\n        color: #000000;\r\n        font-weight: 500;\r\n        padding-bottom: 45px;\r\n      }\r\n\r\n      .company-content {\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #3E3D3D;\r\n        line-height: 28px;\r\n        opacity: 0.94;\r\n      }\r\n    }\r\n  }\r\n\r\n  .description {\r\n    width: 100%;\r\n    margin: 0 auto;\r\n\r\n    .list {\r\n      width: 100%;\r\n      padding: 0;\r\n      // margin: 0 auto;\r\n      padding: 65px 0;\r\n\r\n      li {\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        .listR {\r\n          width: 713px;\r\n          height: 362px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-around;\r\n\r\n          p {\r\n            margin: 0;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #3E3D3D;\r\n            line-height: 26px;\r\n          }\r\n        }\r\n\r\n        .imagebox {\r\n          flex: 1;\r\n          display: flex;\r\n          align-items: center;\r\n          // text-align: center;\r\n          font-size: 34px;\r\n          color: #000000;\r\n          // img {\r\n          //   width: 100%;\r\n          // }\r\n        }\r\n\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// .swiper-pagination-bullet {\r\n//   background: #fff;\r\n// }\r\n\r\n// .swiper-wrapper {\r\n//   position: relative;\r\n// }\r\n\r\n// .swiper-container {\r\n//   width: 100%;\r\n// }\r\n\r\n// .swiper-container2 {\r\n//   width: 100%;\r\n//   overflow: hidden;\r\n// }\r\n\r\n.container {\r\n  width: 100%;\r\n}\r\n\r\n.banner {\r\n  width: 100%;\r\n  height: 400px;\r\n}\r\n</style>\r\n<style scoped>\r\n.course_box {\r\n  background: url(\"../../../assets/aboutUs/enterBgc.png\") no-repeat center center;\r\n  background-size: 100% 100%;\r\n  height: 920px;\r\n  overflow: hidden;\r\n}\r\n\r\n.course_box .course_ul {\r\n  width: 1226px;\r\n  margin: 0px auto 50px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.course_box .course_ul li {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  opacity: 0;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_la::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  border: 1px solid #21c9b8;\r\n  border-radius: 50%;\r\n  background-color: #fbfbfa;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_lb::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  background: #21c9b8;\r\n  border-radius: 50%;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l {\r\n  position: relative;\r\n  width: 120px;\r\n  position: relative;\r\n  margin-right: 20px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 6px;\r\n  top: 0;\r\n  width: 2px;\r\n  height: 50px;\r\n  background: linear-gradient(0deg, #f6f6f6, #21c9b8);\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l span {\r\n  color: #333333;\r\n  font-size: 26px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_r {\r\n  flex: 1;\r\n  height: 60px;\r\n  border: 1px solid #ffffff;\r\n  background-color: rgba(247, 247, 247, 0.8);\r\n  padding: 0 20px;\r\n  line-height: 16px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_r p {\r\n  font-size: 16px;\r\n  color: #5a5a5a;\r\n}\r\n\r\n.aboutMengdou {\r\n  width: 100%;\r\n  text-align: center;\r\n  padding: 50px 0 50px;\r\n  box-sizing: border-box;\r\n  font-size: 30px;\r\n  color: #333333;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport WOW from 'wow.js';\r\nimport 'animate.css';\r\n\r\nexport default {\r\n  name: \"Com\",\r\n  data() {\r\n    return {\r\n      activeIndex: 0,\r\n      list: [\r\n        {\r\n          time: '2023',\r\n          text: \"荣获国家级跨领域跨行业工业互联网平台\"\r\n        },\r\n        {\r\n          time: '2022',\r\n          text: \"建立云端研发新模式，成长为瞪羚企业，获评山东省双跨工业互联网平台、国家特色专业型工业互联网平台\"\r\n        },\r\n        {\r\n          time: '2021',\r\n          text: \"建立上百条优势物料资源线，上线事业合伙人模式，打破发展瓶颈，建立青岛市中小企业供应链场景实验室\"\r\n        },\r\n        {\r\n          time: '2020',\r\n          text: \"智能制造模式上线，平台发展扩展至电子制造、冷库冷藏仪器仪表、新零售畜牧业养殖五大行业营收破2亿\"\r\n        },\r\n        {\r\n          time: '2019',\r\n          text: \"完成A轮融资，玺品直播上线，企业级在线支付工具-楼豆宝上线，获批国家商务部认定的线上线下融合数字商务企业\"\r\n        }, {\r\n          time: '2018',\r\n          text: \"玺品创新模式上线，智能采购模式行业扩展\"\r\n        }, {\r\n          time: '2017',\r\n          text: \"总部落地青岛，首个智能采购机器人--豆小秘上线，智能采购模式上线，颠覆式赋能\"\r\n        }, {\r\n          time: '2016',\r\n          text: \"首创上下游裂变式上线模式，檬豆云实现5次迭代，探索智能采购模式\"\r\n        },\r\n        {\r\n          time: '2015',\r\n          text: \"公司成立于上海，完成天使轮融资檬豆云上线\"\r\n        },\r\n      ],\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    handleMouseEnter(index) {\r\n      this.activeIndex = index;\r\n    },\r\n    handleMouseLeave() {\r\n      // 鼠标离开后恢复默认(第一个保持黑色)\r\n      this.activeIndex = 0;\r\n    },\r\n    initWow() {\r\n      var wow = new WOW({\r\n        boxClass: \"wow\",\r\n        animateClass: \"animated\",\r\n        offset: 200,\r\n        mobile: true,\r\n        live: true,\r\n        callback: (e) => {\r\n          // console.log(this.$refs.list_item1);\r\n          for (let i = 0; i < this.list.length; i++) {\r\n            setTimeout(() => {\r\n              var dom = this.$refs[`list_item${i + 1}`][0]\r\n              console.log(this.$refs[`list_item${i + 1}`][0]);\r\n              dom.classList.add('animate__fadeInUpBig');\r\n              dom.style.opacity = 1;\r\n            }, i * 220);\r\n          }\r\n        },\r\n        scrollContainer: null,\r\n        resetAnimation: true,\r\n      });\r\n      wow.init();\r\n    }\r\n  },\r\n\r\n  computed: {},\r\n\r\n  created() { },\r\n\r\n  mounted() {\r\n    this.initWow();\r\n  },\r\n\r\n\r\n  components: {},\r\n\r\n  watch: {},\r\n  mixins: [],\r\n};\r\n</script>\r\n"]}]}