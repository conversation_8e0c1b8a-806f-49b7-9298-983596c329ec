<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucProductMapper">
    
    <resultMap type="UucProduct" id="UucProductResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="productName"    column="product_name"    />
        <result property="btypeCode"    column="btype_code"    />
        <result property="btypeName"    column="btype_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="collaborative"    column="collaborative"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="label"    column="label"    />
        <result property="image"    column="image"    />
        <result property="content"    column="content"    />
        <result property="remark"    column="remark"    />
        <result property="recommendStatus"    column="recommend_status"    />
        <result property="pictures"    column="pictures"    />
        <result property="attaches"    column="attaches"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucProductVo">
        select id, product_no, product_name, btype_code, btype_name, short_name, collaborative, contact, phone, label, image, content, remark, recommend_status, pictures, attaches, create_by, create_time, update_by, update_time from uuc_product
    </sql>

    <select id="selectUucProductList" parameterType="UucProduct" resultMap="UucProductResult">
        <include refid="selectUucProductVo"/>
        <where>  
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="collaborative != null  and collaborative != ''"> and collaborative = #{collaborative}</if>
            <if test="contact != null  and contact != ''"> and contact like concat('%', #{contact}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="recommendStatus != null  and recommendStatus != ''"> and recommend_status = #{recommendStatus}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppProductList" parameterType="UucProduct" resultMap="UucProductResult">
        <include refid="selectUucProductVo"/>
        <where>
            <if test="searchValue != null  and searchValue != ''"> and product_name like concat('%', #{searchValue}, '%')</if>
            <if test="collaborative != null  and collaborative != ''"> and collaborative = #{collaborative}</if>
            and recommend_status = 1
        </where>
        order by id desc
    </select>

    <select id="selectUucProductById" parameterType="Long" resultMap="UucProductResult">
        <include refid="selectUucProductVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppProductById" parameterType="Long" resultMap="UucProductResult">
        <include refid="selectUucProductVo"/>
        where id = #{id} and recommend_status = 1
    </select>

    <insert id="insertUucProduct" parameterType="UucProduct">
        insert into uuc_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productNo != null">product_no,</if>
            <if test="productName != null">product_name,</if>
            <if test="btypeCode != null">btype_code,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="shortName != null">short_name,</if>
            <if test="collaborative != null">collaborative,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="label != null">label,</if>
            <if test="image != null">image,</if>
            <if test="content != null">content,</if>
            <if test="remark != null">remark,</if>
            <if test="recommendStatus != null">recommend_status,</if>
            <if test="pictures != null">pictures,</if>
            <if test="attaches != null">attaches,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="productName != null">#{productName},</if>
            <if test="btypeCode != null">#{btypeCode},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="collaborative != null">#{collaborative},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="label != null">#{label},</if>
            <if test="image != null">#{image},</if>
            <if test="content != null">#{content},</if>
            <if test="remark != null">#{remark},</if>
            <if test="recommendStatus != null">#{recommendStatus},</if>
            <if test="pictures != null">#{pictures},</if>
            <if test="attaches != null">#{attaches},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertUucAppProduct" parameterType="UucProduct">
        insert into uuc_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productNo != null">product_no,</if>
            <if test="productName != null">product_name,</if>
            <if test="btypeCode != null">btype_code,</if>
            <if test="btypeName != null">btype_name,</if>
            <if test="shortName != null">short_name,</if>
            <if test="collaborative != null">collaborative,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="label != null">label,</if>
            <if test="image != null">image,</if>
            <if test="content != null">content,</if>
            <if test="remark != null">remark,</if>
            recommend_status,
            <if test="pictures != null">pictures,</if>
            <if test="attaches != null">attaches,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="productName != null">#{productName},</if>
            <if test="btypeCode != null">#{btypeCode},</if>
            <if test="btypeName != null">#{btypeName},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="collaborative != null">#{collaborative},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="label != null">#{label},</if>
            <if test="image != null">#{image},</if>
            <if test="content != null">#{content},</if>
            <if test="remark != null">#{remark},</if>
            0,
            <if test="pictures != null">#{pictures},</if>
            <if test="attaches != null">#{attaches},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUucProduct" parameterType="UucProduct">
        update uuc_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="btypeCode != null">btype_code = #{btypeCode},</if>
            <if test="btypeName != null">btype_name = #{btypeName},</if>
            <if test="shortName != null">short_name = #{shortName},</if>
            <if test="collaborative != null">collaborative = #{collaborative},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="label != null">label = #{label},</if>
            <if test="image != null">image = #{image},</if>
            <if test="content != null">content = #{content},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="recommendStatus != null">recommend_status = #{recommendStatus},</if>
            <if test="pictures != null">pictures = #{pictures},</if>
            <if test="attaches != null">attaches = #{attaches},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucProductById" parameterType="Long">
        delete from uuc_product where id = #{id}
    </delete>

    <delete id="deleteUucProductByIds" parameterType="String">
        delete from uuc_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>