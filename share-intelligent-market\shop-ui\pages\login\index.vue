<!-- 登录 -->
<template>
    <div class="login-cont">
        <div class="login">
            <!-- <div class="logo">
                <img @click="toIndex" src="@/static/login.png" alt="" />
            </div> -->
            <div class="title">
                <img class="" @click="toIndex" src="@/static/dllogo.svg" alt="" />
                <span>黔东南州工业互联网 </span> 
                <p>-产业链协同平台</p>
            </div>
           
            <div class="left"></div>
            <div class="right">
                <div class="top">
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane label="验证码登录" name="first">
                            <el-form
                                v-if="activeName == 'first'"
                                :model="codeForm"
                                :rules="codeRules"
                                ref="ruleForm"
                                class="demo-ruleForm"
                            >
                                <el-form-item label="" prop="telphone">
                                    <el-input
                                        placeholder="账号/手机号"
                                        v-model="codeForm.telphone"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="" prop="code">
                                    <div class="input-item">
                                        <el-input
                                            placeholder="验证码"
                                            v-model="codeForm.code"
                                        ></el-input>
                                        <el-button @click="getCode">
                                            {{
                                                verifyTime == 60
                                                    ? "发送验证码"
                                                    : "发送中" + verifyTime
                                            }}
                                        </el-button>
                                    </div>
                                </el-form-item>
                                <el-form-item label="">
                                    <div class="flex agreement">
                                        <div>
                                            <el-radio
                                                @click.native.prevent="
                                                    radioChange
                                                "
                                                :value="radio"
                                                label="1"
                                            >
                                                <span>已阅读并同意</span>
                                            </el-radio>
                                        </div>

                                        <span @click="dialogVisible = true"
                                            >《用户服务协议》</span
                                        >
                                    </div>
                                </el-form-item>

                                <el-form-item label="">
                                    <div class="login-btn">
                                        <el-button
                                            @click="submitForm('ruleForm')"
                                            type="primary"
                                            >登 录</el-button
                                        >
                                    </div>
                                </el-form-item>
                                <el-form-item label="">
                                    <div class="login-btn">
                                        <el-button
                                            @click="handleSSOLogin"
                                            type="info"
                                            class="sso-button"
                                            >SSO统一登录</el-button
                                        >
                                    </div>
                                </el-form-item>
                            </el-form>
                        </el-tab-pane>
                        <el-tab-pane label="账号登录" name="second">
                            <el-form
                                v-if="activeName == 'second'"
                                :model="loginForm"
                                :rules="loginRules"
                                ref="ruleForm"
                                class="demo-ruleForm"
                            >
                                <el-form-item label="" prop="telphone">
                                    <el-input
                                        placeholder="账号/手机号"
                                        v-model="loginForm.telphone"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="" prop="password">
                                    <div>
                                        <el-input
                                            type="password"
                                            placeholder="密码"
                                            v-model="loginForm.password"
                                        ></el-input>
                                    </div>
                                </el-form-item>
                                <el-form-item prop="captcha">
                                  <el-input
                                    v-model="loginForm.captcha"
                                    auto-complete="off"
                                    placeholder="验证码"
                                    style="width: 63%"
                                  >
                                  </el-input>
                                  <div class="login-code">
                                    <img :src="codeUrl" @click="getImgCode" class="login-code-img"/>
                                  </div>
                                </el-form-item>
                                <el-form-item label="">
                                    <div
                                        class="flex agreement"
                                        @click="clickRadios"
                                    >
                                        <el-radio
                                            @click.native.prevent="radioChange"
                                            :value="radio"
                                            label="1"
                                        >
                                            <span>已阅读并同意</span>
                                        </el-radio>
                                        <span @click="dialogVisible = true"
                                            >《用户服务协议》</span
                                        >
                                    </div>
                                </el-form-item>

                                <el-form-item label="">
                                    <div class="login-btn">
                                        <el-button
                                            @click="submitForm('ruleForm')"
                                            type="primary"
                                            >登 录</el-button
                                        >
                                    </div>
                                </el-form-item>
                                <el-form-item label="">
                                    <div class="login-btn">
                                        <el-button
                                            @click="handleSSOLogin"
                                            type="info"
                                            class="sso-button"
                                            >SSO统一登录</el-button
                                        >
                                    </div>
                                </el-form-item>
                            </el-form>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div class="bottom"></div>
            </div>
        </div>
        <el-dialog title="" :visible.sync="dialogVisible" width="80%">
            <clause></clause>
        </el-dialog>
    </div>
</template>

<script>
import config from "@/utils/config.js";
import { loginCode, getLoginCode, login, getCodeImg, getSSOLoginUrl } from "@/apis/login";
import { getProfile } from "@/apis/common/common";
import { setToken } from "@/utils/auth";
import { setCookie } from "@/utils/cookies";
import clause from "@/pages/clause/index.vue";
import axios from "axios";
import { encrypt } from "@/utils/aes";
export default {
    head() {
        return {
            title: "登录",
            meta: [
                {
                    hid: "",
                    name: "",
                    content: "",
                },
            ],
        };
    },
    components: {
        clause,
    },
    data() {
        return {
            logo: "https://resource.ningmengdou.com/image/20220830/202208301408303866.png",
            dialogVisible: false,
            // 验证码获取倒计时60秒
            verifyTime: 60,
            captchaOnOff:false,
            // 是否是获取验证码状态
            isEendCode: false,
            radio: 0,
            input: "",
            activeName: "first",
            codeForm: {
                telphone: "",
                code: "",
            },
            loginForm: {
                telphone: "",
                password: "",
                captcha:''
            },
            codeUrl:'',
            checkKey:'',
            codeRules: {
                telphone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur",
                    },
                    {
                        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                        message: "请输入正确的手机号码",
                        trigger: "blur",
                    },
                ],
                code: [
                    {
                        required: true,
                        message: "请输入验证码",
                        trigger: "blur",
                    },
                ],
            },
            loginRules: {
                telphone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur",
                    },
                    {
                        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                        message: "请输入正确的手机号码",
                        trigger: "blur",
                    },
                ],
                password: [
                    {
                        required: true,
                        message: "请输入密码",
                        trigger: "blur",
                    },
                ],
                captcha: [
                    {
                        required: true,
                        message: "请输入验证码",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    created() {
      this.getImgCode()
    },
    methods: {
      getImgCode() {
        this.checkKey=Date.parse(new Date())
        console.log('获取验证码，checkKey:', this.checkKey);
        getCodeImg(this.checkKey).then(res => {
          console.log('验证码接口响应:', res);
          this.codeUrl = res.data
          console.log('设置codeUrl:', this.codeUrl ? this.codeUrl.substring(0, 50) + '...' : 'null');
        }).catch(err => {
          console.error('获取验证码失败:', err);
        });
      },
        clickRadios() {
            console.log(this.radio);
        },
        radioChange(e) {
            this.radio == 0 ? (this.radio = "1") : (this.radio = "0");
            console.log(this.radio);
        },
        toIndex() {
            this.$router.push({
                path: "/",
            });
        },
        // 获取验证码
        async getCode() {
            if (!this.codeForm.telphone) {
                this.$message({
                    type: "warning",
                    message: "请输入手机号!",
                });
                return;
            }
            if (this.radio == 0) {
                // 警告提示
                this.$message({
                    message: "请阅读并同意用户协议",
                    type: "warning",
                });
                return;
            }
            if (!this.isEendCode) {
                getLoginCode({
                    telphone: this.codeForm.telphone,
                })
                    .then((data) => {
                        if (data.code == 200) {
                            this.isEendCode = true;
                            var time = setInterval(() => {
                                if (this.verifyTime == 0) {
                                    this.verifyTime = 60;
                                    this.isEendCode = false;
                                    clearInterval(time);
                                    return;
                                }
                                this.verifyTime--;
                            }, 1000);
                        } else {
                            // 失败提示
                            this.$message.error("验证码发送失败");
                        }
                    })
                    .catch((err) => {});
            }
        },
        // 设置用户信息
        setUserInfo(userInfo) {
            var _this = this;
            // 提示登录成功
            this.$message.success("登录成功");
            // 存储用户信息到本地
            window.localStorage.setItem("user", JSON.stringify(userInfo));
            // vuex存储用户信息
            this.$store.commit("user/setUserInfo");
            setToken(userInfo.token);
            setCookie("permission", JSON.stringify(userInfo.permission));
            this.$store.commit("user/setPermi");
            getProfile({}).then((res) => {
                if (res.code == 200) {
                    let profile = res.data;
                    window.localStorage.setItem(
                        "profile",
                        JSON.stringify(profile)
                    );
                    this.$store.commit("user/setProfile");
                }
            });

            // 跳转到首页
            this.$router.push("/");
        },
        // 密码登录
        login() {
         // encrypt
            console.log('开始密码登录，表单数据:', {
                telphone: this.loginForm.telphone,
                password: '***',
                checkKey: this.checkKey,
                captcha: this.loginForm.captcha
            });

            login({
                telphone: this.loginForm.telphone,
                password: encrypt(this.loginForm.password),
                checkKey: this.checkKey,
                captcha: this.loginForm.captcha,

            }).then((res) => {
                console.log('登录响应:', res);
                if (res.code == 200) {
                    let userInfo = res.data;
                    console.log('登录成功，用户信息:', userInfo);
                    this.setUserInfo(userInfo);
                } else {
                    console.error('登录失败，错误信息:', res.msg || res.message);
                    this.$message.error(res.msg || res.message || '登录失败');
                    // 刷新验证码
                    this.getImgCode();
                }
            }).catch((error) => {
                console.error('登录请求异常:', error);
                this.$message.error('登录请求失败，请检查网络连接');
                // 刷新验证码
                this.getImgCode();
            });
        },
        // 融云登录
        // rongyunLogin() {
        //     let userInfot = JSON.parse(localStorage.getItem("user"));
        //     let profile = JSON.parse(localStorage.getItem("profile"));
        //     axios
        //         .post(this.$xpURL + "/xipin/im/user/add", {
        //             userId: userInfot.username,
        //             name: userInfot.username,
        //             portraitUri: profile.avatar,
        //         })
        //         .then(function (res) {
        //             if (res.data.code == 200) {
        //                 if (process.client) {
        //                     localStorage.setItem(
        //                         "rongyunToken",
        //                         res.data.data.token
        //                     );
        //                 }
        //             }
        //         })
        //         .catch(function (error) {
        //             console.log(error);
        //         });
        // },
        // 验证码登录
        loginCode() {
            loginCode({
                ...this.codeForm,
            }).then((res) => {
                if (res.code == 200) {
                    let userInfo = res.data;
                    this.setUserInfo(userInfo);
                }
            });
        },
        submitForm(formName) {
            console.log('提交表单，当前选项卡:', this.activeName, '协议状态:', this.radio);
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.radio == 0) {
                        // 警告提示
                        this.$message({
                            message: "请阅读并同意用户协议",
                            type: "warning",
                        });
                        return;
                    }
                    if (this.activeName == "first") {
                        // 验证码登录
                        console.log('执行验证码登录');
                        this.loginCode();
                    } else {
                        // 密码登录
                        console.log('执行密码登录');
                        this.login();
                    }
                } else {
                    this.$modal.msgError("请完善信息再提交!");
                    console.log("表单验证失败");
                    return false;
                }
            });
        },
        handleClick(tab, event) {
            if (process.client) {
                // 客户端
                //
                var activebar = document.querySelector(
                    ".login-cont .el-tabs__active-bar.is-top"
                );
                if (this.activeName == "first") {
                    activebar.style.left = "18px";
                } else {
                    activebar.style.left = "10px";
                }
            }
        },
        toClause() {
            this.$router.push({
                path: "/clause",
            });
        },
        // SSO登录
        handleSSOLogin() {
            const currentUrl = window.location.origin + this.$route.fullPath;
            getSSOLoginUrl(currentUrl)
                .then(response => {
                    if (response.code === 200 && response.data && response.data.loginUrl) {
                        // 跳转到SSO登录页面
                        window.location.href = response.data.loginUrl;
                    } else {
                        this.$message.error("获取SSO登录地址失败");
                    }
                })
                .catch(error => {
                    console.error("SSO登录失败:", error);
                    this.$message.error("SSO登录服务异常");
                });
        }
    },
};
</script>
<style>
#__nuxt {
    width: 100%;
    height: 100%;
}

#__layout {
    width: 100%;
    height: 100%;
}

.login-cont .el-tabs__nav-wrap::after {
    display: none;
}

.login-cont .el-tabs__active-bar {
    width: 32px !important;
    height: 2px !important;
    background: #4072e1 !important;
    left: 18px;
}

.login-cont .el-input__inner {
    border-radius: 0;
    width: 317px;
    height: 50px;
    background: #ffffff;
    border: 1px solid #d2d2d2;
}

.login-cont .el-input {
    width: auto;
}

.login-cont .el-button {
    border-radius: 0;
}

.login-cont .input-item .el-input__inner {
    width: 200px;
}

.login-cont .input-item .el-button {
    height: 50px;

    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4475e1;
    width: 120px;
    /* border-left: none; */
}

.login-cont .el-tabs__header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.login-btn .el-button {
    width: 100%;
    width: 317px;
    height: 50px;
    background: #4072e1;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
}

.login-btn .el-button span {
    color: #fff !important;
}

.sso-button {
    background: #409eff !important;
    border-color: #409eff !important;
}

.agreement .el-radio {
    margin-right: 0;
    align-items: center;
    position: relative;
    left: 3px;
    z-index: 99999999;
}
</style>
<style lang="scss" scoped>
  .login-code {
    width: 33%;
    height: 38px;
    position: absolute;
    right: 14px;
    top: 2px;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
  .login-code-img {
    height: 38px;
  }
.login-cont {
    background: url("../../static/login/bg.png") no-repeat;
    background-size: 100%, 100%;
    width: 100%;
    height: 100%;
    position: relative;

    .login {
        position: relative;

        .logo {
            // width: 150px;
            // height: 44px;
            border-radius: 20px 20px 0 0;
            position: absolute;
            left: 0;
            top: -41px;
            // background: #166aff;
            cursor: pointer;
            img {
                width: 100%;
                height: 100%;
            }
        }

        width: 914px;
        height: 545px;
        background: #ccc;
        position: absolute;
        //     right: 360px;
        // top: 189px;
        transform: translate(-50%, -50%);
        left: 57.5%;
        top: 47%;
        display: flex;
        align-items: center;

        .left {
            width: 471px;
            height: 545px;
            background: url("../../static/login/bg1.png") no-repeat;
            background-size: 100%, 100%;
        }

        .right {
            width: 443px;

            .agreement {
                align-items: center;
                cursor: pointer;
            }

            .agreement span {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #555555;
            }

            .agreement span:nth-child(2) {
                color: #4072e1;
            }

            .input-item {
                display: flex;
                align-items: center;
            }

            .top {
                height: 533px;
                background: #fff;
                padding: 74px 63px;
            }

            .bottom {
                height: 12px;
                background: #4072e1;
            }
        }
    }
}
.title{
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    display: flex;
}
.title img{
    width: 36px;
    height: 36px;
}
.title span{
    line-height: 36px;
    margin-left: 5px;
    
}
.title p{
    line-height: 36px;
}
</style>
