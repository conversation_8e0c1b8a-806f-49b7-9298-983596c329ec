{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue", "mtime": 1750385853720}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_release", "require", "data", "form", "processName", "processingQuantity", "outsourcingContent", "requiredCompletionTime", "projectNumber", "remarks", "companyName", "<PERSON><PERSON><PERSON>", "contactPhone", "rules", "required", "message", "trigger", "created", "userinfo", "JSON", "parse", "window", "sessionStorage", "getItem", "memberCompanyName", "memberRealName", "memberPhone", "methods", "onSubmit", "status", "_this", "$refs", "validate", "valid", "releaseProcess", "then", "res", "code", "$message", "success", "onCancel", "error", "$router", "go"], "sources": ["src/views/release/components/processRequire.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"外协工序名称\" prop=\"processName\">\r\n        <el-input v-model=\"form.processName\" maxlength=\"50\" show-word-limit placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"加工数量\" prop=\"processingQuantity\">\r\n        <el-input type=\"number\" min=\"0\" v-model=\"form.processingQuantity\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"外协加工内容\" prop=\"outsourcingContent\">\r\n        <el-input v-model=\"form.outsourcingContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n          show-word-limit placeholder=\"请输入\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"要求完成时间\" prop=\"requiredCompletionTime\">\r\n        <el-date-picker v-model=\"form.requiredCompletionTime\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\"\r\n          style=\"width: 100%\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"订单号\" prop=\"projectNumber\">\r\n        <el-input v-model=\"form.projectNumber\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"备注\" prop=\"remarks\">\r\n        <el-input v-model=\"form.remarks\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input disabled v-model=\"form.companyName\" placeholder=\"请先绑定公司\"></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input disabled v-model=\"form.contactPerson\" placeholder=\"请先维护联系人\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input disabled v-model=\"form.contactPhone\" placeholder=\"请先维护联系方式\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseProcess } from \"@/api/release\"\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        processName: \"\",\r\n        processingQuantity: \"\",\r\n        outsourcingContent: \"\",\r\n        requiredCompletionTime: \"\",\r\n        projectNumber: \"\",\r\n        remarks: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        processName: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        processingQuantity: [\r\n          { required: true, message: \"加工数量不能为空\", trigger: \"blur\" },\r\n        ],\r\n        outsourcingContent: [\r\n          { required: true, message: \"外协加工内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        requiredCompletionTime: [\r\n          { required: true, message: \"要求完成时间不能为空\", trigger: \"blur\" },\r\n        ],\r\n        projectNumber: [\r\n          { required: true, message: \"订单号不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if(userinfo && userinfo != 'null') {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contactPerson = userinfo.memberRealName;\r\n      this.form.contactPhone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit(status) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n\r\n          releaseProcess(this.form).then(res => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel()\r\n            } else {\r\n              this.$message.error('发布失败')\r\n            }\r\n          })\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AA2CA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,aAAA;QACAC,OAAA;QACAC,WAAA;QACAC,aAAA;QACAC,YAAA;MACA;MACA;MACAC,KAAA;QACAT,WAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,kBAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,kBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,sBAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,aAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,IAAAL,QAAA,IAAAA,QAAA;MACA,KAAAf,IAAA,CAAAO,WAAA,GAAAQ,QAAA,CAAAM,iBAAA;MACA,KAAArB,IAAA,CAAAQ,aAAA,GAAAO,QAAA,CAAAO,cAAA;MACA,KAAAtB,IAAA,CAAAS,YAAA,GAAAM,QAAA,CAAAQ,WAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UAEA,IAAAC,uBAAA,EAAAJ,KAAA,CAAA3B,IAAA,EAAAgC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACAT,KAAA,CAAAU,QAAA;YACA;cACAV,KAAA,CAAAQ,QAAA,CAAAG,KAAA;YACA;UACA;QACA;MACA;IACA;IACAD,QAAA,WAAAA,SAAA;MACA,KAAAE,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}