{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue", "mtime": 1750385853725}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOw0KaW1wb3J0IHsgc3VwcGx5RGF0YSB9IGZyb20gIkAvYXBpL2hvbWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJkZW1hbmRIYWxsIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBwYWdlTnVtOiAxLA0KICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgdG90YWw6IDAsDQogICAgICBrZXl3b3JkczogIiIsDQogICAgICBmb3JtOiB7fSwNCiAgICAgIHN1cHBseVR5cGVMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLllrfmvIblt6UiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi5a6J5YWo5ZGYIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIumHh+i0reWRmCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlt6HpgLvlkZgiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi5py65qKw5Yi25Zu+5ZGYIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIui/kOiQpeS4k+WRmCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlrqPkvKDlkZgiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi6aG555uu57uP55CGIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIuaWh+WRmCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlhbblroMiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIHN1cHBseVR5cGU6ICIxIiwNCiAgICAgIHRlY2hUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi56CU56m255SfIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIuacrOenkSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlpKfkuJMiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi6auY5LitIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIuS4reS4kyIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlhbblroMiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIHRlY2hUeXBlOiAiIiwNCiAgICAgIHByb2R1Y3RUeXBlTGlzdDogW10sDQogICAgICBwcm9kdWN0VHlwZTogIiIsDQogICAgICBhY2hpZXZlU3RhZ2VMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLnibnnuqflt6XnqIvluIgiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi6auY57qn5bel56iL5biIIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIuW3peeoi+W4iCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLliqnnkIblt6XnqIvluIgiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIGFjaGlldmVTdGFnZTogIiIsDQogICAgICBjb29wZXJhdGlvbk1vZGVMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlnKjogYwiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi56a76IGMIiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBjb29wZXJhdGlvbk1vZGU6ICIiLA0KICAgICAgc3VwcGx5TGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLmma7mi4nlpJogMjAyNeasviAyLjRUIOaXl+iIsFZY54mIIDXluqciLA0KICAgICAgICAgIHVybDogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2RlbWFuZC94cWltZ2RlZmF1bHQucG5nIiksDQogICAgICAgICAgYXBwbGlBcmVhOiAiIiwNCiAgICAgICAgICByZXF1aXJlVHlwZTogIuWFtuWugyIsDQogICAgICAgICAgZGVzYzogIuS4reWkp+Wei1NVVu+8jOayueeUtea3t+WQiO+8jOacgOWkp+WKn+eOhyhrVykyNDPvvIzmnIDlpKfmia3nn6koTsK3bSk2MzDvvIzlj5jpgJ/nrrE45oyh5omL6Ieq5LiA5L2T77yM6ZW/KuWuvSrpq5gobW0pNDkzNSoxOTgwKjE5MzXvvIzovabouqvnu5PmnoQ16ZeoNeW6p1NVVu+8jCDmnIDpq5jovabpgJ8oa20vaCkxNzAiLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAiMjAyNS0wMi0yOCAwOTo0OToyMSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuaZruaLieWkmiAyMDI15qy+IDIuNFQg5peX6IiwVljniYggNeW6pyIsDQogICAgICAgICAgdXJsOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvZGVtYW5kL3hxaW1nZGVmYXVsdC5wbmciKSwNCiAgICAgICAgICBhcHBsaUFyZWE6ICIiLA0KICAgICAgICAgIHJlcXVpcmVUeXBlOiAi5YW25a6DIiwNCiAgICAgICAgICBkZXNjOiAi5Lit5aSn5Z6LU1VW77yM5rK555S15re35ZCI77yM5pyA5aSn5Yqf546HKGtXKTI0M++8jOacgOWkp+aJreefqShOwrdtKTYzMO+8jOWPmOmAn+eusTjmjKHmiYvoh6rkuIDkvZPvvIzplb8q5a69KumrmChtbSk0OTM1KjE5ODAqMTkzNe+8jOi9pui6q+e7k+aehDXpl6g15bqnU1VW77yMIOacgOmrmOi9pumAnyhrbS9oKTE3MCIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI1LTAyLTI4IDA5OjQ5OjIxIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5pmu5ouJ5aSaIDIwMjXmrL4gMi40VCDml5foiLBWWOeJiCA15bqnIiwNCiAgICAgICAgICB1cmw6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9kZW1hbmQveHFpbWdkZWZhdWx0LnBuZyIpLA0KICAgICAgICAgIGFwcGxpQXJlYTogIiIsDQogICAgICAgICAgcmVxdWlyZVR5cGU6ICLlhbblroMiLA0KICAgICAgICAgIGRlc2M6ICLkuK3lpKflnotTVVbvvIzmsrnnlLXmt7flkIjvvIzmnIDlpKflip/njocoa1cpMjQz77yM5pyA5aSn5omt55+pKE7Ct20pNjMw77yM5Y+Y6YCf566xOOaMoeaJi+iHquS4gOS9k++8jOmVvyrlrr0q6auYKG1tKTQ5MzUqMTk4MCoxOTM177yM6L2m6Lqr57uT5p6ENemXqDXluqdTVVbvvIwg5pyA6auY6L2m6YCfKGttL2gpMTcwIiwNCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjUtMDItMjggMDk6NDk6MjEiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLmma7mi4nlpJogMjAyNeasviAyLjRUIOaXl+iIsFZY54mIIDXluqciLA0KICAgICAgICAgIHVybDogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2RlbWFuZC94cWltZ2RlZmF1bHQucG5nIiksDQogICAgICAgICAgYXBwbGlBcmVhOiAiIiwNCiAgICAgICAgICByZXF1aXJlVHlwZTogIuWFtuWugyIsDQogICAgICAgICAgZGVzYzogIuS4reWkp+Wei1NVVu+8jOayueeUtea3t+WQiO+8jOacgOWkp+WKn+eOhyhrVykyNDPvvIzmnIDlpKfmia3nn6koTsK3bSk2MzDvvIzlj5jpgJ/nrrE45oyh5omL6Ieq5LiA5L2T77yM6ZW/KuWuvSrpq5gobW0pNDkzNSoxOTgwKjE5MzXvvIzovabouqvnu5PmnoQ16ZeoNeW6p1NVVu+8jCDmnIDpq5jovabpgJ8oa20vaCkxNzAiLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAiMjAyNS0wMi0yOCAwOTo0OToyMSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuaZruaLieWkmiAyMDI15qy+IDIuNFQg5peX6IiwVljniYggNeW6pyIsDQogICAgICAgICAgdXJsOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvZGVtYW5kL3hxaW1nZGVmYXVsdC5wbmciKSwNCiAgICAgICAgICBhcHBsaUFyZWE6ICIiLA0KICAgICAgICAgIHJlcXVpcmVUeXBlOiAi5YW25a6DIiwNCiAgICAgICAgICBkZXNjOiAi5Lit5aSn5Z6LU1VW77yM5rK555S15re35ZCI77yM5pyA5aSn5Yqf546HKGtXKTI0M++8jOacgOWkp+aJreefqShOwrdtKTYzMO+8jOWPmOmAn+eusTjmjKHmiYvoh6rkuIDkvZPvvIzplb8q5a69KumrmChtbSk0OTM1KjE5ODAqMTkzNe+8jOi9pui6q+e7k+aehDXpl6g15bqnU1VW77yMIOacgOmrmOi9pumAnyhrbS9oKTE3MCIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI1LTAyLTI4IDA5OjQ5OjIxIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5pmu5ouJ5aSaIDIwMjXmrL4gMi40VCDml5foiLBWWOeJiCA15bqnIiwNCiAgICAgICAgICB1cmw6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9kZW1hbmQveHFpbWdkZWZhdWx0LnBuZyIpLA0KICAgICAgICAgIGFwcGxpQXJlYTogIiIsDQogICAgICAgICAgcmVxdWlyZVR5cGU6ICLlhbblroMiLA0KICAgICAgICAgIGRlc2M6ICLkuK3lpKflnotTVVbvvIzmsrnnlLXmt7flkIjvvIzmnIDlpKflip/njocoa1cpMjQz77yM5pyA5aSn5omt55+pKE7Ct20pNjMw77yM5Y+Y6YCf566xOOaMoeaJi+iHquS4gOS9k++8jOmVvyrlrr0q6auYKG1tKTQ5MzUqMTk4MCoxOTM177yM6L2m6Lqr57uT5p6ENemXqDXluqdTVVbvvIwg5pyA6auY6L2m6YCfKGttL2gpMTcwIiwNCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjUtMDItMjggMDk6NDk6MjEiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLmma7mi4nlpJogMjAyNeasviAyLjRUIOaXl+iIsFZY54mIIDXluqciLA0KICAgICAgICAgIHVybDogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2RlbWFuZC94cWltZ2RlZmF1bHQucG5nIiksDQogICAgICAgICAgYXBwbGlBcmVhOiAiIiwNCiAgICAgICAgICByZXF1aXJlVHlwZTogIuWFtuWugyIsDQogICAgICAgICAgZGVzYzogIuS4reWkp+Wei1NVVu+8jOayueeUtea3t+WQiO+8jOacgOWkp+WKn+eOhyhrVykyNDPvvIzmnIDlpKfmia3nn6koTsK3bSk2MzDvvIzlj5jpgJ/nrrE45oyh5omL6Ieq5LiA5L2T77yM6ZW/KuWuvSrpq5gobW0pNDkzNSoxOTgwKjE5MzXvvIzovabouqvnu5PmnoQ16ZeoNeW6p1NVVu+8jCDmnIDpq5jovabpgJ8oa20vaCkxNzAiLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAiMjAyNS0wMi0yOCAwOTo0OToyMSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuaZruaLieWkmiAyMDI15qy+IDIuNFQg5peX6IiwVljniYggNeW6pyIsDQogICAgICAgICAgdXJsOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvZGVtYW5kL3hxaW1nZGVmYXVsdC5wbmciKSwNCiAgICAgICAgICBhcHBsaUFyZWE6ICIiLA0KICAgICAgICAgIHJlcXVpcmVUeXBlOiAi5YW25a6DIiwNCiAgICAgICAgICBkZXNjOiAi5Lit5aSn5Z6LU1VW77yM5rK555S15re35ZCI77yM5pyA5aSn5Yqf546HKGtXKTI0M++8jOacgOWkp+aJreefqShOwrdtKTYzMO+8jOWPmOmAn+eusTjmjKHmiYvoh6rkuIDkvZPvvIzplb8q5a69KumrmChtbSk0OTM1KjE5ODAqMTkzNe+8jOi9pui6q+e7k+aehDXpl6g15bqnU1VW77yMIOacgOmrmOi9pumAnyhrbS9oKTE3MCIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI1LTAyLTI4IDA5OjQ5OjIxIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5pmu5ouJ5aSaIDIwMjXmrL4gMi40VCDml5foiLBWWOeJiCA15bqnIiwNCiAgICAgICAgICB1cmw6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9kZW1hbmQveHFpbWdkZWZhdWx0LnBuZyIpLA0KICAgICAgICAgIGFwcGxpQXJlYTogIiIsDQogICAgICAgICAgcmVxdWlyZVR5cGU6ICLlhbblroMiLA0KICAgICAgICAgIGRlc2M6ICLkuK3lpKflnotTVVbvvIzmsrnnlLXmt7flkIjvvIzmnIDlpKflip/njocoa1cpMjQz77yM5pyA5aSn5omt55+pKE7Ct20pNjMw77yM5Y+Y6YCf566xOOaMoeaJi+iHquS4gOS9k++8jOmVvyrlrr0q6auYKG1tKTQ5MzUqMTk4MCoxOTM177yM6L2m6Lqr57uT5p6ENemXqDXluqdTVVbvvIwg5pyA6auY6L2m6YCfKGttL2gpMTcwIiwNCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjUtMDItMjggMDk6NDk6MjEiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLmma7mi4nlpJogMjAyNeasviAyLjRUIOaXl+iIsFZY54mIIDXluqciLA0KICAgICAgICAgIHVybDogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2RlbWFuZC94cWltZ2RlZmF1bHQucG5nIiksDQogICAgICAgICAgYXBwbGlBcmVhOiAiIiwNCiAgICAgICAgICByZXF1aXJlVHlwZTogIuWFtuWugyIsDQogICAgICAgICAgZGVzYzogIuS4reWkp+Wei1NVVu+8jOayueeUtea3t+WQiO+8jOacgOWkp+WKn+eOhyhrVykyNDPvvIzmnIDlpKfmia3nn6koTsK3bSk2MzDvvIzlj5jpgJ/nrrE45oyh5omL6Ieq5LiA5L2T77yM6ZW/KuWuvSrpq5gobW0pNDkzNSoxOTgwKjE5MzXvvIzovabouqvnu5PmnoQ16ZeoNeW6p1NVVu+8jCDmnIDpq5jovabpgJ8oa20vaCkxNzAiLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAiMjAyNS0wMi0yOCAwOTo0OToyMSIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgZml0OiAiY292ZXIiLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRTdXBwbHlEaWN0KCk7IC8vIOS+m+e7meexu+Weiw0KICAgIHRoaXMuZ2V0VGVjaFR5cGVEaWN0KCk7IC8vIOaKgOacr+exu+WIqw0KICAgIHRoaXMuZ2V0U3RhZ2VEaWN0KCk7IC8vIOaIkOaenOmYtuautQ0KICAgIHRoaXMuZ2V0Q29vcGVyYXRpb25EaWN0KCk7IC8vIOWQiOS9nOaWueW8jw0KICAgIHRoaXMuZ2V0UHJvZHVjdFR5cGVEaWN0KCk7IC8vIOS6p+WTgeexu+WIqw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouWtl+WFuOaVsOaNruWIl+ihqCAqLw0KICAgIGdldFN1cHBseURpY3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogInN1cHBseV90eXBlIiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnN1cHBseVR5cGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgLy8gdGhpcy5zdXBwbHlUeXBlTGlzdC51bnNoaWZ0KHsgZGljdFZhbHVlOiAiIiwgZGljdExhYmVsOiAi5YWo6YOoIiB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0VGVjaFR5cGVEaWN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJ0ZWNobm9sb2d5X2NhdGVnb3J5IiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnRlY2hUeXBlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudGVjaFR5cGVMaXN0LnVuc2hpZnQoeyBkaWN0VmFsdWU6ICIiLCBkaWN0TGFiZWw6ICLlhajpg6giIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRQcm9kdWN0VHlwZURpY3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogInByb2R1Y3RfY2F0ZWdvcnkiIH07DQogICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMucHJvZHVjdFR5cGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy5wcm9kdWN0VHlwZUxpc3QudW5zaGlmdCh7IGRpY3RWYWx1ZTogIiIsIGRpY3RMYWJlbDogIuWFqOmDqCIgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFN0YWdlRGljdCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IGRpY3RUeXBlOiAic3VwcGx5X3Byb2Nlc3MiIH07DQogICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuYWNoaWV2ZVN0YWdlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMuYWNoaWV2ZVN0YWdlTGlzdC51bnNoaWZ0KHsgZGljdFZhbHVlOiAiIiwgZGljdExhYmVsOiAi5YWo6YOoIiB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0Q29vcGVyYXRpb25EaWN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJzdXBwbHlfY29vcGVyYXRpb24iIH07DQogICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuY29vcGVyYXRpb25Nb2RlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMuY29vcGVyYXRpb25Nb2RlTGlzdC51bnNoaWZ0KHsgZGljdFZhbHVlOiAiIiwgZGljdExhYmVsOiAi5YWo6YOoIiB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiB0aGlzLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICB0eXBlOiB0aGlzLnN1cHBseVR5cGUsDQogICAgICAgIHRlY2hub2xvZ3lDYXRlZ29yeTogdGhpcy50ZWNoVHlwZSwNCiAgICAgICAgcHJvZHVjdFR5cGU6IHRoaXMucHJvZHVjdFR5cGUsDQogICAgICAgIHByb2Nlc3M6IHRoaXMuYWNoaWV2ZVN0YWdlLA0KICAgICAgICBjb29wZXJhdGlvblR5cGU6IHRoaXMuY29vcGVyYXRpb25Nb2RlLA0KICAgICAgICBrZXl3b3JkOiB0aGlzLmtleXdvcmRzLA0KICAgICAgfTsNCiAgICAgIHN1cHBseURhdGEocGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnN1cHBseUxpc3QgPSByZXMucm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uU2VhcmNoKCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHBhZ2VTaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gcGFnZVNpemU7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UocGFnZU51bSkgew0KICAgICAgdGhpcy5wYWdlTnVtID0gcGFnZU51bTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgc3dpdGNoU3VwcGx5VHlwZSh2YWx1ZSkgew0KICAgICAgdGhpcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuc3VwcGx5VHlwZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBzd2l0Y2hUZWNoVHlwZSh2YWx1ZSkgew0KICAgICAgdGhpcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMudGVjaFR5cGUgPSB2YWx1ZTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgc3dpdGNoUHJvZHVjdFR5cGVUeXBlKHZhbHVlKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5wcm9kdWN0VHlwZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBzd2l0Y2hBY2hpZXZlU3RhZ2UodmFsdWUpIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmFjaGlldmVTdGFnZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBzd2l0Y2hDb29wZXJhdGlvbk1vZGUodmFsdWUpIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmNvb3BlcmF0aW9uTW9kZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBnb0RldGFpbChpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zdXBwbHlEZXRhaWw/aWQ9IiArIGlkKTsNCiAgICB9LA0KICAgIGluaXRQYWdlKCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICByZWZyZXNoKCkgew0KICAgICAgdGhpcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuc3VwcGx5VHlwZSA9ICcxJzsNCiAgICAgIHRoaXMudGVjaFR5cGUgPSAiIjsNCiAgICAgIHRoaXMuYWNoaWV2ZVN0YWdlID0gIiI7DQogICAgICB0aGlzLmNvb3BlcmF0aW9uTW9kZSA9ICIiOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["supplyHall.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "supplyHall.vue", "sourceRoot": "src/views/supplyDemandDocking/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">供给大厅</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">Supply Hall</div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"keywords\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">供给分类：</div>\r\n        <div class=\"smallCategory\" :class=\"supplyType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in supplyTypeList\" :key=\"index\" @click=\"switchSupplyType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '1'\">\r\n        <div class=\"largeCategory\">服务类别：</div>\r\n        <div class=\"smallCategory\" :class=\"techType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in techTypeList\" :key=\"index\" @click=\"switchTechType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_item\" v-if=\"supplyType == '2'\">\r\n        <div class=\"largeCategory\">产品类别：</div>\r\n        <div class=\"smallCategory\" :class=\"productType === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in productTypeList\" :key=\"index\" @click=\"switchProductTypeType(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">成果阶段：</div>\r\n        <div class=\"smallCategory\" :class=\"achieveStage === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in achieveStageList\" :key=\"index\" @click=\"switchAchieveStage(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">合作方式：</div>\r\n        <div class=\"smallCategory\" :class=\"cooperationMode === item.dictValue ? 'smallCategoryActive' : ''\r\n          \" v-for=\"(item, index) in cooperationModeList\" :key=\"index\" @click=\"switchCooperationMode(item.dictValue)\">\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div> -->\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initPage\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/serviceSharing/reset.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"refresh\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"card-container\" v-loading=\"loading\">\r\n      <div class=\"content_bottom\" v-if=\"supplyList && supplyList.length > 0\">\r\n        <div class=\"content_bottom_item tr2\" v-for=\"(item, index) in supplyList\" :key=\"index\"\r\n          @click=\"goDetail(item.id)\">\r\n          <div class=\"detailTitle textOverflow1 tr2\">\r\n            {{ item.title }}\r\n          </div>\r\n          <div class=\"demandChunk\">\r\n            <!-- 左侧图片 -->\r\n            <div>\r\n              <img style=\"width: 130px; height: 130px\" :src=\"item.imageUrl\r\n                  ? item.imageUrl\r\n                  : require('../../../assets/demand/xqimgdefault.png')\r\n                \" alt=\"\" />\r\n            </div>\r\n            <!-- 右侧内容 -->\r\n            <div class=\"demand_right\">\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">供给方：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.organization }}\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle\">应用领域：</div>\r\n                <div class=\"detailrightContent\">\r\n                  {{ item.applicationAreaName }}\r\n                </div>\r\n              </div> -->\r\n              <!-- <div class=\"detailrightTitle2 textOverflow2\">\r\n                {{ item.desc }}\r\n              </div> -->\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '1'\">\r\n                <div class=\"detailrightTitle tr2\">服务类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.technologyCategoryName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\" v-if=\"supplyType == '2'\">\r\n                <div class=\"detailrightTitle tr2\">产品类别：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.productName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"demandTopRightflex\">\r\n                <div class=\"detailrightTitle tr2\">发布时间：</div>\r\n                <div class=\"detailrightContent tr2 textOverflow1\">\r\n                  {{ item.createTime }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image style=\"width: 160px; height: 160px\" :src=\"require('@/assets/user/none.png')\" :fit=\"fit\"></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination v-if=\"supplyList && supplyList.length > 0\" background layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n          @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { supplyData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      supplyTypeList: [\r\n        {\r\n          dictLabel: \"喷漆工\",\r\n        },\r\n        {\r\n          dictLabel: \"安全员\",\r\n        },\r\n        {\r\n          dictLabel: \"采购员\",\r\n        },\r\n        {\r\n          dictLabel: \"巡逻员\",\r\n        },\r\n        {\r\n          dictLabel: \"机械制图员\",\r\n        },\r\n        {\r\n          dictLabel: \"运营专员\",\r\n        },\r\n        {\r\n          dictLabel: \"宣传员\",\r\n        },\r\n        {\r\n          dictLabel: \"项目经理\",\r\n        },\r\n        {\r\n          dictLabel: \"文员\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      supplyType: \"1\",\r\n      techTypeList: [\r\n        {\r\n          dictLabel: \"研究生\",\r\n        },\r\n        {\r\n          dictLabel: \"本科\",\r\n        },\r\n        {\r\n          dictLabel: \"大专\",\r\n        },\r\n        {\r\n          dictLabel: \"高中\",\r\n        },\r\n        {\r\n          dictLabel: \"中专\",\r\n        },\r\n        {\r\n          dictLabel: \"其它\",\r\n        },\r\n      ],\r\n      techType: \"\",\r\n      productTypeList: [],\r\n      productType: \"\",\r\n      achieveStageList: [\r\n        {\r\n          dictLabel: \"特级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"高级工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"工程师\",\r\n        },\r\n        {\r\n          dictLabel: \"助理工程师\",\r\n        },\r\n      ],\r\n      achieveStage: \"\",\r\n      cooperationModeList: [\r\n        {\r\n          dictLabel: \"在职\",\r\n        },\r\n        {\r\n          dictLabel: \"离职\",\r\n        },\r\n      ],\r\n      cooperationMode: \"\",\r\n      supplyList: [\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"普拉多 2025款 2.4T 旗舰VX版 5座\",\r\n          url: require(\"../../../assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getSupplyDict(); // 供给类型\r\n    this.getTechTypeDict(); // 技术类别\r\n    this.getStageDict(); // 成果阶段\r\n    this.getCooperationDict(); // 合作方式\r\n    this.getProductTypeDict(); // 产品类别\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getSupplyDict() {\r\n      let params = { dictType: \"supply_type\" };\r\n      listData(params).then((response) => {\r\n        this.supplyTypeList = response.rows;\r\n        // this.supplyTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getTechTypeDict() {\r\n      let params = { dictType: \"technology_category\" };\r\n      listData(params).then((response) => {\r\n        this.techTypeList = response.rows;\r\n        this.techTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getProductTypeDict() {\r\n      let params = { dictType: \"product_category\" };\r\n      listData(params).then((response) => {\r\n        this.productTypeList = response.rows;\r\n        this.productTypeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getStageDict() {\r\n      let params = { dictType: \"supply_process\" };\r\n      listData(params).then((response) => {\r\n        this.achieveStageList = response.rows;\r\n        this.achieveStageList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getCooperationDict() {\r\n      let params = { dictType: \"supply_cooperation\" };\r\n      listData(params).then((response) => {\r\n        this.cooperationModeList = response.rows;\r\n        this.cooperationModeList.unshift({ dictValue: \"\", dictLabel: \"全部\" });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        type: this.supplyType,\r\n        technologyCategory: this.techType,\r\n        productType: this.productType,\r\n        process: this.achieveStage,\r\n        cooperationType: this.cooperationMode,\r\n        keyword: this.keywords,\r\n      };\r\n      supplyData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    onSearch() {\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    switchSupplyType(value) {\r\n      this.pageNum = 1;\r\n      this.supplyType = value;\r\n      this.getList();\r\n    },\r\n    switchTechType(value) {\r\n      this.pageNum = 1;\r\n      this.techType = value;\r\n      this.getList();\r\n    },\r\n    switchProductTypeType(value) {\r\n      this.pageNum = 1;\r\n      this.productType = value;\r\n      this.getList();\r\n    },\r\n    switchAchieveStage(value) {\r\n      this.pageNum = 1;\r\n      this.achieveStage = value;\r\n      this.getList();\r\n    },\r\n    switchCooperationMode(value) {\r\n      this.pageNum = 1;\r\n      this.cooperationMode = value;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/supplyDetail?id=\" + id);\r\n    },\r\n    initPage() {\r\n      this.getList();\r\n    },\r\n    refresh() {\r\n      this.pageNum = 1;\r\n      this.supplyType = '1';\r\n      this.techType = \"\";\r\n      this.achieveStage = \"\";\r\n      this.cooperationMode = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 58px 60px 32px 62px;\r\n\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n\r\n    &:before {\r\n      content: \"\";\r\n      z-index: -1;\r\n      position: absolute;\r\n      top: 100%;\r\n      left: 100%;\r\n      width: 86px;\r\n      height: 86px;\r\n      border-radius: 50%;\r\n      background-color: #21c9b8;\r\n      transform-origin: center;\r\n      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);\r\n      transition: transform 0.3s ease-in;\r\n    }\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .textOverflow2 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n\r\n    div {\r\n      color: #ffffff !important;\r\n    }\r\n\r\n    &::before {\r\n      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}