<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item label="供给标题" prop="title">
        <el-input v-model="form.title" maxlength="50" show-word-limit placeholder="请输入供给标题"></el-input>
      </el-form-item>
      <el-form-item prop="type" label="供给类型">
        <el-radio-group v-model="form.type" placeholder="请选择" clearable @change="changeType">
          <el-radio v-for="dict in supplyTypeList" :key="dict.dictValue" :label="dict.dictValue"
            :value="dict.dictValue">{{ dict.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="description" label="供给描述（可按需求产品+应用行业+应用领域进行描述）">
        <el-input v-model="form.description" type="textarea" resize="none" :rows="8" maxlength="500" show-word-limit
          placeholder="该供给产品、成果、服务的原理、特点优势，与旧技术的对比数据成本情况等。填写越详细，匹配越准确。" />
      </el-form-item>
      <el-form-item prop="technologyCategory" label="服务类别" v-if="form.type != 2">
        <el-checkbox-group v-model="form.technologyCategory" placeholder="请选择" clearable>
          <el-checkbox v-for="dict in techTypeList" :key="dict.dictValue" :label="dict.dictValue"
            :value="dict.dictValue">{{ dict.dictLabel }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="productCategory" label="产品类别" v-if="form.type == 2">
        <el-checkbox-group v-model="form.productCategory" placeholder="请选择" clearable>
          <el-checkbox v-for="dict in productTypeList" :key="dict.dictCode" :label="dict.dictValue"
            :value="dict.dictValue">{{ dict.dictLabel }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item prop="applications" label="应用领域">
        <div class="apptag">
          <i
            class="el-icon-circle-plus-outline"
            style="font-size: 24px; color: rgba(187, 187, 187, 1)"
            @click="showDialog()"
          ></i>
          <el-tag
            v-for="(item, index) in demandapptagList"
            :key="index"
            closable
            @close="closeTag(item, index)"
          >
            {{ item.applicationFieldName }}
          </el-tag>
        </div>
      </el-form-item> -->
      <el-form-item label="场景图片" prop="imageUrl">
        <ImageUpload :limit="1" v-model="imageUrlList" />
        <!-- <ImageUpload :limit="1" v-model="form.imageUrl" /> -->
      </el-form-item>
      <el-form-item label="合作方式" prop="cooperationType">
        <el-select v-model="form.cooperationType" placeholder="请选择" clearable style="width: 100%">
          <el-option v-for="dict in cooperationModeOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品阶段" prop="process">
        <el-select v-model="form.process" placeholder="请选择" clearable style="width: 100%">
          <el-option v-for="dict in productStageList" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.attachment" />
      </el-form-item>
      <el-form-item label="公司名称">
        <el-input disabled v-model="form.organization" placeholder="请先绑定公司"></el-input>
      </el-form-item>
      <el-form-item label="联系人">
        <el-input disabled v-model="form.contact" placeholder="请先维护联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input disabled v-model="form.phone" placeholder="请先维护联系方式"></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button type="primary" @click="onSubmit">发布</el-button>
        <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
      </el-form-item>
    </el-form>
    <!-- 应用领域弹窗 -->
    <div>
      <el-dialog title="" :visible.sync="appShow" width="30%">
        <div>
          <div class="searchBoxApp">
            <el-input v-model="appinput" placeholder="请输入内容" style="width: 90%">
              <el-button slot="append" icon="el-icon-search" @click="searchApp()"></el-button>
            </el-input>
            <span class="cancelBtn" @click="cancelBtn()">取消</span>
          </div>

          <div style="min-height: 250px; overflow-y: scroll">
            <div v-for="(item, index) in demandappList" :key="index" class="selectBottom" @click="selectApp(item)">
              <i class="el-icon-search"></i>&nbsp;&nbsp;&nbsp;{{
                item.applicationFieldName
              }}
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
// import { product } from "ramda";
// import { get } from "sortablejs";
import { listData } from "@/api/system/dict/data";
import { applicationData, applicationAdd, supplyAdd } from "@/api/release";

export default {
  data() {
    return {
      imageUrlList: [],
      form: {
        title: "",
        type: "",
        description: "",
        technologyCategory: [],
        productCategory: [],
        // applicationArea: [],
        // imageUrl: [],
        cooperationType: "",
        process: "",
        // attachment: [],
        organization: "",
        contact: "",
        phone: "",
      },
      supplyTypeList: [],
      techTypeList: [],
      cooperationModeOptions: [],
      productStageList: [],
      // 表单校验
      rules: {
        title: [{ required: true, message: "请输入供给标题", trigger: "blur" }],
        type: [
          { required: true, message: "请选择供给类型", trigger: "change" },
        ],
        description: [
          { required: true, message: "请输入供给描述", trigger: "blur" },
        ],
        technologyCategory: [
          { required: true, message: "请选择服务类别", trigger: "change" },
        ],
        productCategory: [
          { required: true, message: "请选择产品类别", trigger: "change" },
        ],
        cooperationType: [
          { required: true, message: "请选择合作方式", trigger: "change" },
        ],
        process: [
          { required: true, message: "请选择产品阶段", trigger: "change" },
        ],
        // applicationArea: [
        //   { required: true, message: "请选择应用领域", trigger: "change" },
        // ],
        // displayRestrictions: [
        //   { required: true, message: "请选择展示限制", trigger: "change" },
        // ],
        // summary: [
        //   { required: true, message: "需求描述不能为空", trigger: "blur" },
        // ],
        organization: [
          { required: true, message: "请先绑定公司", trigger: "blur" },
        ],
        contact: [
          { required: true, message: "请先维护联系人", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请先维护联系电话", trigger: "blur" },
        ],
      },
      appShow: false,
      appinput: "",
      demandappList: [],
      demandapptagList: [],
      productTypeList: [],
    };
  },
  created() {
    let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
    this.form.organization = userInfo.memberCompanyName;
    this.form.contact = userInfo.memberRealName;
    this.form.phone = userInfo.memberPhone;
    this.getSupplyTypeDict();
    // this.getApplicationData();
    this.getTechTypeDict();
    this.getCooperationDict();
    this.getProductStageDict();
    this.getProductTypeDict();
  },
  methods: {
    changeType(data) {
      // console.log(data);
      // console.log(this.form.type);
    },
    // 供给类型字典
    getSupplyTypeDict() {
      let params = { dictType: "supply_type" };
      listData(params).then((response) => {
        this.supplyTypeList = response.rows;
      });
    },
    // 服务类别
    getTechTypeDict() {
      let params = { dictType: "technology_category" };
      listData(params).then((response) => {
        this.techTypeList = response.rows;
      });
    },
    // 产品类别
    getProductTypeDict() {
      let params = { dictType: "product_category" };
      listData(params).then((response) => {
        this.productTypeList = response.rows;
        console.log(this.productTypeList, '产品类别');
      });
    },
    // 合作方式
    getCooperationDict() {
      let params = { dictType: "supply_cooperation" };
      listData(params).then((response) => {
        this.cooperationModeOptions = response.rows;
      });
    },
    // 产品阶段
    getProductStageDict() {
      let params = { dictType: "supply_process" };
      listData(params).then((response) => {
        this.productStageList = response.rows;
      });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.technologyCategory = this.form.technologyCategory.join(",");
          this.form.productCategory = this.form.productCategory.join(",");
          this.form.imageUrl =
            this.imageUrlList && this.imageUrlList.length > 0
              ? this.imageUrlList[0].url
              : "";
          supplyAdd(this.form).then((res) => {
            if (res.code === 200) {
              // this.$router.push("/supplyDemandDocking?index=1");
              this.$router.go(-1);
              this.$message.success("发布成功");
            }
          });
        }
      });
    },
    onCancel() {
      this.$emit("cancel");
    },
    showDialog() {
      this.appShow = true;
      this.appinput = "";
    },
    getApplicationData() {
      let params = {
        applicationFieldName: this.appinput,
      };
      applicationData(params).then((res) => {
        if (res.code === 200) {
          this.demandappList = res.rows;
          let arrList = [];
          this.demandappList.forEach((item) => {
            arrList.push(item.applicationFieldName);
          });

          if (this.appinput && arrList.indexOf(this.appinput) === -1) {
            this.addAppli();
          }
        }
      });
    },
    //应用领域如果没有数据就调新增
    addAppli() {
      let data = {
        applicationFieldName: this.appinput,
      };
      applicationAdd(data).then((res) => {
        if (res.code === 200) {
          this.getApplicationData();
        }
      });
    },
    cancelBtn() {
      this.appinput = "";
      this.demandappList = [];
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>
