package com.ruoyi.biz.shop.biz.order;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.foxinmy.weixin4j.util.DateUtil;
import com.google.common.collect.Lists;
import com.ruoyi.biz.shop.biz.common.CommonBiz;
import com.ruoyi.biz.shop.biz.product.ClassifyBiz;
import com.ruoyi.biz.shop.service.enterprise.EnterpriseFeignService;
import com.ruoyi.biz.shop.service.enterprise.StoreFeignService;
import com.ruoyi.biz.shop.service.order.OrderFeignService;
import com.ruoyi.biz.shop.service.order.OrderItemsFeignService;
import com.ruoyi.biz.shop.service.order.PayRequestFeignService;
import com.ruoyi.biz.shop.service.pay.PayStrategyContext;
import com.ruoyi.biz.shop.service.pay.PayStrategyService;
import com.ruoyi.biz.shop.util.pay.wechat.util.WebUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.shop.entity.*;
import com.ruoyi.shop.enums.order.*;
import com.ruoyi.shop.result.CodeMsg;
import com.ruoyi.shop.result.Result;
import com.ruoyi.shop.utils.MdbUtils;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByIdRequest;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Array;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderBiz {

    @Value("${aliPay.product_code}")
    private String product_code;

    @Resource
    private RedisService redisService;

    @Resource
    private OrderItemsFeignService orderItemsFeignService;

    @Resource
    private PayRequestFeignService payRequestFeignService;

    @Resource
    private EnterpriseFeignService enterpriseFeignService;

    @Resource
    private CommonBiz commonBiz;

    @Resource
    private ClassifyBiz classifyBiz;

    @Resource
    private PayStrategyContext payStrategyContext;

    @Resource
    private StoreFeignService storeFeignService;

    @Resource
    private OrderFeignService orderFeignService;

//    @Resource
//    private RSAAutoCertificateConfig jcscWeChatPayConfig;
//
//    @Resource
//    private NativePayService jcscWechatPayNativePayService;

    @Value("${wx.pay.merchantId}")
    private String merchantId;

    @Value("${wx.pay.appid.pc}")
    private String appid;

    @Value("${wx.pay.merchantSerialNumber}")
    private String merchantSerialNumber;

    @Value("${wx.pay.notifyUrl}")
    private String notifyUrl;

    @Value("${wx.pay.refundNotifyUrl}")
    private String refundNotifyUrl;


    /**
     * 生成单号编码
     *
     * @return
     */
    public String getCode(String type) {
        String hKey = type + DateUtil.fortmat2yyyyMMdd(new Date());
        Long orderId = redisService.hIncrKy("ORDERSEQ", hKey, 1L);
        return hKey + String.format("%04d", orderId);
    }

    /**
     * 订单明细
     *
     * @param order
     * @param item
     * @return
     */
    public JSONObject getOrderDetail(Order order, Boolean item) {
        JSONObject retVal = new JSONObject();
        retVal.put("id", order.getId());
        retVal.put("order_no", order.getOrder_no());
        retVal.put("central_product", order.getCentral_product());
        retVal.put("create_time", cn.hutool.core.date.DateUtil.formatDate(order.getCreate_time()));
        retVal.put("payment", order.getPayment());
        retVal.put("payment_str", PaymentType.getPaymentType(order.getPayment()).getValue());
        retVal.put("order_type", order.getOrder_type());
        retVal.put("order_type_str", OrderType.getOrderType(order.getOrder_type()).getValue());
        retVal.put("status", order.getStatus());
        retVal.put("status_str", OrderStatus.getOrderStatus(order.getStatus()).getValue());
        retVal.put("ratio", "10%");
        retVal.put("central_status", order.getCentral_status());
        retVal.put("central_status_str", StringUtils.isNotBlank(order.getCentral_status()) ? CentralOrderStatus.getCentralOrderStatus(order.getCentral_status()).getValue() : "");
        retVal.put("central_pay_status", order.getCentral_pay_status());
        retVal.put("central_pay_status_str", StringUtils.isNotBlank(order.getCentral_status()) ? CentralPayStatus.getCentralPayStatus(order.getCentral_pay_status()).getValue() : "");
        retVal.put("create_by", order.getCreate_by());
        retVal.put("operator", order.getOperator());
        retVal.put("total_price", order.getTotal_price());
        retVal.put("deposit", order.getDeposit());
        retVal.put("linker", order.getLinker());
        retVal.put("linkphone", order.getLinkphone());
        retVal.put("logistics_no", StringUtils.trimToEmpty(order.getLogistics_no()));
        retVal.put("demand_name", StringEscapeUtils.unescapeHtml(order.getDemand_name()));
        retVal.put("demand_location", order.getDemand_location());//需方地址
        retVal.put("demand_proxy", order.getDemand_proxy());//需方委托代理人
        retVal.put("demand_phone", order.getDemand_phone());//需方手机
        retVal.put("contract_no", order.getContract_no());//合同编号
        retVal.put("supply_name", StringEscapeUtils.unescapeHtml(order.getSupply_name()));//供方
        retVal.put("supply_location", order.getSupply_location());//供方地址
        retVal.put("supply_proxy", order.getSupply_proxy());//供方委托人
        retVal.put("supply_phone", order.getSupply_phone());//供方电话
        retVal.put("terms", StringEscapeUtils.unescapeHtml(order.getTerms()));//订单条款
        retVal.put("remark", StringEscapeUtils.unescapeHtml(order.getRemark()));//备注
        retVal.put("address", StringUtils.trimToEmpty(StringEscapeUtils.unescapeHtml(order.getProvince() + order.getCity() + order.getRegion() + order.getAddress())));//收货地址
        if (Objects.equals(PaymentType.PERIOD.getValue(), order.getCentral_pay_status())) {
            PayRequest payRequest = new PayRequest();
            payRequest.setOrder_id(order.getId());
            List<PayRequest> data = payRequestFeignService.findList(payRequest, "period").getData();
            if (!CollectionUtils.isEmpty(data)) {
                retVal.put("period", data.get(0).getPeriod());//账期
            }
        }
        List<PayRequest> payRequests = payRequestFeignService.findList(PayRequest.builder().order_no(order.getOrder_no()).pay_status(PayStatus.UNPAY.getKey()).build(), "id,request_no,payment").getData();
        retVal.put("requestNo",payRequests!=null && payRequests.size()>0?payRequests.get(0).getRequest_no():"");
        retVal.put("requestId",payRequests!=null && payRequests.size()>0?payRequests.get(0).getId():"");
        retVal.put("requestPayment",payRequests!=null && payRequests.size()>0?payRequests.get(0).getPayment():"");
        payRequests = payRequestFeignService.findList(PayRequest.builder().order_no(order.getOrder_no()).pay_status(PayStatus.WAIT.getKey()).build(), "id,request_no,payment,id,pay_proof").getData();
        retVal.put("requestProof",payRequests!=null && payRequests.size()>0?payRequests.get(0).getPay_proof():"");
        retVal.put("crequestId",payRequests!=null && payRequests.size()>0?payRequests.get(0).getId():"");
        if (item) {
            Map<String, String> classifyMap = commonBiz.list2Map(classifyBiz.getClassifyList(), "id");
            List<OrderItems> orderItemsList = orderItemsFeignService.findList(OrderItems.builder().order_id(order.getId()).build(), null).getData();
            if (!CollectionUtils.isEmpty(orderItemsList)) {
                ArrayList<JSONObject> list = Lists.newArrayList();
                JSONObject model = null;
                for (OrderItems orderItems : orderItemsList) {
                    model = new JSONObject();
                    model.put("id", orderItems.getId());
                    model.put("order_id", orderItems.getOrder_id());
                    model.put("product_id", orderItems.getProduct_id());//商品id
                    model.put("classify_id", orderItems.getClassify_id());//分类id
                    model.put("classify2_id", orderItems.getClassify2_id());//分类2id
                    model.put("classify3_id", orderItems.getClassify3_id());//分类3id
                    model.put("classify_name", classifyMap.getOrDefault(orderItems.getClassify_id() + "", ""));
                    model.put("classify2_name", classifyMap.getOrDefault(orderItems.getClassify2_id() + "", ""));
                    model.put("classify3_name", classifyMap.getOrDefault(orderItems.getClassify3_id() + "", ""));
                    model.put("product_no", orderItems.getProduct_no());//商品编号
                    model.put("product_name", StringEscapeUtils.unescapeHtml(orderItems.getProduct_name()));//商品名称
                    model.put("specs", StringEscapeUtils.unescapeHtml(orderItems.getSpecs()));//型号规格
                    model.put("quantity", orderItems.getQuantity());//数量
                    model.put("shiped_quantity", orderItems.getShiped_quantity());//已发数量
                    model.put("pack", orderItems.getPack());
                    model.put("brand", orderItems.getBrand());
                    model.put("pur_attachment", StringUtils.isNotBlank(orderItems.getPur_attachment()) ? JSONArray.parseArray(orderItems.getPur_attachment(), JSONObject.class) : null);
                    model.put("sup_attachment", StringUtils.isNotBlank(orderItems.getSup_attachment()) ? JSONArray.parseArray(orderItems.getSup_attachment(), JSONObject.class) : null);
                    model.put("unit", StringEscapeUtils.unescapeHtml(orderItems.getUnit()));//单位
                    model.put("tax_rate", orderItems.getTax_rate());//税率
                    model.put("total_price", orderItems.getTotal_price());//含税总价
                    model.put("freight", orderItems.getFreight());//运费
                    model.put("tax_price", orderItems.getTax_price());//目标含税价格
                    model.put("total_amount", orderItems.getTotal_price().add(orderItems.getFreight()));//合计
                    model.put("delivery_date", StringUtils.trimToEmpty(orderItems.getDelivery_date()));//交货日期
                    model.put("delivered_date", StringUtils.trimToEmpty(orderItems.getDelivered_date()));//交货日期
                    list.add(model);
                }
                retVal.put("items", list);//备注
            }
        }
        return retVal;
    }


    /**
     * 发送线上订单支付
     * @return
     */
    public Result<JSONObject> payRequestInitial(PayRequest payRequest, Date deadline, List<PayRequest> list) throws Exception {
        JSONObject params = null;
        if (payRequest != null) {
            if (payRequest.getPurchase_id() == null || payRequest.getPurchase_id() < 0) {
                return Result.error(CodeMsg.PURCHASE_NOT_MDB);
            }
            if (PaymentType.getPaymentType(payRequest.getPayment()).getModule().startsWith(PaymentType.MDBONALL.getModule())) {
                Result<JSONObject> retVal = mdPayRequestInitial(payRequest, deadline);
                if(retVal.getData()!=null)
                    return retVal;
                else if(retVal.getCode()==CodeMsg.PURCHASE_NOT_MDB.getCode())
                    return Result.error(CodeMsg.PURCHASE_NOT_MDB);
                else if(retVal.getCode()==CodeMsg.SUPPLY_NOT_MDB.getCode())
                    return Result.error(CodeMsg.SUPPLY_NOT_MDB);
                else
                    return Result.error(CodeMsg.BIND_ERROR.fillArgs(retVal.getMsg()));
            }
        } else {
            if (PaymentType.getPaymentType(list.get(0).getPayment()).getModule().startsWith(PaymentType.ALIONALL.getModule())) {
                params = aliPayRequestInitial(list).getData();
            } else if (PaymentType.getPaymentType(list.get(0).getPayment()).getModule().startsWith(PaymentType.WXONALL.getModule())) {
                params = wxPayNativeRequestInitial(list).getData();
            }
        }
        return Result.success(params);
    }

    /**
     * 发送檬豆宝订单支付
     *
     * @return
     */
    public Result<JSONObject> mdPayRequestInitial(PayRequest payRequest, Date deadline) throws Exception {
        if (payRequest == null) {
            return null;
        }
        if (payRequest.getPurchase_id() == null || payRequest.getPurchase_id() < 0) {
            return Result.error(CodeMsg.PURCHASE_NOT_MDB);
        }
        Enterprise enterprise = enterpriseFeignService.findFieldById(payRequest.getPurchase_id(), "business_no").getData();
        String purchase = MdbUtils.getAccountNo(enterprise.getBusiness_no());
        if (StringUtils.isBlank(purchase)) {
            return Result.error(CodeMsg.PURCHASE_NOT_MDB);
        }
        if (payRequest.getSupply_id() == null || payRequest.getSupply_id() < 0) {
            return Result.error(CodeMsg.SUPPLY_NOT_MDB);
        }
        enterprise = enterpriseFeignService.findFieldById(payRequest.getSupply_id(), "business_no").getData();
        String supply = MdbUtils.getAccountNo(enterprise.getBusiness_no());
        if (StringUtils.isBlank(supply)) {
            return Result.error(CodeMsg.SUPPLY_NOT_MDB);
        }
        JSONObject account = MdbUtils.getAccountInfo(supply);
        if (account == null) {
            return Result.error(CodeMsg.SUPPLY_NOT_MDB);
        }
        JSONObject params = new JSONObject();
        JSONObject paymentApplyOrder = new JSONObject();
        paymentApplyOrder.put("applyAmount", payRequest.getTotal_amount() + "");
        paymentApplyOrder.put("department", "财务部");
        paymentApplyOrder.put("expireTime", deadline);
        paymentApplyOrder.put("receiveCompanyName", account.getString("name"));
        paymentApplyOrder.put("paymentMethod", 1);
        paymentApplyOrder.put("taxInclusive", 1);
        paymentApplyOrder.put("openBank", account.getString("bankBranchName"));
        paymentApplyOrder.put("receiveAccount", account.getString("bankAccount"));
        paymentApplyOrder.put("payReason", "付款申请单：" + payRequest.getRequest_no() + " 在线支付");
        paymentApplyOrder.put("remark", "信息来源-集采平台 备注-" + payRequest.getOrder_no());
        paymentApplyOrder.put("payType", payRequest.getPayment());
        params.put("phone", payRequest.getCreate_by());
        params.put("paymentApplyOrder", paymentApplyOrder);
        return Result.success(params);
    }

    /**
     * @Description 支付宝支付返回
     * <AUTHOR>
     * @Date 2022/8/26 0026 15:42
     */
    public Result<JSONObject> aliPayRequestInitial(List<PayRequest> list) throws Exception {
        JSONObject params = new JSONObject();
        if(list.size() > 0){
            JSONArray order_details =new JSONArray();
            for(PayRequest payRequest : list){
                Store store = storeFeignService.findList(Store.builder().enterprise_id(payRequest.getPurchase_id()).build(),null).getData().get(0);
                List<OrderItems> items = orderItemsFeignService.findList(OrderItems.builder().order_id(payRequest.getOrder_id()).build(),null).getData();
                JSONArray goods_detail =new JSONArray();
                JSONObject detail = new JSONObject();

                detail.put("app_id", store.getAli_mchid());
                detail.put("out_trade_no", payRequest.getRequest_no());
                 detail.put("product_code", product_code);
                detail.put("total_amount", payRequest.getPay_amount());
                detail.put("subject", "订单标题");

                for (OrderItems item : items) {
                    JSONObject goods = new JSONObject();
                    goods.put("goods_id", item.getProduct_id());
                    goods.put("goods_name", item.getProduct_name());
                    goods.put("quantity", item.getQuantity());
                    goods.put("price", item.getTax_price());
                    goods_detail.add(goods);
                }

                detail.put("goods_detail", goods_detail);
                order_details.add(detail);
            }
            params.put("order_details", order_details);
        }
        return Result.success(params);
    }


    /**
     * @Description 微信支付下单参数初始化 native支付
     * <AUTHOR>
     * @Date 2022/8/26 0026 15:42
     */
    public Result<JSONObject> wxPayRequestInitial(List<PayRequest> list) {
        JSONObject params = new JSONObject();
        // 合单商户订单号
        params.put("combine_out_trade_no", "CO" + DateUtil.fortmat2yyyyMMddHHmmss(new Date()) + WebUtil.generateRandomNumber(4));

        // 最大长度 10
        List<JSONObject> subOrdersList = new ArrayList<>();
        for (PayRequest payRequest : list) {
            Store store = storeFeignService.findById(payRequest.getSupply_id()).getData();
            JSONObject subOrders = new JSONObject();
            // 子单商户号
            subOrders.put("mchid", merchantId);
            subOrders.put("attach", ""); //附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用。

            JSONObject amount = new JSONObject();
            amount.put("total_amount", payRequest.getTotal_amount().multiply(new BigDecimal(100)).intValue());
            amount.put("currency", "CNY");
            subOrders.put("amount", amount);
            // 子单商户订单号
            subOrders.put("out_trade_no", payRequest.getRequest_no()); //子单商户订单号
            subOrders.put("description", "在线购买商品");
            subOrdersList.add(subOrders);
        }
        params.put("sub_orders", subOrdersList);
        // 通知地址
        params.put("notify_url", notifyUrl);
        return Result.success(params);
    }

    /**
     * @Description 微信支付下单参数初始化
     * <AUTHOR>
     * @Date 2022/8/26 0026 15:42
     */
    public Result<JSONObject> wxPayNativeRequestInitial(List<PayRequest> list) {
        JSONObject params = new JSONObject();

        BigDecimal amountBig = BigDecimal.ZERO; //总金额
        Set<String> attach_out_trade_no_set = list.stream().map(PayRequest::getRequest_no).collect(Collectors.toSet());
        String attach_out_trade_no = StringUtils.join(attach_out_trade_no_set, ",");

        String out_trade_no_new = "";
        if (list != null && !list.isEmpty()){
            out_trade_no_new = list.get(0).getRequest_no();
        }
        for (PayRequest payRequest : list) {
            amountBig = amountBig.add(payRequest.getTotal_amount());
        }

        params.put("appid",appid);
        params.put("mchid",merchantId);
        params.put("description","订单支付");
        params.put("out_trade_no",out_trade_no_new); //不能超过32个字符
        params.put("notify_url",notifyUrl);
        params.put("attach",attach_out_trade_no); // 这个可以传128个字符，用这个参数传申请单编号（生成多个付款申请单的情况下，用于回调多个付款申请单）

        JSONObject amount = new JSONObject();
        amount.put("total",amountBig.multiply(new BigDecimal(100)).intValue());
        params.put("amount",amount);

        return Result.success(params);
    }

    /**
     * 发送退款返回
     *
     * @return
     */
    public Result<JSONObject> payRefundInitial(PayRequest payRequest) throws Exception {
        JSONObject params = new JSONObject();
        if (payRequest.getPayment().startsWith(PaymentType.MDBONALL.getKey())) {
            params = mdPayRefundInitial(payRequest).getData();
        } else if (payRequest.getPayment().startsWith(PaymentType.ALIONALL.getKey())) {
            params = aliPayRefundInitial(payRequest).getData();
        } else if (payRequest.getPayment().startsWith(PaymentType.WXONALL.getKey())) {
            params = wxPayRefundInitial(payRequest).getData();
        }
        return Result.success(params);
    }

    /**
     * @Description 檬豆退款返回
     * <AUTHOR>
     * @Date 2022/8/28 0028 18:44
     */
    public Result<JSONObject> mdPayRefundInitial(PayRequest payRequest) throws Exception {
        Enterprise enterprise = enterpriseFeignService.findFieldById(payRequest.getPurchase_id(), "business_no").getData();
        String supply = MdbUtils.getAccountNo(enterprise.getBusiness_no());
        JSONObject account = MdbUtils.getAccountInfo(supply);
        JSONObject params = new JSONObject();
        JSONObject paymentApplyOrder = new JSONObject();
        paymentApplyOrder.put("applyAmount", payRequest.getTotal_amount() + "");
        paymentApplyOrder.put("department", "财务部");
        paymentApplyOrder.put("receiveCompanyName", account.getString("name"));
        paymentApplyOrder.put("paymentMethod", 1);
        paymentApplyOrder.put("taxInclusive", 1);
        paymentApplyOrder.put("openBank", account.getString("bankBranchName"));
        paymentApplyOrder.put("receiveAccount", account.getString("bankAccount"));
        paymentApplyOrder.put("payReason", "付款申请单：" + payRequest.getRequest_no() + " 在线退款");
        paymentApplyOrder.put("remark", "信息来源-集采平台 备注-" + payRequest.getOrder_no());
        paymentApplyOrder.put("callBack", "https://chain.ctaste.cn/shop/data/order/callback/pay");
        params.put("phone", payRequest.getUpdate_by());
        params.put("paymentApplyOrder", paymentApplyOrder);
        return Result.success(params);
    }


    /**
     * @Description 支付宝退款返回
     * <AUTHOR>
     * @Date 2022/8/28 0028 18:44
     */
    public Result<JSONObject> aliPayRefundInitial(PayRequest payRequest) throws Exception {
//        Store store = storeFeignService.findById(payRequest.getPurchase_id()).getData();
//        Order order = orderFeignService.findById(payRequest.getOrder_id()).getData();
        JSONObject params = new JSONObject();
        JSONArray refund_royalty_parameters = new JSONArray();
        params.put("out_trade_no", payRequest.getRequest_no());
        params.put("trade_no", payRequest.getPayno());
        params.put("refund_amount", payRequest.getPay_amount());
        return Result.success(params);
    }

    /**
     * @Description 微信退款返回
     * <AUTHOR>
     * @Date 2022/8/28 0028 18:44
     */
    public Result<JSONObject> wxPayRefundInitial(PayRequest payRequest) throws Exception {
        JSONObject params = new JSONObject();
        params.put("transaction_id", payRequest.getOrder_no());
        params.put("out_refund_no", payRequest.getRefundno());
        JSONObject amount = new JSONObject();
        amount.put("refund", payRequest.getPay_amount().multiply(new BigDecimal("100")).toBigInteger());
        amount.put("total", payRequest.getTotal_amount().multiply(new BigDecimal("100")).toBigInteger());
        amount.put("currency", "CNY");
        params.put("amount", amount);
        params.put("notify_url", refundNotifyUrl);

        return Result.success(params);
    }


    /**
     * 发送支付宝订单支付
     *
     * @return
     */
    public Result<JSONObject> aliPayRequestSend(JSONObject payRequest) throws Exception {
        PayStrategyService payServiceImpl = payStrategyContext.getResource("aliPay");
        JSONObject retVal = payServiceImpl.preCreatePay(payRequest);
        return Result.success(retVal);
    }


    /**
     * 发送支付宝订单退款
     *
     * @return
     */
    public Result<JSONObject> aliPayRefundSend(JSONObject payRequest) throws Exception {
        PayStrategyService payServiceImpl = payStrategyContext.getResource("aliPay");
        JSONObject retVal = payServiceImpl.refundPay(payRequest);
        return Result.success(retVal);
    }

    /**
     * 发送檬豆宝订单支付
     *
     * @return
     */
    public Result<String> payRequestSend(JSONObject payRequest) throws Exception {
        JSONObject retVal = MdbUtils.sendOrderReq(payRequest);
        if (retVal == null) {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("发起支付失败"));
        }
        return Result.success(retVal.getString("systemNo"));
    }

    /**
     * 发送微信订单支付
     *
     * @return
     */
    public Result<JSONObject> wxPayRequestSend(JSONObject payRequest, String type, String openid) throws Exception {
        PayStrategyService payServiceImpl = payStrategyContext.getResource("wxPay");
        JSONObject retVal = payServiceImpl.preCreatePayWx(payRequest, type, openid);
        return Result.success(retVal);
    }


    /**
     * 发送微信订单退款
     *
     * @return
     */
    public Result<JSONObject> wxPayRefundSend(JSONObject payRequest) throws Exception {
        PayStrategyService payServiceImpl = payStrategyContext.getResource("wxPay");
        JSONObject retVal = payServiceImpl.refundPay(payRequest);
        return Result.success(retVal);
    }


//    /**
//     * 支付成功后的回调
//     * @param request
//     */
//    public Boolean paySuccessNotify(HttpServletRequest request){
//
//        RequestParam requestParam = this.handleNotifyRequest(request);
//        NotificationParser parser = new NotificationParser(jcscWeChatPayConfig);
//        Transaction transaction = parser.parse(requestParam, Transaction.class);
//        log.info("paySuccessNotify:decryptObject---"+transaction);
//
//        /*
//        15:33:23.481 [http-nio-9204-exec-5] INFO  c.r.b.s.b.w.WeChatPayBiz - [paySuccessNotify,83] -
//        paySuccessNotify:decryptObject---
//
//        {
//        "amount":
//            {"currency":"CNY",
//            "payer_currency":"CNY",
//            "payer_total":1,
//            "total":1},
//            "appid":"wxe411837960491937",
//            "attach":"",
//            "bank_type":"OTHERS",
//            "mchid":"**********",
//            "out_trade_no":"liuchaotest20230207004",
//            "payer":{"openid":"o6xtu5HqDWE-tGpkWNylT2kMRX-I"},
//            "success_time":"2023-02-07T15:33:22+08:00",
//            "trade_state":"SUCCESS",
//            "trade_state_desc":"支付成功",
//            "trade_type":"NATIVE",
//            "transaction_id":"4200001696202302070452920081"}
//
//        * */
//
//        if (null == transaction){
//            log.error("weChatPayError:微信支付回调 oneProductPaySuccessNotify 失败:返回的 transaction 为空");
//            return false;
//        }
//
//        //不管回调的状态是成功还是失败，都主动查询状态，以主动查的为准
//        Transaction transactionNew = queryOrderByTransactionId(transaction.getTransactionId());
//        log.info("主动查询---->",transactionNew);
//        if (!transactionNew.getTradeState().equals(Transaction.TradeStateEnum.SUCCESS)){
//            //如果主动查询状态还是失败，则不做处理
//            log.error("weChatPayError:微信支付回调 oneProductPaySuccessNotify,主动查询支付状态为失败");
//            log.error("查询的返回值为:"+transactionNew);
//            return false;
//        }
//
//        //先检查对应业务数据的状态，并判断该通知是否已经处理
//        //如果未处理，则再进行处理
//        //在对业务数据进行状态检查和处理之前，要采用数据锁进行并发控制，以避免函数重入造成的数据混乱。？？
//        //如果在所有通知频率后没有收到微信侧回调，商户应调用查询订单接口确认订单状态。
//        //商户系统对于开启结果通知的内容一定要做签名验证，并校验通知的信息是否与商户侧的信息一致，防止数据泄露导致出现“假通知”，造成资金损失。
//        return successPayHandle(transaction.getAttach());
//    }

    public Boolean successPayHandle(String attach){
        List<String> outTradeNos = Arrays.stream(StringUtils.split(attach, ",")).collect(Collectors.toList());
        List<PayRequest> list = new ArrayList<>();
        for (String tradeNo : outTradeNos) {
            List<PayRequest> data = payRequestFeignService.findByNos(tradeNo, null).getData();
            list.addAll(data);
        }

        for (PayRequest payRequestData : list){
            Order orderParam = new Order();
            orderParam.setOrder_no(payRequestData.getOrder_no());
            List<Order> orderList = orderFeignService.findList(orderParam, null).getData();
            for (Order orderData : orderList){
                //状态，NEW-审核中，
                // CONFIRM-待（供应商）确认，
                // WAITPAY待付款，PAYCONFIRM付款(供应商)待确认
                // ，WAITSHIP待发货，PARTSHIP部分发货，DONE已完成

                //payRequestService 付款申请单状态也要变更
                if (OrderStatus.WAITPAY.getKey().equals(orderData.getStatus())
                        || OrderStatus.PAYCONFIRM.getKey().equals(orderData.getStatus())
                        || OrderStatus.CONFIRM.getKey().equals(orderData.getStatus())
                ){
                    //如果原先是待付款或付款待确认，变更状态为待发货
                    orderData.setStatus(OrderStatus.WAITSHIP.getKey());
                    orderFeignService.update(orderData);
                }
            }

            if (null != payRequestData
                    && (payRequestData.getPay_status().equals(PayStatus.UNPAY.getKey())
                    || payRequestData.getPay_status().equals(PayStatus.CONFIRM.getKey()))){
                payRequestData.setPay_status(PayStatus.PAYED.getKey());
                payRequestData.setUpdate_time(new Date());
                payRequestData.setUpdate_by("wxCallBack");
                payRequestFeignService.update(payRequestData,payRequestData.getId());
            }
        }
        return true;
    }
//
//    public com.wechat.pay.java.service.payments.model.Transaction queryOrderByOutTradeNo(String outTradeNo){
//        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
//        request.setMchid(merchantId);
//        request.setOutTradeNo(outTradeNo);
////        NativePayService weChatPayNativePayService = new NativePayService.Builder().config(jcscWeChatPayConfig).build();
//        com.wechat.pay.java.service.payments.model.Transaction transaction = jcscWechatPayNativePayService.queryOrderByOutTradeNo(request);
//        return transaction;
//    }

//    public com.wechat.pay.java.service.payments.model.Transaction queryOrderByTransactionId(String transaction_id){
//        QueryOrderByIdRequest request = new QueryOrderByIdRequest();
//        request.setMchid(merchantId);
//        request.setTransactionId(transaction_id);
////        NativePayService weChatPayNativePayService = new NativePayService.Builder().config(jcscWeChatPayConfig).build();
//        com.wechat.pay.java.service.payments.model.Transaction transaction = jcscWechatPayNativePayService.queryOrderById(request);
//        return transaction;
//    }

    public RequestParam handleNotifyRequest(HttpServletRequest request){
        String signature = request.getHeader("Wechatpay-Signature");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String serial = request.getHeader("Wechatpay-Serial");
        String signatureType = request.getHeader("Wechatpay-Signature-Type");

        BufferedReader br = null;
        String wholeStr = "";
        try {
            br = request.getReader();
            String str = "";
            while((str = br.readLine()) != null) {
                wholeStr += str;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new RequestParam.Builder()
                .serialNumber(serial)
                .nonce(nonce)
                .signature(signature)
                .signType(signatureType)
                .timestamp(timestamp)
                .body(wholeStr)
                .build();
    }
}
