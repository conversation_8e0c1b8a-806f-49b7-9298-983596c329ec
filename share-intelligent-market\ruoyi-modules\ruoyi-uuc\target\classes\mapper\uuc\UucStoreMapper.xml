<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucStoreMapper">

    <resultMap type="com.ruoyi.uuc.ningmengdou.domain.UucStore" id="UucStoreResult">
        <result property="id"    column="id"    />
        <result property="appCode"    column="app_code"    />
        <result property="appName"    column="app_name"    />
        <result property="appLogo"    column="app_logo"    />
        <result property="innerImg"    column="inner_img"    />
        <result property="briefInto"    column="brief_into"    />
        <result property="appLabel"    column="app_label"    />
        <result property="content"    column="content"    />
        <result property="appCategory"    column="app_category"    />
        <result property="erweima"    column="erweima"    />
        <result property="recommend"    column="recommend"    />
        <result property="sort"    column="sort"    />
        <result property="supply"    column="supply"    />
        <result property="linkman"    column="linkman"    />
        <result property="phone"    column="phone"    />
        <result property="price"    column="price"    />
        <result property="sub"    column="sub"    />
        <result property="issub"    column="issub"    />
        <result property="unit"    column="unit"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucStoreVo">
        select id, app_code, app_name, app_logo, inner_img, brief_into, app_label, content, app_category, erweima, recommend, sort, supply, linkman, phone, price, sub, issub, unit, create_by, create_time, update_by, update_time from uuc_store
    </sql>

    <select id="selectUucStoreList" parameterType="com.ruoyi.uuc.ningmengdou.domain.UucStore" resultMap="UucStoreResult">
        <include refid="selectUucStoreVo"/>
        <where>
            <if test="appCode != null  and appCode != ''"> and app_code like concat('%', #{appCode}, '%')</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="appCategory != null  and appCategory != ''"> and app_category = #{appCategory}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppStoreList" parameterType="com.ruoyi.uuc.ningmengdou.domain.UucStore" resultMap="UucStoreResult">
        <include refid="selectUucStoreVo"/>
        <where>
            <if test="searchValue != null  and searchValue != ''"> and app_name like concat('%', #{searchValue}, '%')</if>
            <if test="appCategory != null  and appCategory != ''"> and app_category = #{appCategory}</if>
        </where>
        order by sort desc
    </select>

    <select id="selectUucStoreById" parameterType="Long" resultMap="UucStoreResult">
        <include refid="selectUucStoreVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppStoreById" parameterType="Long" resultMap="UucStoreResult">
        <include refid="selectUucStoreVo"/>
        where id = #{id}
    </select>

    <insert id="insertUucStore" parameterType="com.ruoyi.uuc.ningmengdou.domain.UucStore" useGeneratedKeys="true" keyProperty="id">
        insert into uuc_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appCode != null and appCode != ''">app_code,</if>
            <if test="appName != null and appName != ''">app_name,</if>
            <if test="appLogo != null and appLogo != ''">app_logo,</if>
            <if test="innerImg != null and innerImg != ''">inner_img ,</if>
            <if test="briefInto != null and briefInto != ''">brief_into,</if>
            <if test="appLabel != null">app_label,</if>
            <if test="content != null">content,</if>
            <if test="appCategory != null">app_category,</if>
            <if test="erweima != null">erweima,</if>
            <if test="recommend != null">recommend,</if>
            <if test="sort != null">sort,</if>
            <if test="supply != null">supply,</if>
            <if test="linkman != null">linkman,</if>
            <if test="phone != null">phone,</if>
            <if test="price != null">price,</if>
            <if test="sub != null">sub,</if>
            <if test="issub != null">issub,</if>
            <if test="unit != null">unit,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appCode != null and appCode != ''">#{appCode},</if>
            <if test="appName != null and appName != ''">#{appName},</if>
            <if test="appLogo != null and appLogo != ''">#{appLogo},</if>
            <if test="innerImg != null and innerImg != ''"> #{innerImg},</if>
            <if test="briefInto != null and briefInto != ''">#{briefInto},</if>
            <if test="appLabel != null">#{appLabel},</if>
            <if test="content != null">#{content},</if>
            <if test="appCategory != null">#{appCategory},</if>
            <if test="erweima != null">#{erweima},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="sort != null">#{sort},</if>
            <if test="supply != null">#{supply},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="phone != null">#{phone},</if>
            <if test="price != null">#{price},</if>
            <if test="sub != null">#{sub},</if>
            <if test="issub != null">#{issub},</if>
            <if test="unit != null">#{unit},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUucStore" parameterType="com.ruoyi.uuc.ningmengdou.domain.UucStore">
        update uuc_store
        <trim prefix="SET" suffixOverrides=",">
            <if test="appCode != null and appCode != ''">app_code = #{appCode},</if>
            <if test="appName != null and appName != ''">app_name = #{appName},</if>
            <if test="appLogo != null and appLogo != ''">app_logo = #{appLogo},</if>
            <if test="innerImg != null and innerImg != ''">inner_img = #{innerImg},</if>
            <if test="briefInto != null and briefInto != ''">brief_into = #{briefInto},</if>
            <if test="appLabel != null">app_label = #{appLabel},</if>
            <if test="content != null">content = #{content},</if>
            <if test="appCategory != null">app_category = #{appCategory},</if>
            <if test="erweima != null">erweima = #{erweima},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="supply != null">supply = #{supply},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="price != null">price = #{price},</if>
            <if test="sub != null">sub = #{sub},</if>
            <if test="issub != null">issub = #{issub},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucStoreById" parameterType="Long">
        delete from uuc_store where id = #{id}
    </delete>

    <delete id="deleteUucStoreByIds" parameterType="String">
        delete from uuc_store where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
