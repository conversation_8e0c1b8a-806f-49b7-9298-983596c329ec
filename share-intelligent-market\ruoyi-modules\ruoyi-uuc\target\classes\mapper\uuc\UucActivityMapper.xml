<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucActivityMapper">
    
    <resultMap type="UucActivity" id="UucActivityResult">
        <result property="id"    column="id"    />
        <result property="activityNo"    column="activity_no"    />
        <result property="image"    column="image"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="remark"    column="remark"    />
        <result property="link"    column="link"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="isHot"    column="is_hot"    />
        <result property="status"    column="status"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucActivityVo">
        select id, activity_no, image, title, category, remark, link, start_date, end_date, is_hot, status, content, create_by, create_time, update_by, update_time from uuc_activity
    </sql>

    <select id="selectUucActivityList" parameterType="UucActivity" resultMap="UucActivityResult">
        <include refid="selectUucActivityVo"/>
        <where>  
            <if test="activityNo != null  and activityNo != ''"> and activity_no like concat('%', #{activityNo}, '%')</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppActivityList" parameterType="UucActivity" resultMap="UucActivityResult">
        <include refid="selectUucActivityVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
             and status = 1
        </where>
        order by id desc
    </select>

    <select id="selectUucActivityById" parameterType="Long" resultMap="UucActivityResult">
        <include refid="selectUucActivityVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppActivityById" parameterType="Long" resultMap="UucActivityResult">
        <include refid="selectUucActivityVo"/>
        where id = #{id} and status = 1
    </select>

    <insert id="insertUucActivity" parameterType="UucActivity">
        insert into uuc_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="activityNo != null">activity_no,</if>
            <if test="image != null and image != ''">image,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="category != null">category,</if>
            <if test="remark != null">remark,</if>
            <if test="link != null">link,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="status != null">status,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="activityNo != null">#{activityNo},</if>
            <if test="image != null and image != ''">#{image},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="remark != null">#{remark},</if>
            <if test="link != null">#{link},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="status != null">#{status},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucActivity" parameterType="UucActivity">
        update uuc_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityNo != null">activity_no = #{activityNo},</if>
            <if test="image != null and image != ''">image = #{image},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="link != null">link = #{link},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="status != null">status = #{status},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucActivityById" parameterType="Long">
        delete from uuc_activity where id = #{id}
    </delete>

    <delete id="deleteUucActivityByIds" parameterType="String">
        delete from uuc_activity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>