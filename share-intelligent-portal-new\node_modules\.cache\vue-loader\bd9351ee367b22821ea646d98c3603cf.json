{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue", "mtime": 1750385853720}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["processRequire.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "processRequire.vue", "sourceRoot": "src/views/release/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"外协工序名称\" prop=\"processName\">\r\n        <el-input v-model=\"form.processName\" maxlength=\"50\" show-word-limit placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"加工数量\" prop=\"processingQuantity\">\r\n        <el-input type=\"number\" min=\"0\" v-model=\"form.processingQuantity\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"外协加工内容\" prop=\"outsourcingContent\">\r\n        <el-input v-model=\"form.outsourcingContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n          show-word-limit placeholder=\"请输入\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"要求完成时间\" prop=\"requiredCompletionTime\">\r\n        <el-date-picker v-model=\"form.requiredCompletionTime\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\"\r\n          style=\"width: 100%\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"订单号\" prop=\"projectNumber\">\r\n        <el-input v-model=\"form.projectNumber\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"备注\" prop=\"remarks\">\r\n        <el-input v-model=\"form.remarks\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input disabled v-model=\"form.companyName\" placeholder=\"请先绑定公司\"></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input disabled v-model=\"form.contactPerson\" placeholder=\"请先维护联系人\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input disabled v-model=\"form.contactPhone\" placeholder=\"请先维护联系方式\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseProcess } from \"@/api/release\"\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        processName: \"\",\r\n        processingQuantity: \"\",\r\n        outsourcingContent: \"\",\r\n        requiredCompletionTime: \"\",\r\n        projectNumber: \"\",\r\n        remarks: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        processName: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        processingQuantity: [\r\n          { required: true, message: \"加工数量不能为空\", trigger: \"blur\" },\r\n        ],\r\n        outsourcingContent: [\r\n          { required: true, message: \"外协加工内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        requiredCompletionTime: [\r\n          { required: true, message: \"要求完成时间不能为空\", trigger: \"blur\" },\r\n        ],\r\n        projectNumber: [\r\n          { required: true, message: \"订单号不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if(userinfo && userinfo != 'null') {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contactPerson = userinfo.memberRealName;\r\n      this.form.contactPhone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit(status) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n\r\n          releaseProcess(this.form).then(res => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel()\r\n            } else {\r\n              this.$message.error('发布失败')\r\n            }\r\n          })\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}