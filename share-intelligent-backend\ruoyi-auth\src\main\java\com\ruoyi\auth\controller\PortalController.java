package com.ruoyi.auth.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.PortalLoginService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.ProtalTokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class PortalController
{
    @Autowired
    private ProtalTokenService protalTokenService;

    @Autowired
    private PortalLoginService portalLoginService;


    @PostMapping("portallogin")
    public R<?> login(@RequestBody LoginBody form)
    {
        LoginMember memberInfo= null;
        // 用户登录
        try{
            memberInfo = portalLoginService.login(form.getUsername(), form.getPassword(),form.getSmsCode());
        }catch(Exception e){
            if (e instanceof ServiceException){
                HashMap<String, Object> map = new HashMap<String, Object>();
                map.put("msg", e.getMessage());
                map.put("success", false);
                return R.ok(map);
            }else{
                throw e;
            }
        }

        // 获取登录token
        return R.ok(protalTokenService.createToken(memberInfo));
    }

    @PostMapping("loginBySmsCode")
    public R<?> loginBySmsCode(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginMember memberInfo = portalLoginService.loginByCode(form.getUsername(),form.getSmsCode());
        Map<String, Object> token = protalTokenService.createToken(memberInfo);        // 检查下是否有设置密码, 否则跳转页面, 设置密码
        if (StringUtils.isNull(memberInfo.getMember().getMemberPassword()) || StringUtils.isEmpty(memberInfo.getMember().getMemberPassword())){
            // 需要初始化密码
              token.put("initPassword", true);
        }

        // 获取登录token
        return R.ok(token);
    }

    @DeleteMapping("portallogout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            try {
                String username = JwtUtils.getUserName(token);
                // 删除用户缓存记录
                AuthUtil.logoutByToken(token);
                // 记录用户退出日志
                portalLoginService.logout(username);
            } catch (Exception e) {
                // Token格式错误或已过期，直接返回成功（因为用户已经无法使用该token了）
                // 记录日志但不抛出异常
                System.out.println("Token解析失败，可能已过期或格式错误: " + e.getMessage());
            }
        }
        return R.ok();
    }

    @PostMapping("portalrefresh")
    public R<?> refresh(HttpServletRequest request)
    {
    	LoginMember loginMember = protalTokenService.getLoginMember(request);
        if (StringUtils.isNotNull(loginMember))
        {
            // 刷新令牌有效期
        	protalTokenService.refreshToken(loginMember);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("portalregister")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
    	portalLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

    // 重置密码
    @PostMapping("portalpassword")
    public R<?> resetPassword(@RequestBody LoginBody loginBody){
        // 用户登录
        LoginMember memberInfo = portalLoginService.loginByCode(loginBody.getUsername(),loginBody.getSmsCode());
        // 重置密码
         portalLoginService.resetPassword(memberInfo.getMember().getMemberPhone(), loginBody.getPassword());
        // 获取登录token
        return R.ok(protalTokenService.createToken(memberInfo));
    }


}
