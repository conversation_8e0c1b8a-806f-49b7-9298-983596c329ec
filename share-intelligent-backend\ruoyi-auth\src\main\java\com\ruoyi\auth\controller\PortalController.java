package com.ruoyi.auth.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.PortalLoginService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.ProtalTokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class PortalController
{
    @Autowired
    private ProtalTokenService protalTokenService;

    @Autowired
    private PortalLoginService portalLoginService;


    @PostMapping("portallogin")
    public R<?> login(@RequestBody LoginBody form)
    {
        System.out.println("=== portallogin 接口被调用 ===");
        System.out.println("用户名: " + form.getUsername());
        System.out.println("密码: " + (form.getPassword() != null ? "已提供" : "未提供"));
        System.out.println("验证码: " + form.getSmsCode());

        LoginMember memberInfo= null;
        // 用户登录
        try{
            memberInfo = portalLoginService.login(form.getUsername(), form.getPassword(),form.getSmsCode());
            System.out.println("登录成功，用户信息: " + (memberInfo != null ? memberInfo.getMember().getMemberPhone() : "null"));
        }catch(Exception e){
            System.out.println("登录失败: " + e.getMessage());
            if (e instanceof ServiceException){
                HashMap<String, Object> map = new HashMap<String, Object>();
                map.put("msg", e.getMessage());
                map.put("success", false);
                return R.ok(map);
            }else{
                throw e;
            }
        }

        // 获取登录token
        Map<String, Object> tokenMap = protalTokenService.createToken(memberInfo);
        System.out.println("生成的token信息: " + tokenMap);
        return R.ok(tokenMap);
    }

    @PostMapping("loginBySmsCode")
    public R<?> loginBySmsCode(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginMember memberInfo = portalLoginService.loginByCode(form.getUsername(),form.getSmsCode());
        Map<String, Object> token = protalTokenService.createToken(memberInfo);        // 检查下是否有设置密码, 否则跳转页面, 设置密码
        if (StringUtils.isNull(memberInfo.getMember().getMemberPassword()) || StringUtils.isEmpty(memberInfo.getMember().getMemberPassword())){
            // 需要初始化密码
              token.put("initPassword", true);
        }

        // 获取登录token
        return R.ok(token);
    }

    @DeleteMapping("portallogout")
    public R<?> logout(HttpServletRequest request)
    {
        try {
            String token = SecurityUtils.getToken(request);
            System.out.println("退出登录 - 获取到的token: " + (token != null ? "存在" : "不存在"));

            if (StringUtils.isNotEmpty(token))
            {
                // 检查token格式是否正确（JWT应该包含两个点）
                if (token.split("\\.").length != 3) {
                    System.out.println("Token格式不正确，不是有效的JWT格式: " + token);
                    return R.ok("退出成功");
                }

                try {
                    String username = JwtUtils.getUserName(token);
                    System.out.println("退出登录 - 用户名: " + username);

                    // 删除用户缓存记录
                    AuthUtil.logoutByToken(token);
                    // 记录用户退出日志
                    portalLoginService.logout(username);

                    System.out.println("用户 " + username + " 退出登录成功");
                } catch (Exception e) {
                    // Token格式错误或已过期，直接返回成功（因为用户已经无法使用该token了）
                    System.out.println("Token解析失败，可能已过期或格式错误: " + e.getMessage());
                }
            } else {
                System.out.println("未找到token，可能用户已经退出登录");
            }

            return R.ok("退出成功");
        } catch (Exception e) {
            // 捕获所有异常，确保退出登录总是返回成功
            System.out.println("退出登录过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            return R.ok("退出成功");
        }
    }

    @PostMapping("portalrefresh")
    public R<?> refresh(HttpServletRequest request)
    {
    	LoginMember loginMember = protalTokenService.getLoginMember(request);
        if (StringUtils.isNotNull(loginMember))
        {
            // 刷新令牌有效期
        	protalTokenService.refreshToken(loginMember);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("portalregister")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
    	portalLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

    // 重置密码
    @PostMapping("portalpassword")
    public R<?> resetPassword(@RequestBody LoginBody loginBody){
        // 用户登录
        LoginMember memberInfo = portalLoginService.loginByCode(loginBody.getUsername(),loginBody.getSmsCode());
        // 重置密码
         portalLoginService.resetPassword(memberInfo.getMember().getMemberPhone(), loginBody.getPassword());
        // 获取登录token
        return R.ok(protalTokenService.createToken(memberInfo));
    }


}
