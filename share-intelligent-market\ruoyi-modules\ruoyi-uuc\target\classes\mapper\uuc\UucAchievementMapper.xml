<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucAchievementMapper">

    <resultMap type="UucAchievement" id="UucAchievementResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="detail"    column="detail"    />
        <result property="application"    column="application"    />
        <result property="step"    column="step"    />
        <result property="cooperation"    column="cooperation"    />
        <result property="remark"    column="remark"    />
        <result property="compnay"    column="compnay"    />
        <result property="linkName"    column="link_name"    />
        <result property="linkTel"    column="link_tel"    />
        <result property="status"    column="status"    />
        <result property="pictures"    column="pictures"    />
        <result property="attachments"    column="attachments"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucAchievementVo">
        select id, title, detail, application, step, cooperation, remark, compnay, link_name, link_tel, status, pictures, attachments, create_by, create_time, update_by, update_time from uuc_achievement
    </sql>

    <select id="selectUucAchievementList" parameterType="UucAchievement" resultMap="UucAchievementResult">
        <include refid="selectUucAchievementVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="application != null  and application != '' and application != 0"> and application = #{application}</if>
            <if test="step != null  and step != '' and step != 0"> and step = #{step}</if>
            <if test="cooperation != null  and cooperation != '' and cooperation != 0"> and cooperation = #{cooperation}</if>
            <if test="linkName != null  and linkName != ''"> and link_name like concat('%', #{linkName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by id desc
    </select>

    <select id="selectUucAppAchievementList" parameterType="UucAchievement" resultMap="UucAchievementResult">
        <include refid="selectUucAchievementVo"/>
        <where>
            <if test="searchValue != null  and searchValue != ''"> and (title like concat('%', #{searchValue}, '%') or detail like concat('%', #{searchValue}, '%'))</if>
            <if test="application != null  and application != '' and application != 0"> and application = #{application}</if>
            <if test="step != null  and step != '' and step != 0"> and step = #{step}</if>
            <if test="cooperation != null  and cooperation != '' and cooperation != 0"> and cooperation = #{cooperation}</if>
             and status = 1
        </where>
        order by id desc
    </select>

    <select id="selectUucAchievementById" parameterType="Long" resultMap="UucAchievementResult">
        <include refid="selectUucAchievementVo"/>
        where id = #{id}
    </select>

    <select id="selectUucAppAchievementById" parameterType="Long" resultMap="UucAchievementResult">
        <include refid="selectUucAchievementVo"/>
        where id = #{id} and status = 1
    </select>

    <insert id="insertUucAchievement" parameterType="UucAchievement">
        insert into uuc_achievement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="detail != null">detail,</if>
            <if test="application != null and application != ''">application,</if>
            <if test="step != null and step != ''">step,</if>
            <if test="cooperation != null and cooperation != ''">cooperation,</if>
            <if test="remark != null">remark,</if>
            <if test="compnay != null">compnay,</if>
            <if test="linkName != null">link_name,</if>
            <if test="linkTel != null">link_tel,</if>
            <if test="status != null">status,</if>
            <if test="pictures != null">pictures,</if>
            <if test="attachments != null">attachments,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="detail != null">#{detail},</if>
            <if test="application != null and application != ''">#{application},</if>
            <if test="step != null and step != ''">#{step},</if>
            <if test="cooperation != null and cooperation != ''">#{cooperation},</if>
            <if test="remark != null">#{remark},</if>
            <if test="compnay != null">#{compnay},</if>
            <if test="linkName != null">#{linkName},</if>
            <if test="linkTel != null">#{linkTel},</if>
            <if test="status != null">#{status},</if>
            <if test="pictures != null">#{pictures},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertUucAppAchievement" parameterType="UucAchievement">
        insert into uuc_achievement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="detail != null">detail,</if>
            <if test="application != null and application != ''">application,</if>
            <if test="step != null and step != ''">step,</if>
            <if test="cooperation != null and cooperation != ''">cooperation,</if>
            <if test="remark != null">remark,</if>
            <if test="compnay != null">compnay,</if>
            <if test="linkName != null">link_name,</if>
            <if test="linkTel != null">link_tel,</if>
            status,
            <if test="pictures != null">pictures,</if>
            <if test="attachments != null">attachments,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="detail != null">#{detail},</if>
            <if test="application != null and application != ''">#{application},</if>
            <if test="step != null and step != ''">#{step},</if>
            <if test="cooperation != null and cooperation != ''">#{cooperation},</if>
            <if test="remark != null">#{remark},</if>
            <if test="compnay != null">#{compnay},</if>
            <if test="linkName != null">#{linkName},</if>
            <if test="linkTel != null">#{linkTel},</if>
            0,
            <if test="pictures != null">#{pictures},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucAchievement" parameterType="UucAchievement">
        update uuc_achievement
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="application != null and application != ''">application = #{application},</if>
            <if test="step != null and step != ''">step = #{step},</if>
            <if test="cooperation != null and cooperation != ''">cooperation = #{cooperation},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="compnay != null">compnay = #{compnay},</if>
            <if test="linkName != null">link_name = #{linkName},</if>
            <if test="linkTel != null">link_tel = #{linkTel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="pictures != null">pictures = #{pictures},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUucAchievementById" parameterType="Long">
        delete from uuc_achievement where id = #{id}
    </delete>

    <delete id="deleteUucAchievementByIds" parameterType="String">
        delete from uuc_achievement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>