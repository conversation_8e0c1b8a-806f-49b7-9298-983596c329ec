package com.ruoyi.auth.config;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.system.api.RemoteSSOService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Feign客户端预热配置
 * 解决第一次调用慢的问题
 * 
 * <AUTHOR>
 */
@Component
public class FeignWarmupConfig implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(FeignWarmupConfig.class);

    @Autowired(required = false)
    private RemoteSSOService remoteSSOService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("开始预热Feign客户端...");

        // 异步预热，不阻塞应用启动
        new Thread(() -> {
            try {
                // 等待应用完全启动
                Thread.sleep(5000);

                // 预热多次，确保连接池建立
                for (int i = 0; i < 3; i++) {
                    warmupSSOService();
                    Thread.sleep(1000); // 间隔1秒
                }

                logger.info("Feign客户端预热完成");
            } catch (Exception e) {
                logger.warn("Feign客户端预热失败: {}", e.getMessage());
            }
        }, "feign-warmup").start();
    }

    /**
     * 预热SSO服务
     */
    private void warmupSSOService() {
        if (remoteSSOService == null) {
            logger.warn("RemoteSSOService未注入，跳过预热");
            return;
        }

        try {
            logger.info("预热SSO服务...");
            
            // 调用一个轻量级的接口进行预热
            // 使用一个不存在的手机号，只是为了建立连接
            remoteSSOService.checkUserExists("00000000000", SecurityConstants.INNER);
            
            logger.info("SSO服务预热成功");
        } catch (Exception e) {
            logger.warn("SSO服务预热失败: {}", e.getMessage());
        }
    }
}
