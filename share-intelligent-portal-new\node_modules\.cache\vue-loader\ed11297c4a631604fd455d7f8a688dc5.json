{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue", "mtime": 1750329471822}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRDb2RlSW1nLCBnZXRTU09Mb2dpblVybCB9IGZyb20gIkAvYXBpL2xvZ2luIjsNCmltcG9ydCBWZXJpZmljYXRpb25Db2RlIGZyb20gIkAvY29tcG9uZW50cy92ZXJpZmljYXRpb25Db2RlLyI7DQppbXBvcnQgQWdyZWVtZW50RGlhbG9nIGZyb20gIi4vYWdyZWVtZW50RGlhbG9nIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgVmVyaWZpY2F0aW9uQ29kZSwNCiAgICBBZ3JlZW1lbnREaWFsb2csDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZvcm06IHt9LA0KICAgICAgY29kZVVybDogIiIsIC8v5Zu+5b2i6aqM6K+B56CB5Zu+54mHDQogICAgICB0eXBlOiAiY29kZSIsIC8v6LSm5Y+357G75Z6LIChjb2Rl77ya6aqM6K+B56CB55m75b2VICBhY2NvdW50Oui0puWPt+WvhueggeeZu+W9lSAgc2V0OuWvhueggeiuvue9rikNCiAgICAgIGFncmVlbWVudDogMCwgLy/ljY/orq4NCiAgICAgIHByb3RvY29sc1Zpc2libGU6IGZhbHNlLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5pbml0Rm9ybSgpOw0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHJ1bGVzKCkgew0KICAgICAgbGV0IHJ1bGVzID0gew0KICAgICAgICB1c2VybmFtZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeaJi+acuuWPtyIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IG1pbjogMTEsIG1lc3NhZ2U6ICLor7fovpPlhaUxMeS9jeaJi+acuuWPtyIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBzbXNDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpemqjOivgeeggSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdmFsaWRhdG9yOiAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICAgICAgICAgIGlmICghdmFsdWUpIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbHVlLmxlbmd0aCAhPT0gNikgew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aqM6K+B56CB5qC85byP5LiN5q2j56GuIikpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29kZTogW3sgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36L6T5YWl6aqM6K+B56CBIiB9XSwNCiAgICAgICAgcGFzc3dvcmQxOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeehruiupOWvhueggSIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiA2LCBtZXNzYWdlOiAi6K+36L6T5YWlNi0xMeS9jeehruiupOWvhueggSIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWF4OiAxMSwgbWVzc2FnZTogIuivt+i+k+WFpTYtMTHkvY3noa7orqTlr4bnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0b3JQYXNzd29yZCB9LA0KICAgICAgICBdLA0KICAgICAgfTsNCiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJhY2NvdW50Iikgew0KICAgICAgICBydWxlcy5wYXNzd29yZCA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5a+G56CBIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtaW46IDYsIG1lc3NhZ2U6ICLor7fovpPlhaU2LTEx5L2N5a+G56CBIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtYXg6IDExLCBtZXNzYWdlOiAi6K+36L6T5YWlNi0xMeS9jeWvhueggSIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnR5cGUgPT09ICJzZXQiKSB7DQogICAgICAgIHJ1bGVzLnBhc3N3b3JkID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXlr4bnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogNiwgbWVzc2FnZTogIuivt+i+k+WFpTYtMTHkvY3lr4bnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1heDogMTEsIG1lc3NhZ2U6ICLor7fovpPlhaU2LTEx5L2N5a+G56CBIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdG9yUGFzc3dvcmQgfSwNCiAgICAgICAgXTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBydWxlczsNCiAgICB9LA0KICB9LA0KICB3YXRjaDogew0KICAgICJmb3JtLnBhc3N3b3JkIigpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucGFzc3dvcmQxICYmIHRoaXMuZm9ybS5wYXNzd29yZCA9PT0gdGhpcy5mb3JtLnBhc3N3b3JkMSkgew0KICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGVGaWVsZCgicGFzc3dvcmQxIik7DQogICAgICB9DQogICAgfSwNCiAgICAiZm9ybS5wYXNzd29yZDEiKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5wYXNzd29yZDEgJiYgdGhpcy5mb3JtLnBhc3N3b3JkID09PSB0aGlzLmZvcm0ucGFzc3dvcmQxKSB7DQogICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKCJwYXNzd29yZCIpOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgdXNlcm5hbWU6ICIiLCAvL+i0puWPtw0KICAgICAgICBzbXNDb2RlOiAiIiwgLy/nn63kv6Hpqozor4HnoIENCiAgICAgICAgcGFzc3dvcmQ6ICIiLCAvL+WvhueggQ0KICAgICAgICBwYXNzd29yZDE6ICIiLCAvL+ehruiupOWvhueggQ0KICAgICAgICBjb2RlOiAiIiwgLy/lm77lvaLpqozor4HnoIENCiAgICAgICAgdXVpZDogIiIsDQogICAgICB9Ow0KICAgIH0sDQogICAgLy8g5YiH5o2i55m75b2V5pa55byPDQogICAgc3dpdGNoTG9naW4odmFsKSB7DQogICAgICAvLyDnp7vpmaTlr4bnoIHnmbvlvZXml7bojrflj5blm77lvaLpqozor4HnoIHnmoTpgLvovpHvvIzlm6DkuLrmlrDnmoTmjqXlj6PkuI3pnIDopoHpqozor4HnoIENCiAgICAgIC8vIGlmICh2YWwgPT09ICJhY2NvdW50Iikgew0KICAgICAgLy8gICB0aGlzLmdldENvZGVJbWcoKTsNCiAgICAgIC8vIH0NCiAgICAgIHRoaXMuaW5pdEZvcm0oKTsNCiAgICAgIHRoaXMudHlwZSA9IHZhbDsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5mb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W5Zu+5b2i6aqM6K+B56CBDQogICAgZ2V0Q29kZUltZygpIHsNCiAgICAgIGdldENvZGVJbWcoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5jb2RlVXJsID0gImRhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCwiICsgcmVzLmltZzsNCiAgICAgICAgdGhpcy5mb3JtLnV1aWQgPSByZXMudXVpZDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5a+G56CB5qCh6aqMDQogICAgdmFsaWRhdG9yUGFzc3dvcmQocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBsZXQgcGFzc3dvcmQgPSB0aGlzLmZvcm0ucGFzc3dvcmQ7DQogICAgICBsZXQgcGFzc3dvcmQxID0gdGhpcy5mb3JtLnBhc3N3b3JkMTsNCiAgICAgIGlmIChwYXNzd29yZCAmJiBwYXNzd29yZDEgJiYgcGFzc3dvcmQgIT09IHBhc3N3b3JkMSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWvhueggei+k+WFpeS4jeS4gOiHtO+8jOivt+mHjeaWsOi+k+WFpSIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfSwNCiAgICBiZWZvcmVTZW5kQ29kZSgpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKCJ1c2VybmFtZSIsIChlcnJvck1lc3NhZ2UpID0+IHsNCiAgICAgICAgICBlcnJvck1lc3NhZ2UgPyByZWplY3QoKSA6IHJlc29sdmUoKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaJk+W8gOacjeWKoeWNj+iuruW8ueeqlw0KICAgIHZpZXdQcm90b2NvbHMoKSB7DQogICAgICB0aGlzLnByb3RvY29sc1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy8g55m75b2VDQogICAgaGFuZGxlTG9naW4oKSB7DQogICAgICBpZiAodGhpcy5hZ3JlZW1lbnQgIT09IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+mYheivu+W5tuWQjOaEj+OAiuacjeWKoeWNj+iuruOAiyIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBsZXQgb2JqID0geyAuLi50aGlzLmZvcm0sIHR5cGU6IHRoaXMudHlwZSB9Ow0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgdGhpcy4kc3RvcmUNCiAgICAgICAgICAgIC5kaXNwYXRjaCgiTG9naW4iLCBvYmopDQogICAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogdGhpcy5yZWRpcmVjdCB8fCAiLyIgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICAgIC8vIOenu+mZpOWvhueggeeZu+W9leWksei0peaXtumHjeaWsOiOt+WPluWbvuW9oumqjOivgeeggeeahOmAu+i+kQ0KICAgICAgICAgICAgICAvLyBpZiAodGhpcy50eXBlID09PSAiYWNjb3VudCIpIHsNCiAgICAgICAgICAgICAgLy8gICB0aGlzLmZvcm0uY29kZSA9ICIiOw0KICAgICAgICAgICAgICAvLyAgIHRoaXMuZ2V0Q29kZUltZygpOw0KICAgICAgICAgICAgICAvLyAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgLy8gICAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCJjb2RlIik7DQogICAgICAgICAgICAgIC8vICAgfSk7DQogICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBhdXRoZW50aWNhdGlvbigpIHsNCiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0NCiAgICAgICAgImh0dHBzOi8vcXl6aGZ3LmNoZW5neWFuZy5nb3YuY24vc3NvL2xvZ2luP3JlZGlyZWN0VXJsPWh0dHBzOi8vcXlmdy5jaGVuZ3lhbmcuZ292LmNuL2luZGV4IjsNCiAgICAgIC8vIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0NCiAgICAgIC8vICAgImh0dHBzOi8vcXl6aGZ3LmNoZW5neWFuZy5nb3YuY24vc3NvL2xvZ2luP3JlZGlyZWN0VXJsPWh0dHA6Ly9sb2NhbGhvc3QvaW5kZXgiOw0KICAgIH0sDQogICAgdG9JbmRleCgpew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiLyIgfSkNCiAgICB9LA0KICAgIC8vIFNTT+eZu+W9lQ0KICAgIGhhbmRsZVNTT0xvZ2luKCkgew0KICAgICAgZ2V0U1NPTG9naW5Vcmwod2luZG93LmxvY2F0aW9uLm9yaWdpbiArIHRoaXMuJHJvdXRlLmZ1bGxQYXRoKQ0KICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubG9naW5VcmwpIHsNCiAgICAgICAgICAgIC8vIOi3s+i9rOWIsFNTT+eZu+W9lemhtemdog0KICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSByZXNwb25zZS5kYXRhLmxvZ2luVXJsOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5ZTU0/nmbvlvZXlnLDlnYDlpLHotKUiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigiU1NP55m75b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJTU0/nmbvlvZXmnI3liqHlvILluLgiKTsNCiAgICAgICAgfSk7DQogICAgfQ0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-28 09:15:15\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-15 14:46:27\r\n-->\r\n<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"title\" @click=\"toIndex\">\r\n      <div class=\"titLeft\">\r\n        <img src=\"@/assets/images/home/<USER>\" alt=\"\" />\r\n      </div>\r\n      <div class=\"titRight\">易复材共享智造工业互联网平台</div>\r\n    </div>\r\n    <div class=\"login_content\">\r\n      <div class=\"left_img\"></div>\r\n      <div\r\n        :class=\"{\r\n          'login-content-code': type === 'code',\r\n          'login-content-account': type === 'account',\r\n          'login-content-set': type === 'set',\r\n        }\"\r\n      >\r\n        <div class=\"login-info\">\r\n          <!-- <div class=\"login-project-name\">星碳生态平台</div> -->\r\n          <div class=\"login-box\">\r\n            <div class=\"login-tab\">\r\n              <div\r\n                class=\"tabStyle\"\r\n                v-show=\"type == 'account' || type == 'code'\"\r\n              >\r\n                <div\r\n                  class=\"tab_left\"\r\n                  :style=\"\r\n                    type == 'code'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('code')\"\r\n                >\r\n                  验证码登录\r\n                </div>\r\n                <div\r\n                  class=\"tab_right\"\r\n                  :style=\"\r\n                    type == 'account'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('account')\"\r\n                >\r\n                  密码登录\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-show=\"type == 'set'\"\r\n                style=\"width: 100%; text-align: center\"\r\n              >\r\n                设置密码\r\n                <!-- {{\r\n                  type === \"account\"\r\n                    ? \"账号密码登录\"\r\n                    : type === \"code\"\r\n                    ? \"验证码登录\"\r\n                    : \"设置密码\"\r\n                }} -->\r\n              </div>\r\n            </div>\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\">\r\n              <el-form-item prop=\"username\">\r\n                <el-input\r\n                  v-model=\"form.username\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入手机号\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/mobile.png\"\r\n                    alt=\"\"\r\n                    style=\"width: 12px; height: 16px; margin: 12px\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 验证码登录 -->\r\n              <el-form-item\r\n                v-if=\"type !== 'account'\"\r\n                prop=\"smsCode\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <verification-code\r\n                  v-model=\"form.smsCode\"\r\n                  :mobile=\"{ phone: form.username }\"\r\n                  :before-send-code=\"beforeSendCode\"\r\n                ></verification-code>\r\n              </el-form-item>\r\n              <!-- 账号密码登录、密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'account' || type === 'set'\"\r\n                prop=\"password\"\r\n                class=\"form-item-password-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 账号密码登录 -->\r\n              <!-- <el-form-item\r\n                v-if=\"type === 'account'\"\r\n                prop=\"code\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.code\"\r\n                  placeholder=\"请输入验证码\"\r\n                  auto-complete=\"off\"\r\n                  style=\"width: 63%\"\r\n                  class=\"form-input-img-style\"\r\n                  :maxlength=\"200\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/imgCode.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n                <div class=\"login-code\">\r\n                  <img\r\n                    class=\"login-code-img\"\r\n                    :src=\"codeUrl\"\r\n                    @click=\"getCodeImg\"\r\n                  />\r\n                </div>\r\n              </el-form-item> -->\r\n              <!-- 密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'set'\"\r\n                prop=\"password1\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password1\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请确认密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-checkbox\r\n                  v-model=\"agreement\"\r\n                  :true-label=\"1\"\r\n                  :false-label=\"0\"\r\n                >\r\n                  <span class=\"login-agreement-text\">已阅读并同意</span>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    class=\"login-agreement-btn\"\r\n                    @click=\"viewProtocols\"\r\n                  >\r\n                    《服务协议》\r\n                  </el-button>\r\n                </el-checkbox>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"medium\"\r\n              class=\"button-area\"\r\n              @click=\"handleLogin\"\r\n              >登录</el-button\r\n            >\r\n            <el-button\r\n              type=\"info\"\r\n              size=\"medium\"\r\n              class=\"button-area sso-button\"\r\n              @click=\"handleSSOLogin\"\r\n              >SSO统一登录</el-button\r\n            >\r\n            <div class=\"button-switch-box\">\r\n              <el-button\r\n                v-if=\"type === 'account'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('set')\"\r\n                >设置密码</el-button\r\n              >\r\n              <el-button\r\n                v-if=\"type == 'set'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('account')\"\r\n                >密码登录</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务协议弹窗 -->\r\n    <agreement-dialog :visible.sync=\"protocolsVisible\"></agreement-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, getSSOLoginUrl } from \"@/api/login\";\r\nimport VerificationCode from \"@/components/verificationCode/\";\r\nimport AgreementDialog from \"./agreementDialog\";\r\n\r\nexport default {\r\n  components: {\r\n    VerificationCode,\r\n    AgreementDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      codeUrl: \"\", //图形验证码图片\r\n      type: \"code\", //账号类型 (code：验证码登录  account:账号密码登录  set:密码设置)\r\n      agreement: 0, //协议\r\n      protocolsVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.initForm();\r\n  },\r\n  computed: {\r\n    rules() {\r\n      let rules = {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n          { min: 11, message: \"请输入11位手机号\", trigger: \"blur\" },\r\n        ],\r\n        smsCode: [\r\n          { required: true, message: \"请输入验证码\", trigger: \"change\" },\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (!value) {\r\n                callback();\r\n              } else if (value.length !== 6) {\r\n                callback(new Error(\"验证码格式不正确\"));\r\n              } else {\r\n                callback();\r\n              }\r\n            },\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n        password1: [\r\n          { required: true, message: \"请输入确认密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ],\r\n      };\r\n      if (this.type === \"account\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n        ];\r\n      } else if (this.type === \"set\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ];\r\n      }\r\n      return rules;\r\n    },\r\n  },\r\n  watch: {\r\n    \"form.password\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password1\");\r\n      }\r\n    },\r\n    \"form.password1\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password\");\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        username: \"\", //账号\r\n        smsCode: \"\", //短信验证码\r\n        password: \"\", //密码\r\n        password1: \"\", //确认密码\r\n        code: \"\", //图形验证码\r\n        uuid: \"\",\r\n      };\r\n    },\r\n    // 切换登录方式\r\n    switchLogin(val) {\r\n      // 移除密码登录时获取图形验证码的逻辑，因为新的接口不需要验证码\r\n      // if (val === \"account\") {\r\n      //   this.getCodeImg();\r\n      // }\r\n      this.initForm();\r\n      this.type = val;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.clearValidate();\r\n      });\r\n    },\r\n    // 获取图形验证码\r\n    getCodeImg() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.form.uuid = res.uuid;\r\n      });\r\n    },\r\n    // 密码校验\r\n    validatorPassword(rule, value, callback) {\r\n      let password = this.form.password;\r\n      let password1 = this.form.password1;\r\n      if (password && password1 && password !== password1) {\r\n        callback(new Error(\"密码输入不一致，请重新输入\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    beforeSendCode() {\r\n      return new Promise((resolve, reject) => {\r\n        this.$refs.form.validateField(\"username\", (errorMessage) => {\r\n          errorMessage ? reject() : resolve();\r\n        });\r\n      });\r\n    },\r\n    // 打开服务协议弹窗\r\n    viewProtocols() {\r\n      this.protocolsVisible = true;\r\n    },\r\n    // 登录\r\n    handleLogin() {\r\n      if (this.agreement !== 1) {\r\n        this.$message({\r\n          message: \"请阅读并同意《服务协议》\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          let obj = { ...this.form, type: this.type };\r\n          this.loading = true;\r\n          this.$store\r\n            .dispatch(\"Login\", obj)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              // 移除密码登录失败时重新获取图形验证码的逻辑\r\n              // if (this.type === \"account\") {\r\n              //   this.form.code = \"\";\r\n              //   this.getCodeImg();\r\n              //   this.$nextTick(() => {\r\n              //     this.$refs.form.clearValidate(\"code\");\r\n              //   });\r\n              // }\r\n              this.loading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    authentication() {\r\n      window.location.href =\r\n        \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n      // window.location.href =\r\n      //   \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=http://localhost/index\";\r\n    },\r\n    toIndex(){\r\n      this.$router.push({ path: \"/\" })\r\n    },\r\n    // SSO登录\r\n    handleSSOLogin() {\r\n      getSSOLoginUrl(window.location.origin + this.$route.fullPath)\r\n        .then(response => {\r\n          if (response.code === 200 && response.data && response.data.loginUrl) {\r\n            // 跳转到SSO登录页面\r\n            window.location.href = response.data.loginUrl;\r\n          } else {\r\n            this.$message.error(\"获取SSO登录地址失败\");\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"SSO登录失败:\", error);\r\n          this.$message.error(\"SSO登录服务异常\");\r\n        });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url(\"../assets/login/background.png\") no-repeat;\r\n  background-size: 100% 100%;\r\n  padding-top: calc((100vh - 580px) / 2);\r\n  position: relative;\r\n  .title {\r\n    display: flex;\r\n    position: absolute;\r\n    left: 2%;\r\n    top: 72px;\r\n    width: 15%;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .titLeft {\r\n      width: 60px;\r\n      height: 50px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    .titRight {\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      color: #000000;\r\n      margin-left: 1%;\r\n      width: 210px;\r\n    }\r\n  }\r\n  .left_img {\r\n    width: 50%;\r\n    height: 367px;\r\n    position: absolute;\r\n    top: 95px;\r\n    left: 3.3%;\r\n    background: url(\"../assets/login/image.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login_content {\r\n    position: relative;\r\n    width: 62.5%;\r\n    height: 580px;\r\n    margin-left: 18.75%;\r\n    // margin-top: 100px;\r\n    // left: 18.75%;\r\n    // top: calc((100vh - 580px) / 2);\r\n    background: url(\"../assets/login/background1.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login-logo {\r\n    width: 139px;\r\n    height: 48px;\r\n    position: absolute;\r\n    top: 4.07%;\r\n    left: 2.6%;\r\n  }\r\n  .login-background1 {\r\n    position: absolute;\r\n    top: 24.07%;\r\n    left: 9.01%;\r\n    width: 1638px;\r\n    height: 613px;\r\n  }\r\n  .login-content-code {\r\n    position: absolute;\r\n    top: calc((100% - 400px) / 2);\r\n    // top: 28.33%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 470px;\r\n  }\r\n  .login-content-account {\r\n    position: absolute;\r\n    top: calc((100% - 465px) / 2);\r\n    // top: 25.37%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 535px;\r\n  }\r\n  .login-content-set {\r\n    position: absolute;\r\n    top: calc((100% - 530px) / 2);\r\n    // top: 22.4%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 600px;\r\n  }\r\n  .login-background2-code {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 22px;\r\n  }\r\n  .login-background2-account {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 49px;\r\n  }\r\n  .login-background2-set {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 77px;\r\n  }\r\n  .login-info {\r\n    width: 393px;\r\n    // width: 464px;\r\n    .login-project-name {\r\n      height: 70px;\r\n      font-size: 22px;\r\n      font-family: AlibabaPuHuiTi_2_85_Bold;\r\n      color: #fff;\r\n      line-height: 70px;\r\n      text-align: center;\r\n      // margin-bottom: 36px;\r\n      // background: rgb(41, 92, 233);\r\n      background: linear-gradient(\r\n        to right,\r\n        rgb(83, 140, 241),\r\n        rgb(41, 92, 233)\r\n      );\r\n      border-top-left-radius: 10px;\r\n      border-top-right-radius: 10px;\r\n    }\r\n    .login-box {\r\n      width: 100%;\r\n      background: #fff;\r\n      box-shadow: 0px 10px 30px 0px #********;\r\n      padding: 0 32px;\r\n      .login-tab {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 24px;\r\n        text-align: center;\r\n        padding: 40px 0 32px;\r\n        .tabStyle {\r\n          display: flex;\r\n          justify-content: center;\r\n          width: 100%;\r\n          .tab_left {\r\n            width: 91px;\r\n            height: 35px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n          .tab_right {\r\n            margin-left: 60px;\r\n            width: 73px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .form-item-password-style {\r\n        margin-top: 24px;\r\n      }\r\n      .form-item-style {\r\n        margin: 24px 0 15px;\r\n        .login-code {\r\n          width: 33%;\r\n          height: 40px;\r\n          float: right;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            cursor: pointer;\r\n            vertical-align: middle;\r\n          }\r\n        }\r\n      }\r\n      .input-icon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin: 12px;\r\n      }\r\n      .login-agreement-text {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 14px;\r\n      }\r\n      .login-agreement-btn {\r\n        color: #21c9b8;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n      }\r\n      .button-area {\r\n        margin: 7px 0 26px;\r\n        width: 330px;\r\n        height: 40px;\r\n        background: #21c9b8;\r\n        border-color: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n      }\r\n      .sso-button {\r\n        background: #409eff !important;\r\n        border-color: #409eff !important;\r\n        margin-top: 12px !important;\r\n        margin-bottom: 12px !important;\r\n      }\r\n      .button-switch-box {\r\n        display: flex;\r\n        justify-content: right;\r\n        align-items: center;\r\n        padding: 0 0 18px 69px;\r\n        .button-switch-style {\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.login-container {\r\n  .login-box {\r\n    .form-input-style {\r\n      .el-input__inner {\r\n        // width: 400px;\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-form-item__error {\r\n      background: url(\"../assets/login/warningIcon.png\") no-repeat;\r\n      background-size: 16px 16px;\r\n      padding-left: 18px;\r\n      padding-top: 3px;\r\n      margin-top: 2px;\r\n      height: 16px;\r\n      color: #f05642;\r\n    }\r\n    .form-input-img-style {\r\n      .el-input__inner {\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-checkbox__inner {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-top: -1px;\r\n      border-color: #d9d9d9;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner,\r\n    .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n      background-color: #21c9b8;\r\n      border-color: #21c9b8;\r\n    }\r\n    .el-checkbox__inner:hover {\r\n      border-color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}