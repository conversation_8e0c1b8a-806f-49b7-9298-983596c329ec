# Discovered Components

This is an auto-generated list of components discovered by [nuxt/components](https://github.com/nuxt/components).

You can directly use them in pages and other components without the need to import them.

**Tip:** If a component is conditionally rendered with `v-if` and is big, it is better to use `Lazy` or `lazy-` prefix to lazy load.

- `<AddRess>` | `<add-ress>` (components/AddRess/index.vue)
- `<ImageUpload>` | `<image-upload>` (components/ImageUpload/index.vue)
- `<FileUpload>` | `<file-upload>` (components/FileUpload/index.vue)
- `<IssueAccurate>` | `<issue-accurate>` (components/issue/accurate.vue)
- `<IssueEnquiryDetails>` | `<issue-enquiry-details>` (components/issue/enquiryDetails.vue)
- `<IssueTopspeed>` | `<issue-topspeed>` (components/issue/topspeed.vue)
- `<ShopCart>` | `<shop-cart>` (components/shop/cart.vue)
- `<ShopCompanyProfile>` | `<shop-company-profile>` (components/shop/company-profile.vue)
- `<ShopCompany>` | `<shop-company>` (components/shop/company.vue)
- `<ShopEcharts>` | `<shop-echarts>` (components/shop/echarts.vue)
- `<ShopEditor>` | `<shop-editor>` (components/shop/editor.vue)
- `<ShopFooter>` | `<shop-footer>` (components/shop/footer.vue)
- `<ShopOrderForm>` | `<shop-order-form>` (components/shop/order-form.vue)
- `<ShopProductStatus>` | `<shop-product-status>` (components/shop/product-status.vue)
- `<ShopScreen>` | `<shop-screen>` (components/shop/screen.vue)
- `<ShopSearchBar>` | `<shop-search-bar>` (components/shop/search-bar.vue)
- `<ShopItem>` | `<shop-item>` (components/shop/shop-item.vue)
- `<ShopTabBar>` | `<shop-tab-bar>` (components/shop/tab_bar.vue)
- `<LayoutNavbar>` | `<layout-navbar>` (components/layout/Navbar.vue)
- `<CommonPagination>` | `<common-pagination>` (components/common/Pagination/index.vue)
- `<CommonShopNoData>` | `<common-shop-no-data>` (components/common/shop/no-data.vue)
- `<LayoutSidebar>` | `<layout-sidebar>` (components/layout/Sidebar/index.vue)
- `<LayoutSidebarItem>` | `<layout-sidebar-item>` (components/layout/Sidebar/item.vue)
- `<CommonCenterEnterprise>` | `<common-center-enterprise>` (components/common/center/enterprise/index.vue)
- `<CommonCenterProfileCompany>` | `<common-center-profile-company>` (components/common/center/profile/company.vue)
- `<CommonCenterProfileInfo>` | `<common-center-profile-info>` (components/common/center/profile/info.vue)
- `<CommonCenterProfilePhone>` | `<common-center-profile-phone>` (components/common/center/profile/phone.vue)
- `<CommonCenterProfilePwd>` | `<common-center-profile-pwd>` (components/common/center/profile/pwd.vue)
- `<CommonShopProductDtModal>` | `<common-shop-product-dt-modal>` (components/common/shop/product/dt-modal.vue)
- `<CommonShopProductProductdetail>` | `<common-shop-product-productdetail>` (components/common/shop/product/productdetail.vue)
- `<CommonSystemOrgAddDep>` | `<common-system-org-add-dep>` (components/common/system/org/add-dep.vue)
- `<CommonSystemOrgList>` | `<common-system-org-list>` (components/common/system/org/list.vue)
- `<CommonSystemPermList>` | `<common-system-perm-list>` (components/common/system/perm/list.vue)
- `<CommonSystemPermUserModal>` | `<common-system-perm-user-modal>` (components/common/system/perm/user-modal.vue)
- `<CommonSystemRoleList>` | `<common-system-role-list>` (components/common/system/role/list.vue)
- `<CommonSystemRoleUpdateRole>` | `<common-system-role-update-role>` (components/common/system/role/update-role.vue)
