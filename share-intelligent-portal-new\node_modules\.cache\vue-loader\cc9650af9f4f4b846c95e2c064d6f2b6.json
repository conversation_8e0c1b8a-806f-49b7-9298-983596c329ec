{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\login.vue", "mtime": 1750329471822}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2aA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-28 09:15:15\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-15 14:46:27\r\n-->\r\n<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"title\" @click=\"toIndex\">\r\n      <div class=\"titLeft\">\r\n        <img src=\"@/assets/images/home/<USER>\" alt=\"\" />\r\n      </div>\r\n      <div class=\"titRight\">易复材共享智造工业互联网平台</div>\r\n    </div>\r\n    <div class=\"login_content\">\r\n      <div class=\"left_img\"></div>\r\n      <div\r\n        :class=\"{\r\n          'login-content-code': type === 'code',\r\n          'login-content-account': type === 'account',\r\n          'login-content-set': type === 'set',\r\n        }\"\r\n      >\r\n        <div class=\"login-info\">\r\n          <!-- <div class=\"login-project-name\">星碳生态平台</div> -->\r\n          <div class=\"login-box\">\r\n            <div class=\"login-tab\">\r\n              <div\r\n                class=\"tabStyle\"\r\n                v-show=\"type == 'account' || type == 'code'\"\r\n              >\r\n                <div\r\n                  class=\"tab_left\"\r\n                  :style=\"\r\n                    type == 'code'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('code')\"\r\n                >\r\n                  验证码登录\r\n                </div>\r\n                <div\r\n                  class=\"tab_right\"\r\n                  :style=\"\r\n                    type == 'account'\r\n                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'\r\n                      : ''\r\n                  \"\r\n                  @click=\"switchLogin('account')\"\r\n                >\r\n                  密码登录\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-show=\"type == 'set'\"\r\n                style=\"width: 100%; text-align: center\"\r\n              >\r\n                设置密码\r\n                <!-- {{\r\n                  type === \"account\"\r\n                    ? \"账号密码登录\"\r\n                    : type === \"code\"\r\n                    ? \"验证码登录\"\r\n                    : \"设置密码\"\r\n                }} -->\r\n              </div>\r\n            </div>\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\">\r\n              <el-form-item prop=\"username\">\r\n                <el-input\r\n                  v-model=\"form.username\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入手机号\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/mobile.png\"\r\n                    alt=\"\"\r\n                    style=\"width: 12px; height: 16px; margin: 12px\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 验证码登录 -->\r\n              <el-form-item\r\n                v-if=\"type !== 'account'\"\r\n                prop=\"smsCode\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <verification-code\r\n                  v-model=\"form.smsCode\"\r\n                  :mobile=\"{ phone: form.username }\"\r\n                  :before-send-code=\"beforeSendCode\"\r\n                ></verification-code>\r\n              </el-form-item>\r\n              <!-- 账号密码登录、密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'account' || type === 'set'\"\r\n                prop=\"password\"\r\n                class=\"form-item-password-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请输入密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <!-- 账号密码登录 -->\r\n              <!-- <el-form-item\r\n                v-if=\"type === 'account'\"\r\n                prop=\"code\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.code\"\r\n                  placeholder=\"请输入验证码\"\r\n                  auto-complete=\"off\"\r\n                  style=\"width: 63%\"\r\n                  class=\"form-input-img-style\"\r\n                  :maxlength=\"200\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/imgCode.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n                <div class=\"login-code\">\r\n                  <img\r\n                    class=\"login-code-img\"\r\n                    :src=\"codeUrl\"\r\n                    @click=\"getCodeImg\"\r\n                  />\r\n                </div>\r\n              </el-form-item> -->\r\n              <!-- 密码设置 -->\r\n              <el-form-item\r\n                v-if=\"type === 'set'\"\r\n                prop=\"password1\"\r\n                class=\"form-item-style\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.password1\"\r\n                  type=\"password\"\r\n                  autocomplete=\"off\"\r\n                  auto-complete=\"new-password\"\r\n                  placeholder=\"请确认密码\"\r\n                  class=\"form-input-style\"\r\n                  :maxlength=\"11\"\r\n                >\r\n                  <img\r\n                    slot=\"prefix\"\r\n                    src=\"../assets/login/lockIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"input-icon\"\r\n                  />\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-checkbox\r\n                  v-model=\"agreement\"\r\n                  :true-label=\"1\"\r\n                  :false-label=\"0\"\r\n                >\r\n                  <span class=\"login-agreement-text\">已阅读并同意</span>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    class=\"login-agreement-btn\"\r\n                    @click=\"viewProtocols\"\r\n                  >\r\n                    《服务协议》\r\n                  </el-button>\r\n                </el-checkbox>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"medium\"\r\n              class=\"button-area\"\r\n              @click=\"handleLogin\"\r\n              >登录</el-button\r\n            >\r\n            <el-button\r\n              type=\"info\"\r\n              size=\"medium\"\r\n              class=\"button-area sso-button\"\r\n              @click=\"handleSSOLogin\"\r\n              >SSO统一登录</el-button\r\n            >\r\n            <div class=\"button-switch-box\">\r\n              <el-button\r\n                v-if=\"type === 'account'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('set')\"\r\n                >设置密码</el-button\r\n              >\r\n              <el-button\r\n                v-if=\"type == 'set'\"\r\n                type=\"text\"\r\n                class=\"button-switch-style\"\r\n                @click=\"switchLogin('account')\"\r\n                >密码登录</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务协议弹窗 -->\r\n    <agreement-dialog :visible.sync=\"protocolsVisible\"></agreement-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, getSSOLoginUrl } from \"@/api/login\";\r\nimport VerificationCode from \"@/components/verificationCode/\";\r\nimport AgreementDialog from \"./agreementDialog\";\r\n\r\nexport default {\r\n  components: {\r\n    VerificationCode,\r\n    AgreementDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      codeUrl: \"\", //图形验证码图片\r\n      type: \"code\", //账号类型 (code：验证码登录  account:账号密码登录  set:密码设置)\r\n      agreement: 0, //协议\r\n      protocolsVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.initForm();\r\n  },\r\n  computed: {\r\n    rules() {\r\n      let rules = {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n          { min: 11, message: \"请输入11位手机号\", trigger: \"blur\" },\r\n        ],\r\n        smsCode: [\r\n          { required: true, message: \"请输入验证码\", trigger: \"change\" },\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (!value) {\r\n                callback();\r\n              } else if (value.length !== 6) {\r\n                callback(new Error(\"验证码格式不正确\"));\r\n              } else {\r\n                callback();\r\n              }\r\n            },\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n        password1: [\r\n          { required: true, message: \"请输入确认密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位确认密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ],\r\n      };\r\n      if (this.type === \"account\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n        ];\r\n      } else if (this.type === \"set\") {\r\n        rules.password = [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 6, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { max: 11, message: \"请输入6-11位密码\", trigger: \"blur\" },\r\n          { validator: this.validatorPassword },\r\n        ];\r\n      }\r\n      return rules;\r\n    },\r\n  },\r\n  watch: {\r\n    \"form.password\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password1\");\r\n      }\r\n    },\r\n    \"form.password1\"() {\r\n      if (this.form.password1 && this.form.password === this.form.password1) {\r\n        this.$refs.form.validateField(\"password\");\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        username: \"\", //账号\r\n        smsCode: \"\", //短信验证码\r\n        password: \"\", //密码\r\n        password1: \"\", //确认密码\r\n        code: \"\", //图形验证码\r\n        uuid: \"\",\r\n      };\r\n    },\r\n    // 切换登录方式\r\n    switchLogin(val) {\r\n      // 移除密码登录时获取图形验证码的逻辑，因为新的接口不需要验证码\r\n      // if (val === \"account\") {\r\n      //   this.getCodeImg();\r\n      // }\r\n      this.initForm();\r\n      this.type = val;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.clearValidate();\r\n      });\r\n    },\r\n    // 获取图形验证码\r\n    getCodeImg() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.form.uuid = res.uuid;\r\n      });\r\n    },\r\n    // 密码校验\r\n    validatorPassword(rule, value, callback) {\r\n      let password = this.form.password;\r\n      let password1 = this.form.password1;\r\n      if (password && password1 && password !== password1) {\r\n        callback(new Error(\"密码输入不一致，请重新输入\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    beforeSendCode() {\r\n      return new Promise((resolve, reject) => {\r\n        this.$refs.form.validateField(\"username\", (errorMessage) => {\r\n          errorMessage ? reject() : resolve();\r\n        });\r\n      });\r\n    },\r\n    // 打开服务协议弹窗\r\n    viewProtocols() {\r\n      this.protocolsVisible = true;\r\n    },\r\n    // 登录\r\n    handleLogin() {\r\n      if (this.agreement !== 1) {\r\n        this.$message({\r\n          message: \"请阅读并同意《服务协议》\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          let obj = { ...this.form, type: this.type };\r\n          this.loading = true;\r\n          this.$store\r\n            .dispatch(\"Login\", obj)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              // 移除密码登录失败时重新获取图形验证码的逻辑\r\n              // if (this.type === \"account\") {\r\n              //   this.form.code = \"\";\r\n              //   this.getCodeImg();\r\n              //   this.$nextTick(() => {\r\n              //     this.$refs.form.clearValidate(\"code\");\r\n              //   });\r\n              // }\r\n              this.loading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    authentication() {\r\n      window.location.href =\r\n        \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n      // window.location.href =\r\n      //   \"https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=http://localhost/index\";\r\n    },\r\n    toIndex(){\r\n      this.$router.push({ path: \"/\" })\r\n    },\r\n    // SSO登录\r\n    handleSSOLogin() {\r\n      getSSOLoginUrl(window.location.origin + this.$route.fullPath)\r\n        .then(response => {\r\n          if (response.code === 200 && response.data && response.data.loginUrl) {\r\n            // 跳转到SSO登录页面\r\n            window.location.href = response.data.loginUrl;\r\n          } else {\r\n            this.$message.error(\"获取SSO登录地址失败\");\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"SSO登录失败:\", error);\r\n          this.$message.error(\"SSO登录服务异常\");\r\n        });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url(\"../assets/login/background.png\") no-repeat;\r\n  background-size: 100% 100%;\r\n  padding-top: calc((100vh - 580px) / 2);\r\n  position: relative;\r\n  .title {\r\n    display: flex;\r\n    position: absolute;\r\n    left: 2%;\r\n    top: 72px;\r\n    width: 15%;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .titLeft {\r\n      width: 60px;\r\n      height: 50px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    .titRight {\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      color: #000000;\r\n      margin-left: 1%;\r\n      width: 210px;\r\n    }\r\n  }\r\n  .left_img {\r\n    width: 50%;\r\n    height: 367px;\r\n    position: absolute;\r\n    top: 95px;\r\n    left: 3.3%;\r\n    background: url(\"../assets/login/image.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login_content {\r\n    position: relative;\r\n    width: 62.5%;\r\n    height: 580px;\r\n    margin-left: 18.75%;\r\n    // margin-top: 100px;\r\n    // left: 18.75%;\r\n    // top: calc((100vh - 580px) / 2);\r\n    background: url(\"../assets/login/background1.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n  .login-logo {\r\n    width: 139px;\r\n    height: 48px;\r\n    position: absolute;\r\n    top: 4.07%;\r\n    left: 2.6%;\r\n  }\r\n  .login-background1 {\r\n    position: absolute;\r\n    top: 24.07%;\r\n    left: 9.01%;\r\n    width: 1638px;\r\n    height: 613px;\r\n  }\r\n  .login-content-code {\r\n    position: absolute;\r\n    top: calc((100% - 400px) / 2);\r\n    // top: 28.33%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 470px;\r\n  }\r\n  .login-content-account {\r\n    position: absolute;\r\n    top: calc((100% - 465px) / 2);\r\n    // top: 25.37%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 535px;\r\n  }\r\n  .login-content-set {\r\n    position: absolute;\r\n    top: calc((100% - 530px) / 2);\r\n    // top: 22.4%;\r\n    right: 3.7%; // 21.46\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 600px;\r\n  }\r\n  .login-background2-code {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 22px;\r\n  }\r\n  .login-background2-account {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 49px;\r\n  }\r\n  .login-background2-set {\r\n    width: 503px;\r\n    height: 438px;\r\n    margin-top: 77px;\r\n  }\r\n  .login-info {\r\n    width: 393px;\r\n    // width: 464px;\r\n    .login-project-name {\r\n      height: 70px;\r\n      font-size: 22px;\r\n      font-family: AlibabaPuHuiTi_2_85_Bold;\r\n      color: #fff;\r\n      line-height: 70px;\r\n      text-align: center;\r\n      // margin-bottom: 36px;\r\n      // background: rgb(41, 92, 233);\r\n      background: linear-gradient(\r\n        to right,\r\n        rgb(83, 140, 241),\r\n        rgb(41, 92, 233)\r\n      );\r\n      border-top-left-radius: 10px;\r\n      border-top-right-radius: 10px;\r\n    }\r\n    .login-box {\r\n      width: 100%;\r\n      background: #fff;\r\n      box-shadow: 0px 10px 30px 0px #********;\r\n      padding: 0 32px;\r\n      .login-tab {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 24px;\r\n        text-align: center;\r\n        padding: 40px 0 32px;\r\n        .tabStyle {\r\n          display: flex;\r\n          justify-content: center;\r\n          width: 100%;\r\n          .tab_left {\r\n            width: 91px;\r\n            height: 35px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n          .tab_right {\r\n            margin-left: 60px;\r\n            width: 73px;\r\n            font-size: 18px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .form-item-password-style {\r\n        margin-top: 24px;\r\n      }\r\n      .form-item-style {\r\n        margin: 24px 0 15px;\r\n        .login-code {\r\n          width: 33%;\r\n          height: 40px;\r\n          float: right;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            cursor: pointer;\r\n            vertical-align: middle;\r\n          }\r\n        }\r\n      }\r\n      .input-icon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin: 12px;\r\n      }\r\n      .login-agreement-text {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 14px;\r\n      }\r\n      .login-agreement-btn {\r\n        color: #21c9b8;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n      }\r\n      .button-area {\r\n        margin: 7px 0 26px;\r\n        width: 330px;\r\n        height: 40px;\r\n        background: #21c9b8;\r\n        border-color: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n      }\r\n      .sso-button {\r\n        background: #409eff !important;\r\n        border-color: #409eff !important;\r\n        margin-top: 12px !important;\r\n        margin-bottom: 12px !important;\r\n      }\r\n      .button-switch-box {\r\n        display: flex;\r\n        justify-content: right;\r\n        align-items: center;\r\n        padding: 0 0 18px 69px;\r\n        .button-switch-style {\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.login-container {\r\n  .login-box {\r\n    .form-input-style {\r\n      .el-input__inner {\r\n        // width: 400px;\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-form-item__error {\r\n      background: url(\"../assets/login/warningIcon.png\") no-repeat;\r\n      background-size: 16px 16px;\r\n      padding-left: 18px;\r\n      padding-top: 3px;\r\n      margin-top: 2px;\r\n      height: 16px;\r\n      color: #f05642;\r\n    }\r\n    .form-input-img-style {\r\n      .el-input__inner {\r\n        height: 40px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        line-height: 14px;\r\n        padding-left: 40px;\r\n      }\r\n    }\r\n    .el-checkbox__inner {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-top: -1px;\r\n      border-color: #d9d9d9;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__inner::after {\r\n      margin-top: 0.5px;\r\n      margin-left: 1px;\r\n    }\r\n    .el-checkbox__input.is-checked .el-checkbox__inner,\r\n    .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n      background-color: #21c9b8;\r\n      border-color: #21c9b8;\r\n    }\r\n    .el-checkbox__inner:hover {\r\n      border-color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}