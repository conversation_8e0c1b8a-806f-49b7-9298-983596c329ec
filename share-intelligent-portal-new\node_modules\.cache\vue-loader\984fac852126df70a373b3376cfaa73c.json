{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue?vue&type=template&id=8cb17206&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyHall.vue", "mtime": 1750385853725}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}