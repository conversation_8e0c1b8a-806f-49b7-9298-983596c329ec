package com.ruoyi.sso.mapper;

import com.ruoyi.sso.domain.SSOUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SSO用户Mapper接口
 * 
 * <AUTHOR>
 */
public interface SSOUserMapper {

    /**
     * 根据用户名查询SSO用户
     *
     * @param username 用户名
     * @return SSO用户信息
     */
    SSOUser selectSSOUserByUsername(String username);

    /**
     * 根据手机号查询SSO用户
     *
     * @param phone 手机号
     * @return SSO用户信息
     */
    SSOUser selectSSOUserByPhone(String phone);

    /**
     * 根据用户ID查询SSO用户
     *
     * @param userId 用户ID
     * @return SSO用户信息
     */
    SSOUser selectSSOUserById(Long userId);

    /**
     * 查询SSO用户列表
     *
     * @param ssoUser SSO用户
     * @return SSO用户集合
     */
    List<SSOUser> selectSSOUserList(SSOUser ssoUser);

    /**
     * 新增SSO用户
     *
     * @param ssoUser SSO用户
     * @return 结果
     */
    int insertSSOUser(SSOUser ssoUser);

    /**
     * 修改SSO用户
     *
     * @param ssoUser SSO用户
     * @return 结果
     */
    int updateSSOUser(SSOUser ssoUser);

    /**
     * 删除SSO用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteSSOUserById(Long userId);

    /**
     * 批量删除SSO用户
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteSSOUserByIds(Long[] userIds);

    /**
     * 验证用户名是否唯一
     *
     * @param username 用户名
     * @return 结果
     */
    int checkUsernameUnique(String username);

    /**
     * 验证手机号是否唯一
     *
     * @param phone 手机号
     * @return 结果
     */
    int checkPhoneUnique(String phone);

    /**
     * 验证邮箱是否唯一
     *
     * @param email 邮箱
     * @return 结果
     */
    int checkEmailUnique(String email);

    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 结果
     */
    int updateUserLoginInfo(@Param("userId") Long userId, @Param("loginIp") String loginIp);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param password 新密码
     * @return 结果
     */
    int resetUserPassword(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 根据系统权限查询用户
     *
     * @param clientId 客户端ID
     * @return 用户列表
     */
    List<SSOUser> selectUsersBySystemAccess(String clientId);
}
