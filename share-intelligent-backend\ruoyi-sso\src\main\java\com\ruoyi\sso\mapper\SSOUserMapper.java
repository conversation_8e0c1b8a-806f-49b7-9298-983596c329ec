package com.ruoyi.sso.mapper;

import com.ruoyi.sso.domain.SSOUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SSO用户Mapper接口
 *
 * <AUTHOR>
 */
public interface SSOUserMapper {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    SSOUser selectByUsername(String username);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户对象信息
     */
    SSOUser selectByPhone(String phone);

    /**
     * 根据用户ID查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    SSOUser selectById(Long id);



    /**
     * 更新最后登录时间
     *
     * @param userId 用户ID
     * @return 结果
     */
    int updateLastLoginTime(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insert(SSOUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int update(SSOUser user);
}
