{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue?vue&type=style&index=0&id=05a221dd&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\release\\components\\processRequire.vue", "mtime": 1750385853720}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5mb290ZXItc3VibWl0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbi10b3A6IDYwcHg7DQoNCiAgLmVsLWJ1dHRvbiB7DQogICAgd2lkdGg6IDE0MHB4Ow0KICAgIGhlaWdodDogNTBweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["processRequire.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "processRequire.vue", "sourceRoot": "src/views/release/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"外协工序名称\" prop=\"processName\">\r\n        <el-input v-model=\"form.processName\" maxlength=\"50\" show-word-limit placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"加工数量\" prop=\"processingQuantity\">\r\n        <el-input type=\"number\" min=\"0\" v-model=\"form.processingQuantity\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"外协加工内容\" prop=\"outsourcingContent\">\r\n        <el-input v-model=\"form.outsourcingContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n          show-word-limit placeholder=\"请输入\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"要求完成时间\" prop=\"requiredCompletionTime\">\r\n        <el-date-picker v-model=\"form.requiredCompletionTime\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\"\r\n          style=\"width: 100%\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"订单号\" prop=\"projectNumber\">\r\n        <el-input v-model=\"form.projectNumber\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"备注\" prop=\"remarks\">\r\n        <el-input v-model=\"form.remarks\" placeholder=\"请输入\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input disabled v-model=\"form.companyName\" placeholder=\"请先绑定公司\"></el-input>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"联系人\" prop=\"contactPerson\">\r\n        <el-input disabled v-model=\"form.contactPerson\" placeholder=\"请先维护联系人\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n        <el-input disabled v-model=\"form.contactPhone\" placeholder=\"请先维护联系方式\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n        <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { releaseProcess } from \"@/api/release\"\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        processName: \"\",\r\n        processingQuantity: \"\",\r\n        outsourcingContent: \"\",\r\n        requiredCompletionTime: \"\",\r\n        projectNumber: \"\",\r\n        remarks: \"\",\r\n        companyName: \"\",\r\n        contactPerson: \"\",\r\n        contactPhone: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        processName: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        processingQuantity: [\r\n          { required: true, message: \"加工数量不能为空\", trigger: \"blur\" },\r\n        ],\r\n        outsourcingContent: [\r\n          { required: true, message: \"外协加工内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        requiredCompletionTime: [\r\n          { required: true, message: \"要求完成时间不能为空\", trigger: \"blur\" },\r\n        ],\r\n        projectNumber: [\r\n          { required: true, message: \"订单号不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n    if(userinfo && userinfo != 'null') {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.contactPerson = userinfo.memberRealName;\r\n      this.form.contactPhone = userinfo.memberPhone;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit(status) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n\r\n          releaseProcess(this.form).then(res => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel()\r\n            } else {\r\n              this.$message.error('发布失败')\r\n            }\r\n          })\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}