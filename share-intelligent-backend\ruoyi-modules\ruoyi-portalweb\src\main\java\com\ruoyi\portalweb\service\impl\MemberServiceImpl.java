package com.ruoyi.portalweb.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Map;
import java.util.HashMap;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.api.enums.MemberStatus;
import com.ruoyi.portalweb.api.enums.YesOrNo;
import com.ruoyi.portalweb.service.ICompanyRelatedService;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.CustomerAudingVO;
import com.ruoyi.portalweb.vo.FileDetailVO;
import com.ruoyi.system.api.domain.TalentInfoApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.model.LoginMember;
import com.ruoyi.portalweb.mapper.MemberMapper;
import com.ruoyi.portalweb.service.ICompanyService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.RemoteTalentService;
import com.ruoyi.system.api.RemoteSSOService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.security.utils.SecurityUtils;


import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 会员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class MemberServiceImpl implements IMemberService {
	@Autowired
	private MemberMapper memberMapper;
	@Autowired
	private ICompanyService companyService;
	@Autowired
	private IFileDetailService fileDetailService;
	@Autowired
	private ICompanyRelatedService companyRelatedService;
	@Autowired
	private RemoteUserService remoteUserService;
	@Autowired
	private RemoteTalentService remoteTalentService;
	@Autowired
	private RemoteSSOService remoteSSOService;

	/**
	 * 查询会员
	 *
	 * @param memberId
	 *            会员主键
	 * @return 会员
	 */
	@Override
	public MemberVO selectMemberByMemberId(Long memberId) {
		return memberMapper.selectMemberByMemberId(memberId);
	}
	/**
	 * 查询会员列表
	 *
	 * @param member
	 *            会员
	 * @return 会员
	 */
	@Override
	public List<MemberVO> selectMemberList(Member member) {
		MemberVO memberVO = memberMapper.selectMemberByMemberId(SecurityUtils.getUserId());
		if (memberVO.getIsAdmin() == null || memberVO.getIsAdmin().equals(YesOrNo.NO.getValue())) {
			throw new SecurityException("非企业管理员");
		}
		startPage();
		PageUtils.setOrderBy("member_id DESC");
		member.setCompanyRelatedId(memberVO.getCompanyRelatedId());
		// 会员数据出现isAdmin Y ,但是未关联企业的数据，以下解决这种情况下查询到所有会员的bug, 待找出数据原因后可删除
		if (member.getCompanyRelatedId() == null){
			Member updateMember = new Member();
			updateMember.setMemberId(memberVO.getMemberId());
			updateMember.setIsAdmin(YesOrNo.NO.getValue());
			memberMapper.updateMember(updateMember);
			return new ArrayList<>();
		}
		return memberMapper.selectMemberList(member);
	}

	/**
	 * 新增会员
	 *
	 * @param member
	 *            会员
	 * @return 结果
	 */
	@Override
	public int insertMember(Member member) {
		member.setCreateTime(DateUtils.getNowDate());
		member.setIsAdmin(YesOrNo.NO.getValue());
		return memberMapper.insertMember(member);
	}

	/**
	 * 企业认证
	 */
	public int saveCompany(CompanyVO company) {
		Integer res = null;
		if (company.getCompanyId() == null) {
			// 新增
			res = companyService.insertCompany(company);
		} else {
			CompanyVO saveData = companyService.selectCompanyByCompanyId(company.getCompanyId());
			if (saveData != null && saveData.getCompanyId() != null) {
				// 修改
				res = companyService.updateCompany(company);
			} else {
				// 新增
				res = companyService.insertCompany(company);
			}
		}
		// 插入成功,再修改member关联字段
		if (res == 1) {
			Member member = new Member();
			member.setMemberId(SecurityUtils.getUserId());
			member.setMemberCompanyId(company.getCompanyId());
			memberMapper.updateMember(member);
		}
		return res;
	}

	/**
	 * 修改会员
	 *
	 * @param member
	 *            会员
	 * @return 结果
	 */
	@Override
	public void updateMemberInfo(Member member) {
		member.setUpdateTime(DateUtils.getNowDate());
		// 如果是关联企业操作则保存企业信息
		saveCompanyRelatedInfo(member);
	}

	@Override
	public int updateMember(Member member) {
		return memberMapper.updateMember(member);
	}

	@Override
	public int quitCompanyRelated() {
		return memberMapper.quitCompanyRelated(SecurityUtils.getUserId(), null);
	}


	public void removeMemberFromCompanyRelated(List<Long> memberIds) {
		MemberVO memberVO = memberMapper.selectMemberByMemberId(SecurityUtils.getUserId());
		if (!Objects.equals(memberVO.getIsAdmin(), "Y")){
			throw new ServiceException("非管理员");
		}
		for (Long memberId: memberIds){
			memberMapper.quitCompanyRelated(memberId, memberVO.getCompanyRelatedId());
		}
	}

	@Override
	public int auditMember(Member member) {
		Member updateEntity = new Member();
		updateEntity.setMemberId(member.getMemberId());
		updateEntity.setMemberStatus(member.getMemberStatus());
		return memberMapper.updateMember(updateEntity);
	}

	/**
	 * 批量删除会员
	 *
	 * @param memberIds
	 *            需要删除的会员主键
	 * @return 结果
	 */
	@Override
	public int deleteMemberByMemberIds(Long[] memberIds) {
		return memberMapper.deleteMemberByMemberIds(memberIds);
	}

	/**
	 * 获取我的企业信息
	 */
	public CompanyVO getMyCompany() {
		MemberVO member = memberMapper.selectMemberByMemberId(SecurityUtils.getUserId());
		if (member != null && member.getMemberCompanyId() != null) {
			CompanyVO companyVO = companyService.selectCompanyByCompanyId(member.getMemberCompanyId());
			if (companyVO != null) {
				List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(companyVO.getCompanyId(),"company", "");
				List<FileDetailVO> list0601 = new ArrayList<>();
				List<FileDetailVO> list0602 = new ArrayList<>();
				for (FileDetailVO item : alPictureVOs) {
					if ("0601".equals(item.getFileType())) {
						list0601.add(item);
					}
					if ("0602".equals(item.getFileType())) {
						list0602.add(item);
					}
				}
				companyVO.setAlFile0601(list0601);
				companyVO.setAlFile0602(list0602);
			}
			return companyVO;
		} else {
			return null;
		}
	}

	@Override
	public CompanyRelated getMyCompanyRelated() {
		MemberVO member = memberMapper.selectMemberByMemberId(SecurityUtils.getUserId());
		if (member != null && member.getCompanyRelatedId() != null) {
			CompanyRelated companyRelated = companyRelatedService.selectCompanyRelatedByCompanyRelatedId(member.getCompanyRelatedId());
			return companyRelated;
		} else {
			return null;
		}
	}

	/**
	 * 删除会员信息
	 *
	 * @param memberId
	 *            会员主键
	 * @return 结果
	 */
	@Override
	public int deleteMemberByMemberId(Long memberId) {
		return memberMapper.deleteMemberByMemberId(memberId);
	}

	@Override
	public LoginMember selectMemberByPhone(String memberphone) {
		LoginMember loginMember = new LoginMember();
		Member member = memberMapper.selectMemberByPhone(memberphone);
		loginMember.setMember(member);
		Company company = companyService.selectCompanyByCompanyIdAndMemberId(member.getMemberCompanyId(), member.getMemberId());
		if (company != null) {
			loginMember.setCompany(company);
		}
		return loginMember;
	}

	/**
	 * 根据手机号，查询会员信息
	 */
	public LoginMember selectMemberBySmsCode(String memberphone) {
		LoginMember loginMember = new LoginMember();
//		System.out.println("------------登录memberphone:"+memberphone+"--------------------------");
		Member member = memberMapper.selectMemberByPhone(memberphone);
		// 没有账号注册个新的
		if (member == null) {
			member = new Member();
			member.setMemberPhone(memberphone);
//			member.setMemberPassword(SecurityUtils.encryptPassword("123456"));//默认密码
			member.setMemberRealName(memberphone);
			member.setMemberStatus("0");
			loginMember = this.registerMember(member);
		} else {
			loginMember.setMember(member);
			Company company = companyService.selectCompanyByCompanyIdAndMemberId(member.getMemberCompanyId(), member.getMemberId());
			if (company != null) {
				loginMember.setCompany(company);
			}
		}
		return loginMember;
	}

	@Override
	public LoginMember registerMember(Member member) {
		LoginMember loginMember = new LoginMember();
		member.setCreateTime(DateUtils.getNowDate());

		// 创建Member记录
		memberMapper.insertMember(member);
		loginMember.setMember(member);

		// 同步创建SSO用户（验证码登录自动注册时）
		createSSOUserForMember(member);

		return loginMember;
	}

	/**
	 * 为Member创建对应的SSO用户
	 * 验证码登录自动注册时调用
	 */
	private void createSSOUserForMember(Member member) {
		try {
			if (member == null || member.getMemberPhone() == null) {
				return;
			}

			// 生成默认密码（验证码登录用户的默认密码）
			String defaultPassword = SecurityUtils.encryptPassword("123456");

			// 调用SSO服务创建用户
			R<Boolean> ssoResult = remoteSSOService.createSSOUser(
				member.getMemberPhone(),
				member.getMemberRealName() != null ? member.getMemberRealName() : member.getMemberPhone(),
				defaultPassword,
				SecurityConstants.INNER
			);

			if (ssoResult != null && R.isSuccess(ssoResult)) {
				System.out.println("验证码登录自动注册：SSO用户创建成功 - " + member.getMemberPhone());
			} else {
				System.out.println("验证码登录自动注册：SSO用户创建失败 - " + member.getMemberPhone() +
					", 原因: " + (ssoResult != null ? ssoResult.getMsg() : "SSO服务不可用"));
			}
		} catch (Exception e) {
			System.out.println("验证码登录自动注册：SSO用户创建异常 - " + member.getMemberPhone() +
				", 异常: " + e.getMessage());
		}
	}

	@Override
	public int updateMemberPassword(Member member) {
		return memberMapper.updateMemberPassword(member);
	}


	public void  saveCompanyRelatedInfo(Member member){

		// 查询该公司是否曾经被关联过, 如果有则直接返回, 没有则创建
		if (member.getMemberCompanyName() != null && !Objects.equals(member.getMemberCompanyName(), "")){
			Long companyRelatedId = null;
			String isAdmin = null;
			List<CompanyRelated> companyRelatedList = companyRelatedService.selectCompanyRelatedByCompanyName(member.getMemberCompanyName());
			if (!companyRelatedList.isEmpty()){
				companyRelatedId =  companyRelatedList.get(0).getCompanyRelatedId();
				// 查找企业下是否有管理员
				Member wrapper = new Member();
				wrapper.setCompanyRelatedId(companyRelatedId);
				wrapper.setIsAdmin(YesOrNo.YES.getValue());
				wrapper.setMemberStatus(MemberStatus.OK.getCode());
				List<MemberVO> memberVOS = memberMapper.selectMemberList(wrapper);
				if (memberVOS.isEmpty()){
					isAdmin = YesOrNo.YES.getValue();
				}else{
				   isAdmin = YesOrNo.NO.getValue();
				}
			}else{
				CustomerAudingVO customerAudingVO = companyService.searchByCustomerName(member.getMemberCompanyName());
				CompanyRelated companyRelatedEntity = this.getCompanyRelatedEntity(customerAudingVO);
				companyRelatedService.insertCompanyRelated(companyRelatedEntity);
				companyRelatedId = companyRelatedEntity.getCompanyRelatedId();
				isAdmin = YesOrNo.YES.getValue();
			}

			// 更新关联公司
			member.setCompanyRelatedId(companyRelatedId);
			member.setIsAdmin(isAdmin);
			memberMapper.updateMember(member);

		}else {
			// 置空关联公司
			memberMapper.quitCompanyRelated(member.getMemberId(),null);
		}
	}



	private CompanyRelated getCompanyRelatedEntity(CustomerAudingVO customerAudingVO){
		CompanyRelated companyRelated = new CompanyRelated();
		companyRelated.setCompanyName(customerAudingVO.getCompName());
		companyRelated.setAddress(customerAudingVO.getAddress());
		companyRelated.setPhone(customerAudingVO.getCompPhone());
		companyRelated.setCompanyLegal(customerAudingVO.getCompPhone());
		companyRelated.setPhone(customerAudingVO.getCompPhone());
		companyRelated.setCompanyLegal(customerAudingVO.getCompLegal());
		companyRelated.setSocialUnityCreditCode(customerAudingVO.getTaxNo());
		companyRelated.setRegisteredCapital(customerAudingVO.getRegCapital());
		companyRelated.setEstablishTime(customerAudingVO.getEstiblishTime());
		companyRelated.setStatus(customerAudingVO.getRegStatus());
		companyRelated.setRegNumber(customerAudingVO.getRegNumber());
		companyRelated.setOrgNumber(customerAudingVO.getOrgNumber());
		companyRelated.setCompanyOrgType(customerAudingVO.getCompanyOrgType());
		companyRelated.setServiceIndustry(customerAudingVO.getIndustry());
		companyRelated.setActualCapital(customerAudingVO.getActualCapital());
		companyRelated.setCompanySize(customerAudingVO.getSocialStaffNum());
		companyRelated.setHistoryNameList(customerAudingVO.getHistoryNameList());
		return companyRelated;
	}


	/**
	 * 根据用户ID查询人才信息
	 *
	 * @param userId 用户ID
	 * @return 人才信息
	 */
	@Override
	public TalentInfoApi selectTalentInfoByUserId(Long userId) {
		R<TalentInfoApi> talentResult = remoteTalentService.getTalentByUserId(userId);
		if (talentResult.getCode() == 200 && talentResult.getData() != null) {
			return talentResult.getData();
		}
		return null;
	}

}
