09:11:11.298 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:12.831 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0
09:11:12.974 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 90 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:13.058 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:13.081 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:13.364 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 279 ms to scan 207 urls, producing 0 keys and 0 values 
09:11:13.378 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:13.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:13.420 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:13.687 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 264 ms to scan 207 urls, producing 0 keys and 0 values 
09:11:13.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:13.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1062163248
09:11:13.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/21723014
09:11:13.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:13.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:13.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:17.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381876963_127.0.0.1_49705
09:11:17.508 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Notify connected event to listeners.
09:11:17.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:17.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e187f5e-a4d4-4274-95cb-8ad2b54988f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/803685654
09:11:17.916 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:24.927 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:11:24.933 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:24.933 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:11:25.609 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:33.496 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:35.102 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f0187db-9bab-45cb-8342-a6a36addfd4c
09:11:35.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] RpcClient init label, labels = {module=naming, source=sdk}
09:11:35.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:35.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:35.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:35.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:35.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381895128_127.0.0.1_50014
09:11:35.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:35.237 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Notify connected event to listeners.
09:11:35.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0187db-9bab-45cb-8342-a6a36addfd4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/803685654
