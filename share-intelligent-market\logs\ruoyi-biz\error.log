19:36:13.260 [main] ERROR o.s.b.SpringApplication - [reportFailure,830] - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'wxPayInformController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderBiz': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jcscWeChatPayConfig' defined in class path resource [com/ruoyi/biz/shop/config/WechatPayJcscConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wechat.pay.java.core.RSAAutoCertificateConfig]: Factory method 'jcscWeChatPayConfig' threw exception; nested exception is com.wechat.pay.java.core.exception.ServiceException: Wrong HttpStatusCode[401]
httResponseBody[{"code":"SIGN_ERROR","detail":{"detail":{"issue":"sign not match"},"field":"signature","location":"authorization","sign_information":{"method":"GET","sign_message_length":85,"truncated_sign_message":"GET\n/v3/certificates?algorithm_type=RSA\n1750332972\nbFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\n\n","url":"/v3/certificates?algorithm_type=RSA"}},"message":"错误的签名，验签失败"}]	HttpRequest[{"http_method":"GET","url":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","uri":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","headers":{"headers":{"Authorization":"WECHATPAY2-SHA256-RSA2048 mchid=\"1623108931\",nonce_str=\"bFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\",timestamp=\"1750332972\",serial_no=\"2CDF73C504F8F46F81EFFDA1062F472631FD6DC9\",signature=\"ZpnfZT243qi2gIANY2a3yzcZnz1rXFX7usGvXYqhm+938BTQRKVA8Vrlvpsdng9K/25lxtH4yQTjYgFWFm9txdgzBXvUHSdZmCdrWnT4lserdehXXqGASbkhkv129IMekzNWeNkCVDEQyQAcf/faVAK+ALbb5Ez4zj+LDN5KkjYF2fzCOzTlIWm2xLJK+xaVOEb13qKcBaJPnRKlLmlc/f7m4gCe6Kw0MG5qq8K56yzyOHThug8v4ydXBSU3bwWYOqf2+b8rv0xoaq2MI3gEss4WBHtN6OLdFWttv+GtKXBVQ2zIy6/AsYRvUJ7VOGLpwkKSo+8Zw3PXha5ZDb52/A==\"","Accept":" */*","User-Agent":"WechatPay-Java/0.2.5 (Windows 10/10.0) Java/1.8.0_152 Credential/WechatPay2Credential Validator/ okhttp3/null","Content-Type":"application/json"}}}]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:953)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:740)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:415)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1301)
	at com.ruoyi.biz.shop.RuoyiBizShopApplication.main(RuoyiBizShopApplication.java:17)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'orderBiz': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jcscWeChatPayConfig' defined in class path resource [com/ruoyi/biz/shop/config/WechatPayJcscConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wechat.pay.java.core.RSAAutoCertificateConfig]: Factory method 'jcscWeChatPayConfig' threw exception; nested exception is com.wechat.pay.java.core.exception.ServiceException: Wrong HttpStatusCode[401]
httResponseBody[{"code":"SIGN_ERROR","detail":{"detail":{"issue":"sign not match"},"field":"signature","location":"authorization","sign_information":{"method":"GET","sign_message_length":85,"truncated_sign_message":"GET\n/v3/certificates?algorithm_type=RSA\n1750332972\nbFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\n\n","url":"/v3/certificates?algorithm_type=RSA"}},"message":"错误的签名，验签失败"}]	HttpRequest[{"http_method":"GET","url":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","uri":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","headers":{"headers":{"Authorization":"WECHATPAY2-SHA256-RSA2048 mchid=\"1623108931\",nonce_str=\"bFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\",timestamp=\"1750332972\",serial_no=\"2CDF73C504F8F46F81EFFDA1062F472631FD6DC9\",signature=\"ZpnfZT243qi2gIANY2a3yzcZnz1rXFX7usGvXYqhm+938BTQRKVA8Vrlvpsdng9K/25lxtH4yQTjYgFWFm9txdgzBXvUHSdZmCdrWnT4lserdehXXqGASbkhkv129IMekzNWeNkCVDEQyQAcf/faVAK+ALbb5Ez4zj+LDN5KkjYF2fzCOzTlIWm2xLJK+xaVOEb13qKcBaJPnRKlLmlc/f7m4gCe6Kw0MG5qq8K56yzyOHThug8v4ydXBSU3bwWYOqf2+b8rv0xoaq2MI3gEss4WBHtN6OLdFWttv+GtKXBVQ2zIy6/AsYRvUJ7VOGLpwkKSo+8Zw3PXha5ZDb52/A==\"","Accept":" */*","User-Agent":"WechatPay-Java/0.2.5 (Windows 10/10.0) Java/1.8.0_152 Credential/WechatPay2Credential Validator/ okhttp3/null","Content-Type":"application/json"}}}]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:479)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:550)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jcscWeChatPayConfig' defined in class path resource [com/ruoyi/biz/shop/config/WechatPayJcscConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wechat.pay.java.core.RSAAutoCertificateConfig]: Factory method 'jcscWeChatPayConfig' threw exception; nested exception is com.wechat.pay.java.core.exception.ServiceException: Wrong HttpStatusCode[401]
httResponseBody[{"code":"SIGN_ERROR","detail":{"detail":{"issue":"sign not match"},"field":"signature","location":"authorization","sign_information":{"method":"GET","sign_message_length":85,"truncated_sign_message":"GET\n/v3/certificates?algorithm_type=RSA\n1750332972\nbFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\n\n","url":"/v3/certificates?algorithm_type=RSA"}},"message":"错误的签名，验签失败"}]	HttpRequest[{"http_method":"GET","url":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","uri":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","headers":{"headers":{"Authorization":"WECHATPAY2-SHA256-RSA2048 mchid=\"1623108931\",nonce_str=\"bFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\",timestamp=\"1750332972\",serial_no=\"2CDF73C504F8F46F81EFFDA1062F472631FD6DC9\",signature=\"ZpnfZT243qi2gIANY2a3yzcZnz1rXFX7usGvXYqhm+938BTQRKVA8Vrlvpsdng9K/25lxtH4yQTjYgFWFm9txdgzBXvUHSdZmCdrWnT4lserdehXXqGASbkhkv129IMekzNWeNkCVDEQyQAcf/faVAK+ALbb5Ez4zj+LDN5KkjYF2fzCOzTlIWm2xLJK+xaVOEb13qKcBaJPnRKlLmlc/f7m4gCe6Kw0MG5qq8K56yzyOHThug8v4ydXBSU3bwWYOqf2+b8rv0xoaq2MI3gEss4WBHtN6OLdFWttv+GtKXBVQ2zIy6/AsYRvUJ7VOGLpwkKSo+8Zw3PXha5ZDb52/A==\"","Accept":" */*","User-Agent":"WechatPay-Java/0.2.5 (Windows 10/10.0) Java/1.8.0_152 Credential/WechatPay2Credential Validator/ okhttp3/null","Content-Type":"application/json"}}}]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:479)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:550)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 31 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wechat.pay.java.core.RSAAutoCertificateConfig]: Factory method 'jcscWeChatPayConfig' threw exception; nested exception is com.wechat.pay.java.core.exception.ServiceException: Wrong HttpStatusCode[401]
httResponseBody[{"code":"SIGN_ERROR","detail":{"detail":{"issue":"sign not match"},"field":"signature","location":"authorization","sign_information":{"method":"GET","sign_message_length":85,"truncated_sign_message":"GET\n/v3/certificates?algorithm_type=RSA\n1750332972\nbFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\n\n","url":"/v3/certificates?algorithm_type=RSA"}},"message":"错误的签名，验签失败"}]	HttpRequest[{"http_method":"GET","url":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","uri":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","headers":{"headers":{"Authorization":"WECHATPAY2-SHA256-RSA2048 mchid=\"1623108931\",nonce_str=\"bFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\",timestamp=\"1750332972\",serial_no=\"2CDF73C504F8F46F81EFFDA1062F472631FD6DC9\",signature=\"ZpnfZT243qi2gIANY2a3yzcZnz1rXFX7usGvXYqhm+938BTQRKVA8Vrlvpsdng9K/25lxtH4yQTjYgFWFm9txdgzBXvUHSdZmCdrWnT4lserdehXXqGASbkhkv129IMekzNWeNkCVDEQyQAcf/faVAK+ALbb5Ez4zj+LDN5KkjYF2fzCOzTlIWm2xLJK+xaVOEb13qKcBaJPnRKlLmlc/f7m4gCe6Kw0MG5qq8K56yzyOHThug8v4ydXBSU3bwWYOqf2+b8rv0xoaq2MI3gEss4WBHtN6OLdFWttv+GtKXBVQ2zIy6/AsYRvUJ7VOGLpwkKSo+8Zw3PXha5ZDb52/A==\"","Accept":" */*","User-Agent":"WechatPay-Java/0.2.5 (Windows 10/10.0) Java/1.8.0_152 Credential/WechatPay2Credential Validator/ okhttp3/null","Content-Type":"application/json"}}}]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 47 common frames omitted
Caused by: com.wechat.pay.java.core.exception.ServiceException: Wrong HttpStatusCode[401]
httResponseBody[{"code":"SIGN_ERROR","detail":{"detail":{"issue":"sign not match"},"field":"signature","location":"authorization","sign_information":{"method":"GET","sign_message_length":85,"truncated_sign_message":"GET\n/v3/certificates?algorithm_type=RSA\n1750332972\nbFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\n\n","url":"/v3/certificates?algorithm_type=RSA"}},"message":"错误的签名，验签失败"}]	HttpRequest[{"http_method":"GET","url":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","uri":"https://api.mch.weixin.qq.com/v3/certificates?algorithm_type=RSA","headers":{"headers":{"Authorization":"WECHATPAY2-SHA256-RSA2048 mchid=\"1623108931\",nonce_str=\"bFZA7jWjs7vAKV8cheSK90IUVr6eLYsk\",timestamp=\"1750332972\",serial_no=\"2CDF73C504F8F46F81EFFDA1062F472631FD6DC9\",signature=\"ZpnfZT243qi2gIANY2a3yzcZnz1rXFX7usGvXYqhm+938BTQRKVA8Vrlvpsdng9K/25lxtH4yQTjYgFWFm9txdgzBXvUHSdZmCdrWnT4lserdehXXqGASbkhkv129IMekzNWeNkCVDEQyQAcf/faVAK+ALbb5Ez4zj+LDN5KkjYF2fzCOzTlIWm2xLJK+xaVOEb13qKcBaJPnRKlLmlc/f7m4gCe6Kw0MG5qq8K56yzyOHThug8v4ydXBSU3bwWYOqf2+b8rv0xoaq2MI3gEss4WBHtN6OLdFWttv+GtKXBVQ2zIy6/AsYRvUJ7VOGLpwkKSo+8Zw3PXha5ZDb52/A==\"","Accept":" */*","User-Agent":"WechatPay-Java/0.2.5 (Windows 10/10.0) Java/1.8.0_152 Credential/WechatPay2Credential Validator/ okhttp3/null","Content-Type":"application/json"}}}]
	at com.wechat.pay.java.core.http.AbstractHttpClient.validateResponse(AbstractHttpClient.java:74)
	at com.wechat.pay.java.core.http.AbstractHttpClient.execute(AbstractHttpClient.java:45)
	at com.wechat.pay.java.core.certificate.AbstractAutoCertificateProvider.downloadCertificate(AbstractAutoCertificateProvider.java:116)
	at com.wechat.pay.java.core.certificate.AbstractAutoCertificateProvider.downloadAndUpdate(AbstractAutoCertificateProvider.java:93)
	at com.wechat.pay.java.core.certificate.AbstractAutoCertificateProvider.<init>(AbstractAutoCertificateProvider.java:76)
	at com.wechat.pay.java.core.certificate.RSAAutoCertificateProvider.<init>(RSAAutoCertificateProvider.java:31)
	at com.wechat.pay.java.core.certificate.RSAAutoCertificateProvider.<init>(RSAAutoCertificateProvider.java:20)
	at com.wechat.pay.java.core.certificate.RSAAutoCertificateProvider$Builder.build(RSAAutoCertificateProvider.java:100)
	at com.wechat.pay.java.core.RSAAutoCertificateConfig$Builder.build(RSAAutoCertificateConfig.java:105)
	at com.ruoyi.biz.shop.config.WechatPayJcscConfig.jcscWeChatPayConfig(WechatPayJcscConfig.java:33)
	at com.ruoyi.biz.shop.config.WechatPayJcscConfig$$EnhancerBySpringCGLIB$$2ef0dce7.CGLIB$jcscWeChatPayConfig$0(<generated>)
	at com.ruoyi.biz.shop.config.WechatPayJcscConfig$$EnhancerBySpringCGLIB$$2ef0dce7$$FastClassBySpringCGLIB$$8bdf3008.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.biz.shop.config.WechatPayJcscConfig$$EnhancerBySpringCGLIB$$2ef0dce7.jcscWeChatPayConfig(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 48 common frames omitted
19:37:35.783 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.wechat.pay.java.service.payments.nativepay.NativePayService' that could not be found.


Action:

Consider defining a bean of type 'com.wechat.pay.java.service.payments.nativepay.NativePayService' in your configuration.

19:39:04.115 [main] ERROR c.w.p.c.a.h.SignatureExec - [executeWithSignature,95] - 应答的状态码不为200-299。status code[401]	request headers[[Accept: application/json; charset=UTF-8, Authorization: WECHATPAY2-SHA256-RSA2048 mchid="1623108931",nonce_str="aZduNDpeelLi84GaCeKarKF5hlEQ53qh",timestamp="1750333143",serial_no="2CDF73C504F8F46F81EFFDA1062F472631FD6DC9",signature="s5IUQDZ+EvTHl2VufK2SrX+WVlDjFZw8QFzbk850R3nuCgxBgPtip76Abw/LC7u4tySp+4Lm0w1HV7ORvByaZ/ay8kcP+dfiXz7DP5QulOOo2/wTS7QkENwsACVrD6PepHuvzWmmN+ZKEMf51NjjXsqXqeQVZwql2aY993p/KRNBHwHXcaHXlkvD/O3fDb96cFhRh758z0XNWY01fD8wMBL2EOW/WZTmBPc2YI4n8zRCu3DZz4Hx3Idng+Qny6Dlrd5KAn6aKjXSlSxTj58KeOkH23R8L4jH/1joztXUTja2Nf50SbGLbQMB5DKPmYbldj59PnaTdudBuhMchXHraw==", Host: api.mch.weixin.qq.com, Connection: Keep-Alive, User-Agent: WechatPay-Apache-HttpClient/0.4.8 (Windows 10/10.0) Java/1.8.0_152, Accept-Encoding: gzip,deflate]]
