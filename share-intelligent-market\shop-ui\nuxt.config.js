import config from "./utils/config";
export default {
  ssr: false,
  head: {
    title: "黔东南州工业互联网-供应链协同平台",
    htmlAttrs: {
      lang: "en",
    },
    meta: [
      {
        charset: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      {
        hid: "description",
        name: "description",
        content: "",
      },
      {
        name: "format-detection",
        content: "telephone=no",
      },
    ],
    link: [
      {
        rel: "icon",
        type: "image/x-icon",
        href: "./logo.svg",
      },
    ],
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    "element-ui/lib/theme-chalk/index.css",
    {
      src: "~assets/styles/index.scss",
      lang: "scss",
    },
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    "@/plugins/element-ui",
    {
      src: "@/plugins/main.js",
    },
    "~plugins/echarts",
    "@/plugins/axios.js",
    "@/plugins/permission.js",
    { src: "@/plugins/vue-seamless-scroll", ssr: false },
    {
      src: "@/plugins/statistics.prod.js",
      ssr: false,
    },
  ],

  components: true,

  buildModules: [],

  modules: ["@nuxtjs/axios", "@nuxtjs/proxy"],

  // Axios配置
  axios: {
    proxy: true
  },

  // 代理配置
  proxy: {
    '/shop/': {
      target: 'http://localhost:8097',
      changeOrigin: true,
      pathRewrite: {
        '^/shop/': '/shop/'
      }
    }
  },

  router: {
    middleware: "unknownRoute",
    base: "/chain/"
  },

  build: {
    transpile: [/^element-ui/],
    vendor: ["axios"],
  },
  // nitro: {
  //   devProxy: {
  //     "/shop": {
  //       target: 'http://localhost:8097',
  //       changeOrigin: true
  //     }
  //   }
  // }
};
