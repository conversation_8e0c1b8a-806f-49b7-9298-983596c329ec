09:14:55.364 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:14:56.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0
09:14:56.365 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:56.403 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:56.415 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:56.611 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 192 ms to scan 274 urls, producing 0 keys and 0 values 
09:14:56.620 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:56.633 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:56.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:56.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 162 ms to scan 274 urls, producing 0 keys and 0 values 
09:14:56.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:56.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1193939374
09:14:56.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/231182885
09:14:56.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:56.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:56.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:14:58.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295698051_127.0.0.1_50348
09:14:58.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Notify connected event to listeners.
09:14:58.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:58.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4449408-d000-4c1b-b40e-063ae99e5fe6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1628252344
09:14:58.427 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:15:01.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
09:15:01.775 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:15:01.775 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:15:02.051 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:03.504 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
09:15:03.507 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
09:15:03.508 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:15:10.018 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:10.587 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 272ebfcd-5dce-4979-a3f9-e1faf99ba2ae
09:15:10.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] RpcClient init label, labels = {module=naming, source=sdk}
09:15:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:10.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:10.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:10.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:10.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295710599_127.0.0.1_50512
09:15:10.718 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Notify connected event to listeners.
09:15:10.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:10.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1628252344
09:15:13.323 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
09:15:13.370 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
09:15:13.629 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 18.969 seconds (JVM running for 20.492)
09:15:13.657 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
09:15:13.658 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
09:15:13.659 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
09:15:13.943 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:13.947 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:15:13.952 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272ebfcd-5dce-4979-a3f9-e1faf99ba2ae] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:17:47.491 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:17:52.115 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0
09:17:52.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 88 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:52.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:52.378 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:52.738 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 355 ms to scan 274 urls, producing 0 keys and 0 values 
09:17:52.755 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:52.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:52.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:53.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 348 ms to scan 274 urls, producing 0 keys and 0 values 
09:17:53.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:53.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1998103567
09:17:53.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/824915171
09:17:53.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:53.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:53.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:17:57.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:00.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:03.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:03.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:03.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1504912697
09:18:04.679 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:18:09.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:12.497 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:12.951 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
09:18:12.953 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:12.953 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:18:13.411 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:14.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:15.438 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
09:18:15.445 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
09:18:15.445 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:17.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:19.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:22.655 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:25.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:28.518 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:31.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:32.099 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:34.643 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:35.824 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1497764c-596c-4666-844c-92f63b255f40
09:18:35.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] RpcClient init label, labels = {module=naming, source=sdk}
09:18:35.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:35.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:35.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:35.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:37.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:37.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:40.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:41.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:42.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:42.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:42.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1504912697
09:18:45.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:49.146 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:49.562 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:50.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:50.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:51.046 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Success to connect a server [127.0.0.1:8848], connectionId = 1750295930694_127.0.0.1_54343
09:18:51.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Notify connected event to listeners.
09:18:51.797 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750295931655_127.0.0.1_54350
09:18:51.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3fa7f20-719d-4078-88f5-6c51d38b5ff8_config-0] Notify connected event to listeners.
09:18:55.588 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
09:18:55.783 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:18:55.785 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1497764c-596c-4666-844c-92f63b255f40] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:18:55.946 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:18:55.946 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@279e9c95[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:18:55.946 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750295930694_127.0.0.1_54343
09:18:55.947 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5546a9e0[Running, pool size = 19, active threads = 0, queued tasks = 0, completed tasks = 27]
09:18:55.954 [grpc-nio-worker-ELG-4-12] INFO  c.a.n.s.i.g.i.AbstractClientStream - [inboundTrailersReceived,391] - Received trailers on closed stream:
 Metadata()
 {2}
09:18:56.323 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
09:18:56.334 [main] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
09:18:56.351 [main] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
09:18:56.352 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
09:18:56.354 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9701"]
09:18:56.354 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:18:56.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9701"]
09:18:56.367 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9701"]
09:19:51.153 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:19:52.311 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d0506fc1-02db-496b-8e63-aec9656964d9_config-0
09:19:52.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:52.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:52.437 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:52.639 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 199 ms to scan 274 urls, producing 0 keys and 0 values 
09:19:52.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:52.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:52.684 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:52.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 207 ms to scan 274 urls, producing 0 keys and 0 values 
09:19:52.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:52.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1640832113
09:19:52.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1730465260
09:19:52.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:52.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:52.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:54.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295994253_127.0.0.1_55001
09:19:54.534 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Notify connected event to listeners.
09:19:54.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:54.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0506fc1-02db-496b-8e63-aec9656964d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/266894813
09:19:54.646 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:19:58.106 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
09:19:58.107 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:58.107 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:19:58.401 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:59.893 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
09:19:59.898 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
09:19:59.898 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:20:06.271 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:20:06.827 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ed0cf44-2459-4867-8c2b-0c3ac18a73f2
09:20:06.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] RpcClient init label, labels = {module=naming, source=sdk}
09:20:06.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:20:06.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:20:06.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:20:06.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:20:06.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296006838_127.0.0.1_55180
09:20:06.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Notify connected event to listeners.
09:20:06.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:06.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/266894813
09:20:09.502 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
09:20:10.009 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:20:10.010 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed0cf44-2459-4867-8c2b-0c3ac18a73f2] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:20:10.162 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:20:10.162 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25767bd9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:20:10.162 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750296006838_127.0.0.1_55180
09:20:10.164 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750296006838_127.0.0.1_55180]Ignore complete event,isRunning:false,isAbandon=false
09:20:10.174 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18f37d6e[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 7]
09:20:10.335 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
09:20:10.337 [main] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
09:20:10.347 [main] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
09:20:10.347 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
09:20:10.349 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9701"]
09:20:10.349 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:20:10.356 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9701"]
09:20:10.357 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9701"]
09:21:41.241 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:21:42.207 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0
09:21:42.286 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:42.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:42.331 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:42.527 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 194 ms to scan 274 urls, producing 0 keys and 0 values 
09:21:42.538 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:42.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:42.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:42.752 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 187 ms to scan 274 urls, producing 0 keys and 0 values 
09:21:42.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:42.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/556662073
09:21:42.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/170778406
09:21:42.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:42.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:42.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:44.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296104056_127.0.0.1_55990
09:21:44.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Notify connected event to listeners.
09:21:44.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:44.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6b6327b-bfeb-410f-8e4a-9a2af54c9af2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/789640755
09:21:44.443 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:21:47.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
09:21:47.829 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:47.830 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:21:48.139 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:49.721 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
09:21:49.726 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
09:21:49.726 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:21:57.254 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:57.882 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f4c4a38-2381-4df1-9f1b-c6622256e27f
09:21:57.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] RpcClient init label, labels = {module=naming, source=sdk}
09:21:57.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:57.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:57.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:57.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:58.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296117895_127.0.0.1_56170
09:21:58.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Notify connected event to listeners.
09:21:58.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:58.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/789640755
09:22:00.688 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
09:22:00.732 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
09:22:01.005 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 20.469 seconds (JVM running for 21.984)
09:22:01.032 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
09:22:01.032 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
09:22:01.035 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
09:22:01.246 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:22:01.251 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f4c4a38-2381-4df1-9f1b-c6622256e27f] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:22:01.502 [RMI TCP Connection(9)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:27:43.820 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:27:43.822 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:27:44.150 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:27:44.150 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@60142258[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:27:44.150 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750296117895_127.0.0.1_56170
11:27:44.151 [nacos-grpc-client-executor-1525] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750296117895_127.0.0.1_56170]Ignore complete event,isRunning:false,isAbandon=false
11:27:44.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@19cce7b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1526]
11:27:44.317 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
11:27:44.319 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
11:27:44.344 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
11:27:44.346 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
11:52:59.594 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:53:01.246 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0
11:53:01.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 79 ms to scan 1 urls, producing 3 keys and 6 values 
11:53:01.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
11:53:01.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
11:53:01.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 321 ms to scan 274 urls, producing 0 keys and 0 values 
11:53:01.823 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
11:53:01.849 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
11:53:01.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
11:53:02.184 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 309 ms to scan 274 urls, producing 0 keys and 0 values 
11:53:02.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:02.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1903051549
11:53:02.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/2121551683
11:53:02.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:02.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:02.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:04.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305184401_127.0.0.1_53363
11:53:04.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Notify connected event to listeners.
11:53:04.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:04.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8dd17a1e-61fc-45b7-8d19-83983fb91f05_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1879000767
11:53:04.923 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:53:09.556 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
11:53:09.557 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:53:09.557 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:53:09.936 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:53:11.804 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
11:53:11.808 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
11:53:11.809 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:53:19.306 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:53:19.983 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 56e44805-e179-4e25-a73b-857f7508f036
11:53:19.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] RpcClient init label, labels = {module=naming, source=sdk}
11:53:19.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:53:19.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:53:19.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:53:19.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:20.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305199996_127.0.0.1_53810
11:53:20.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Notify connected event to listeners.
11:53:20.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:20.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1879000767
11:53:23.140 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
11:53:23.191 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
11:53:23.517 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 25.097 seconds (JVM running for 26.893)
11:53:23.555 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
11:53:23.556 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
11:53:23.557 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
11:53:23.631 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Receive server push request, request = NotifySubscriberRequest, requestId = 41
11:53:23.636 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56e44805-e179-4e25-a73b-857f7508f036] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:53:23.870 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:38:53.067 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:53.069 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:53.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:53.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@233d40e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:53.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305199996_127.0.0.1_53810
13:38:53.407 [nacos-grpc-client-executor-1276] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305199996_127.0.0.1_53810]Ignore complete event,isRunning:false,isAbandon=false
13:38:53.433 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@705f558a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1277]
13:38:53.622 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
13:38:53.623 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:38:53.630 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:38:53.631 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
13:39:03.473 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
13:39:05.105 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0
13:39:05.229 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 76 ms to scan 1 urls, producing 3 keys and 6 values 
13:39:05.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
13:39:05.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
13:39:05.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 319 ms to scan 274 urls, producing 0 keys and 0 values 
13:39:05.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
13:39:05.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
13:39:05.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
13:39:05.972 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 285 ms to scan 274 urls, producing 0 keys and 0 values 
13:39:05.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:39:05.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/792195577
13:39:05.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/863286716
13:39:05.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:39:05.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:39:06.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:08.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311548252_127.0.0.1_56439
13:39:08.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Notify connected event to listeners.
13:39:08.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:08.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4a8822a-f6a5-49a5-9deb-91dec0db4a82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1537227906
13:39:08.853 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
13:39:13.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
13:39:13.851 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:39:13.851 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
13:39:14.220 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:39:15.934 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
13:39:15.939 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
13:39:15.940 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:39:23.901 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:39:24.737 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1b8634b-b0fd-46c1-ad75-b81db951224c
13:39:24.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] RpcClient init label, labels = {module=naming, source=sdk}
13:39:24.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:39:24.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:39:24.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:39:24.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:39:24.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750311564758_127.0.0.1_56832
13:39:24.864 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Notify connected event to listeners.
13:39:24.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:24.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1537227906
13:39:28.905 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
13:39:28.977 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
13:39:29.347 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 26.979 seconds (JVM running for 28.744)
13:39:29.382 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
13:39:29.383 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
13:39:29.385 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
13:39:29.483 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Receive server push request, request = NotifySubscriberRequest, requestId = 65
13:39:29.487 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1b8634b-b0fd-46c1-ad75-b81db951224c] Ack server push request, request = NotifySubscriberRequest, requestId = 65
13:39:30.278 [RMI TCP Connection(6)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:02:00.068 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:02:00.070 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:02:00.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:02:00.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1949b0d5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:02:00.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750311564758_127.0.0.1_56832
14:02:00.409 [nacos-grpc-client-executor-280] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750311564758_127.0.0.1_56832]Ignore complete event,isRunning:false,isAbandon=false
14:02:00.421 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@199c4096[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 281]
14:02:00.594 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
14:02:00.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
14:02:00.615 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
14:02:00.615 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
14:02:06.840 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
14:02:08.169 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 02a89461-bd83-4182-8599-f9d1942dde2e_config-0
14:02:08.272 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:02:08.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
14:02:08.338 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:02:08.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 225 ms to scan 274 urls, producing 0 keys and 0 values 
14:02:08.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
14:02:08.599 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
14:02:08.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:02:08.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 232 ms to scan 274 urls, producing 0 keys and 0 values 
14:02:08.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:02:08.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1566311673
14:02:08.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1867108691
14:02:08.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:02:08.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:02:08.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:10.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312930394_127.0.0.1_60700
14:02:10.697 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Notify connected event to listeners.
14:02:10.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:10.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02a89461-bd83-4182-8599-f9d1942dde2e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1667348377
14:02:10.830 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
14:02:15.193 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
14:02:15.194 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:02:15.195 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
14:02:15.570 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:02:17.325 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
14:02:17.331 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
14:02:17.332 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:02:24.682 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:02:25.319 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b1a4493-f03f-4848-9813-a1f509847fe5
14:02:25.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] RpcClient init label, labels = {module=naming, source=sdk}
14:02:25.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:02:25.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:02:25.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:02:25.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:02:25.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750312945333_127.0.0.1_60945
14:02:25.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Notify connected event to listeners.
14:02:25.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:02:25.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1667348377
14:02:28.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
14:02:28.602 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
14:02:28.906 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 23.126 seconds (JVM running for 24.999)
14:02:28.942 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
14:02:28.942 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
14:02:28.944 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
14:02:29.135 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Receive server push request, request = NotifySubscriberRequest, requestId = 76
14:02:29.139 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b1a4493-f03f-4848-9813-a1f509847fe5] Ack server push request, request = NotifySubscriberRequest, requestId = 76
14:02:29.433 [RMI TCP Connection(2)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:39:58.991 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:58.993 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@13377b23[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:59.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750312945333_127.0.0.1_60945
16:39:59.320 [nacos-grpc-client-executor-1899] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750312945333_127.0.0.1_60945]Ignore complete event,isRunning:false,isAbandon=false
16:39:59.331 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3a5332a3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1900]
16:39:59.505 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
16:39:59.508 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
16:39:59.526 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
16:39:59.526 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
16:40:08.760 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:40:10.003 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0
16:40:10.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:10.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:10.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:10.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 213 ms to scan 274 urls, producing 0 keys and 0 values 
16:40:10.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:10.396 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:10.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:10.610 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 197 ms to scan 274 urls, producing 0 keys and 0 values 
16:40:10.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:10.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1332150216
16:40:10.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/306889029
16:40:10.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:10.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:12.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322412064_127.0.0.1_50466
16:40:12.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Notify connected event to listeners.
16:40:12.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:12.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe205fef-4bbc-4e3d-b445-7faf49ef2a3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1998224723
16:40:12.462 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:40:17.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
16:40:17.054 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:40:17.054 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
16:40:17.387 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:40:18.938 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
16:40:18.940 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
16:40:18.941 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:40:26.610 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:40:27.265 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 508efb81-afae-4b1c-a544-50c846e80d2d
16:40:27.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] RpcClient init label, labels = {module=naming, source=sdk}
16:40:27.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:40:27.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:40:27.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:40:27.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:27.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322427283_127.0.0.1_50618
16:40:27.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Notify connected event to listeners.
16:40:27.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:27.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1998224723
16:40:30.612 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
16:40:30.664 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
16:40:30.954 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 23.199 seconds (JVM running for 24.91)
16:40:30.990 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
16:40:30.991 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
16:40:30.992 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
16:40:31.178 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Receive server push request, request = NotifySubscriberRequest, requestId = 112
16:40:31.182 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [508efb81-afae-4b1c-a544-50c846e80d2d] Ack server push request, request = NotifySubscriberRequest, requestId = 112
16:40:31.627 [RMI TCP Connection(7)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
