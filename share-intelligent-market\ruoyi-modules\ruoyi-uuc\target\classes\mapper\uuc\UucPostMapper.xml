<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.uuc.ningmengdou.mapper.UucPostMapper">
    
    <resultMap type="UucPost" id="UucPostResult">
        <result property="postId"    column="post_id"    />
        <result property="postCode"    column="post_code"    />
        <result property="postName"    column="post_name"    />
        <result property="postNum"    column="post_num"    />
        <result property="postDuty"    column="post_duty"    />
        <result property="postSatisfy"    column="post_satisfy"    />
        <result property="postSort"    column="post_sort"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUucPostVo">
        select post_id, post_code, post_name, post_num, post_duty, post_satisfy, post_sort, status, remark, create_by, create_time, update_by, update_time from uuc_post
    </sql>

    <select id="selectUucPostList" parameterType="UucPost" resultMap="UucPostResult">
        <include refid="selectUucPostVo"/>
        <where>  
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by post_id desc
    </select>

    <select id="selectUucAppPostList" parameterType="UucPost" resultMap="UucPostResult">
        <include refid="selectUucPostVo"/>
        <where>
             and status = 1
        </where>
        order by post_sort desc
    </select>

    <select id="selectUucPostByPostId" parameterType="Long" resultMap="UucPostResult">
        <include refid="selectUucPostVo"/>
        where post_id = #{postId}
    </select>
        
    <insert id="insertUucPost" parameterType="UucPost" useGeneratedKeys="true" keyProperty="postId">
        insert into uuc_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">post_code,</if>
            <if test="postName != null and postName != ''">post_name,</if>
            <if test="postNum != null">post_num,</if>
            <if test="postDuty != null and postDuty != ''">post_duty,</if>
            <if test="postSatisfy != null and postSatisfy != ''">post_satisfy,</if>
            <if test="postSort != null">post_sort,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">#{postCode},</if>
            <if test="postName != null and postName != ''">#{postName},</if>
            <if test="postNum != null">#{postNum},</if>
            <if test="postDuty != null and postDuty != ''">#{postDuty},</if>
            <if test="postSatisfy != null and postSatisfy != ''">#{postSatisfy},</if>
            <if test="postSort != null">#{postSort},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUucPost" parameterType="UucPost">
        update uuc_post
        <trim prefix="SET" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">post_code = #{postCode},</if>
            <if test="postName != null and postName != ''">post_name = #{postName},</if>
            <if test="postNum != null">post_num = #{postNum},</if>
            <if test="postDuty != null and postDuty != ''">post_duty = #{postDuty},</if>
            <if test="postSatisfy != null and postSatisfy != ''">post_satisfy = #{postSatisfy},</if>
            <if test="postSort != null">post_sort = #{postSort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where post_id = #{postId}
    </update>

    <delete id="deleteUucPostByPostId" parameterType="Long">
        delete from uuc_post where post_id = #{postId}
    </delete>

    <delete id="deleteUucPostByPostIds" parameterType="String">
        delete from uuc_post where post_id in 
        <foreach item="postId" collection="array" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>
</mapper>