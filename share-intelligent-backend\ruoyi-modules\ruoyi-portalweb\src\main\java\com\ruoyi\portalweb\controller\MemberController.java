package com.ruoyi.portalweb.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.system.api.domain.TalentInfoApi;
import com.ruoyi.system.api.domain.key.SsoKey;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.model.LoginMember;
import com.ruoyi.portalweb.service.IMemberService;


/**
 * 会员Controller
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/Member")
@Api(value = "4.会员", tags = "4.会员")
public class MemberController extends BaseController
{
    @Autowired
    private IMemberService memberService;
    @Autowired
    private RedisService redisService;

    /**
     * 查询会员列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询会员列表", notes = "传入")
    public TableDataInfo list(Member member) {
        List<MemberVO> list = memberService.selectMemberList(member);
        return getDataTable(list);
    }

    /**
     * 导出会员列表
     */
    @Log(title = "会员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出会员列表", notes = "传入")
    public void export(HttpServletResponse response, Member member) {
        List<MemberVO> list = memberService.selectMemberList(member);
        ExcelUtil<MemberVO> util = new ExcelUtil<>(MemberVO.class);
        util.exportExcel(response, list, "会员数据");
    }

    /**
     * 获取会员详细信息
     */
    @GetMapping(value = "/{memberId}")
    @ApiOperation(value = "获取会员详细信息", notes = "传入")
    public AjaxResult getInfo(@ApiParam(value = "主键", required = true) @PathVariable("memberId") Long memberId) {
        return success(memberService.selectMemberByMemberId(memberId));
    }

    /**
     * 新增会员
     */
    @Log(title = "会员", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增会员", notes = "传入")
    public AjaxResult add(@RequestBody Member member) {
        return toAjax(memberService.insertMember(member));
    }

    /**
     * 修改会员
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改会员", notes = "传入")
    public AjaxResult edit(@RequestBody Member member) {
        memberService.updateMemberInfo(member);
        MemberVO resMem = memberService.selectMemberByMemberId(member.getMemberId());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("member", resMem);
        return ajax;
    }

    /**
     * 审核会员
     */
    @PostMapping("/quit")
    @ApiOperation(value = "退出企业", notes = "传入")
    public AjaxResult quit(@RequestBody Member member)
    {
        return toAjax(memberService.quitCompanyRelated());
    }

    /**
     * 移除会员
     */
    @PostMapping("/remove/{memberIds}")
    @ApiOperation(value = "退出企业", notes = "传入")
    public AjaxResult removeMemberFromCompanyRelated(@PathVariable List<Long> memberIds)
    {
         memberService.removeMemberFromCompanyRelated(memberIds);
         return AjaxResult.success();
    }


    /**
     * 审核会员
     */
    @PostMapping("/audit")
    @ApiOperation(value = "审核会员", notes = "传入")
    public AjaxResult audit(@RequestBody Member member)
    {
        return toAjax(memberService.auditMember(member));
    }

    /**
     * 删除会员
     */
    @Log(title = "会员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{memberIds}")
    @ApiOperation(value = "删除会员", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] memberIds)
    {
        return toAjax(memberService.deleteMemberByMemberIds(memberIds));
    }

    /**
     * 获取我的企业信息
     */
    @GetMapping("/getMyCompany")
    @ApiOperation(value = "获取我的企业信息", notes = "传入")
    public AjaxResult getMyCompany() {
        return success(memberService.getMyCompany());
    }

    /**
     * 获取我的关联企业信息
     */
    @GetMapping("/getMyCompanyRelated")
    @ApiOperation(value = "获取我的关联企业信息", notes = "传入")
    public AjaxResult getMyCompanyRelated() {
        return success(memberService.getMyCompanyRelated());
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info/{memberphone}")
    @ApiOperation(value = "获取当前用户信息", notes = "传入")
    public R<LoginMember> info(@PathVariable("memberphone") String memberphone)
    {
    	LoginMember loginMember = memberService.selectMemberByPhone(memberphone);
        if (StringUtils.isNull(loginMember))
        {
            return R.fail("用户名或密码错误");
        }

        return R.ok(loginMember);
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/smsCodeInfo/{memberphone}")
    @ApiOperation(value = "获取当前用户信息", notes = "传入")
    public R<LoginMember> smsCodeInfo(@PathVariable("memberphone") String memberphone)
    {
    	LoginMember loginMember = memberService.selectMemberBySmsCode(memberphone);
        if (StringUtils.isNull(loginMember))
        {
            return R.fail("用户名或密码错误");
        }

        return R.ok(loginMember);
    }

    /**
     * 检查验证码
     */
    @PostMapping("/checkUserSmsCode/{phone}/{smsCode}")
    public R<Boolean> checkUserSmsCode(@PathVariable("phone") String phone,
                                       @PathVariable("smsCode") String smsCode)
    {
        String code = redisService.getCacheMapValue(SsoKey.getCode.getPrefix(), phone);
        if (StringUtils.isBlank(code) || !code.equalsIgnoreCase(smsCode)) {
            return R.fail();
        }
        redisService.deleteCacheMapValue(SsoKey.getCode.getPrefix(), phone);

        return R.ok();
    }

    /**
     * 检查验证码
     */
    @PutMapping("/password")
    public R<Boolean> updateMemberPassword(@RequestBody Member member)
    {
        int i = memberService.updateMemberPassword(member);
        if (i!= 1) {
            return R.fail();
        }
        return R.ok();
    }


    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    @ApiOperation(value = "注册用户信息", notes = "传入")
    public R<LoginMember> register(@RequestBody Member member)
    {
    	LoginMember isExistsMember = memberService.selectMemberByPhone(member.getMemberPhone());
    	if(isExistsMember == null){
    		isExistsMember = memberService.registerMember(member);
    	}
        return R.ok(isExistsMember);
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getMemberInfo")
    public AjaxResult getMemberInfo()
    {
        System.out.println("=== getMemberInfo 方法被调用 ===");
        Long userId = SecurityUtils.getUserId();
        System.out.println("当前用户ID: " + userId);

        MemberVO member = memberService.selectMemberByMemberId(userId);
        TalentInfoApi talentInfo = memberService.selectTalentInfoByUserId(userId);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("member", member);
        ajax.put("talentInfo", talentInfo);
        return ajax;
    }

    /**
     * 获取用户ticket
     *
     * @return 用户信息
     */
    @GetMapping("getTicket")
    public R<String> getTicket()
    {
        return R.ok(SecurityUtils.getUserKey());
    }

    /**
     * 企业认证
     */
    @PostMapping("/addCompany")
    @ApiOperation(value = "企业认证", notes = "传入")
    public AjaxResult addCompany(@RequestBody CompanyVO company)
    {
        return toAjax(memberService.saveCompany(company));
    }
}
